test_all_types:{optional_int32:1001  optional_int64:1002  optional_uint32:1003  optional_uint64:1004  optional_sint32:1005  optional_sint64:1006  optional_fixed32:1007  optional_fixed64:1008  optional_sfixed32:1009  optional_sfixed64:1010  optional_float:1011.5  optional_double:1012.5  optional_bool:true  optional_string:"string"  optional_bytes:"bytes"  OptionalGroup:{a:1017}  optional_nested_message:{a:42  corecursive:{optional_int32:43}}  optional_nested_enum:BAR  repeated_int32:1001  repeated_int32:2001  repeated_int64:1002  repeated_int64:2002  repeated_uint32:1003  repeated_uint32:2003  repeated_uint64:1004  repeated_uint64:2004  repeated_sint32:1005  repeated_sint32:2005  repeated_sint64:1006  repeated_sint64:2006  repeated_fixed32:1007  repeated_fixed32:2007  repeated_fixed64:1008  repeated_fixed64:2008  repeated_sfixed32:1009  repeated_sfixed32:2009  repeated_sfixed64:1010  repeated_sfixed64:2010  repeated_float:1011.5  repeated_float:2011.5  repeated_double:1012.5  repeated_double:2012.5  repeated_bool:true  repeated_bool:false  repeated_string:"foo"  repeated_string:"bar"  repeated_bytes:"FOO"  repeated_bytes:"BAR"  RepeatedGroup:{a:1017}  RepeatedGroup:{}  RepeatedGroup:{a:2017}  repeated_nested_message:{a:1}  repeated_nested_message:{}  repeated_nested_message:{a:2}  repeated_nested_enum:FOO  repeated_nested_enum:BAR  map_int32_int32:{key:1056  value:1156}  map_int32_int32:{key:2056  value:2156}  map_int64_int64:{key:1057  value:1157}  map_int64_int64:{key:2057  value:2157}  map_uint32_uint32:{key:1058  value:1158}  map_uint32_uint32:{key:2058  value:2158}  map_uint64_uint64:{key:1059  value:1159}  map_uint64_uint64:{key:2059  value:2159}  map_sint32_sint32:{key:1060  value:1160}  map_sint32_sint32:{key:2060  value:2160}  map_sint64_sint64:{key:1061  value:1161}  map_sint64_sint64:{key:2061  value:2161}  map_fixed32_fixed32:{key:1062  value:1162}  map_fixed32_fixed32:{key:2062  value:2162}  map_fixed64_fixed64:{key:1063  value:1163}  map_fixed64_fixed64:{key:2063  value:2163}  map_sfixed32_sfixed32:{key:1064  value:1164}  map_sfixed32_sfixed32:{key:2064  value:2164}  map_sfixed64_sfixed64:{key:1065  value:1165}  map_sfixed64_sfixed64:{key:2065  value:2165}  map_int32_float:{key:1066  value:1166.5}  map_int32_float:{key:2066  value:2166.5}  map_int32_double:{key:1067  value:1167.5}  map_int32_double:{key:2067  value:2167.5}  map_bool_bool:{key:false  value:true}  map_bool_bool:{key:true  value:false}  map_string_string:{key:"69.1.key"  value:"69.1.val"}  map_string_string:{key:"69.2.key"  value:"69.2.val"}  map_string_bytes:{key:"70.1.key"  value:"70.1.val"}  map_string_bytes:{key:"70.2.key"  value:"70.2.val"}  map_string_nested_message:{key:"71.1.key"  value:{a:1171}}  map_string_nested_message:{key:"71.2.key"  value:{a:2171}}  map_string_nested_enum:{key:"73.1.key"  value:FOO}  map_string_nested_enum:{key:"73.2.key"  value:BAR}  oneof_uint32:1111}