// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

edition = "2023";

package goproto.proto.test;

import "internal/testprotos/race/message/test.proto";

option go_package = "google.golang.org/protobuf/internal/testprotos/race/extender";

message OtherMessage {
  int32 i32 = 1;
}

extend MyMessage {
  string s = 2;
}
