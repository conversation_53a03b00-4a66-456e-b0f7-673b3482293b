{"testAllTypes": {"optionalInt32": 1001, "optionalInt64": "1002", "optionalUint32": 1003, "optionalUint64": "1004", "optionalSint32": 1005, "optionalSint64": "1006", "optionalFixed32": 1007, "optionalFixed64": "1008", "optionalSfixed32": 1009, "optionalSfixed64": "1010", "optionalFloat": 1011.5, "optionalDouble": 1012.5, "optionalBool": true, "optionalString": "string", "optionalBytes": "Ynl0ZXM=", "optionalgroup": {"a": 1017}, "optionalNestedMessage": {"a": 42, "corecursive": {"optionalInt32": 43}}, "optionalNestedEnum": "BAR", "repeatedInt32": [1001, 2001], "repeatedInt64": ["1002", "2002"], "repeatedUint32": [1003, 2003], "repeatedUint64": ["1004", "2004"], "repeatedSint32": [1005, 2005], "repeatedSint64": ["1006", "2006"], "repeatedFixed32": [1007, 2007], "repeatedFixed64": ["1008", "2008"], "repeatedSfixed32": [1009, 2009], "repeatedSfixed64": ["1010", "2010"], "repeatedFloat": [1011.5, 2011.5], "repeatedDouble": [1012.5, 2012.5], "repeatedBool": [true, false], "repeatedString": ["foo", "bar"], "repeatedBytes": ["Rk9P", "QkFS"], "repeatedgroup": [{"a": 1017}, {}, {"a": 2017}], "repeatedNestedMessage": [{"a": 1}, {}, {"a": 2}], "repeatedNestedEnum": ["FOO", "BAR"], "mapInt32Int32": {"1056": 1156, "2056": 2156}, "mapInt64Int64": {"1057": "1157", "2057": "2157"}, "mapUint32Uint32": {"1058": 1158, "2058": 2158}, "mapUint64Uint64": {"1059": "1159", "2059": "2159"}, "mapSint32Sint32": {"1060": 1160, "2060": 2160}, "mapSint64Sint64": {"1061": "1161", "2061": "2161"}, "mapFixed32Fixed32": {"1062": 1162, "2062": 2162}, "mapFixed64Fixed64": {"1063": "1163", "2063": "2163"}, "mapSfixed32Sfixed32": {"1064": 1164, "2064": 2164}, "mapSfixed64Sfixed64": {"1065": "1165", "2065": "2165"}, "mapInt32Float": {"1066": 1166.5, "2066": 2166.5}, "mapInt32Double": {"1067": 1167.5, "2067": 2167.5}, "mapBoolBool": {"false": true, "true": false}, "mapStringString": {"69.1.key": "69.1.val", "69.2.key": "69.2.val"}, "mapStringBytes": {"70.1.key": "NzAuMS52YWw=", "70.2.key": "NzAuMi52YWw="}, "mapStringNestedMessage": {"71.1.key": {"a": 1171}, "71.2.key": {"a": 2171}}, "mapStringNestedEnum": {"73.1.key": "FOO", "73.2.key": "BAR"}, "oneofUint32": 1111}}