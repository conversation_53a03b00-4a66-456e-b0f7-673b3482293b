name: CI

on:
  push:
    branches: [master]
    tags: ['v*']
  pull_request:
    branches: ['*']

permissions:
  contents: read

jobs:

  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go: ["1.20.x", "1.21.x"]

    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go }}

    - name: Test
      run: make cover

    - name: Upload coverage to codecov.io
      uses: codecov/codecov-action@v3

  lint:
    name: Lint
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      name: Check out repository
    - uses: actions/setup-go@v4
      name: Set up Go
      with:
        go-version: 1.21.x
        cache: false  # managed by golangci-lint

    - uses: golangci/golangci-lint-action@v3
      name: Install golangci-lint
      with:
        version: latest
        args: --version  # make lint will run the linter

    - run: make lint
      name: Lint
