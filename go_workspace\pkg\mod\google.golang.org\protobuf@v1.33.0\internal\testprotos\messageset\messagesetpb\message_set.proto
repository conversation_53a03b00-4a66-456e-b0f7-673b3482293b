// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

syntax = "proto2";

package goproto.proto.messageset;

option go_package = "google.golang.org/protobuf/internal/testprotos/messageset/messagesetpb";

message MessageSet {
  option message_set_wire_format = true;

  extensions 4 to max;
}

message MessageSetContainer {
  optional MessageSet message_set = 1;
}
