name: goreleaser

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write # for goreleaser/goreleaser-action to create a GitHub release

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
      -
        name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.21.x
      -
        name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v2
        with:
          version: 1.20.0
          args: release --rm-dist
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CGO_ENABLED: 0
