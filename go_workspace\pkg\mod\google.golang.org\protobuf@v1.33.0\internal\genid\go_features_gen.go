// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_reflect_protodesc_proto_go_features_proto = "reflect/protodesc/proto/go_features.proto"

// Names for google.protobuf.GoFeatures.
const (
	GoFeatures_message_name     protoreflect.Name     = "GoFeatures"
	GoFeatures_message_fullname protoreflect.FullName = "google.protobuf.GoFeatures"
)

// Field names for google.protobuf.GoFeatures.
const (
	GoFeatures_LegacyUnmarshalJsonEnum_field_name protoreflect.Name = "legacy_unmarshal_json_enum"

	GoFeatures_LegacyUnmarshalJsonEnum_field_fullname protoreflect.FullName = "google.protobuf.GoFeatures.legacy_unmarshal_json_enum"
)

// Field numbers for google.protobuf.GoFeatures.
const (
	GoFeatures_LegacyUnmarshalJsonEnum_field_number protoreflect.FieldNumber = 1
)
