// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto2";

package google.protobuf;

import "google/protobuf/descriptor.proto";

option go_package = "google.golang.org/protobuf/types/gofeaturespb";

extend google.protobuf.FeatureSet {
  optional GoFeatures go = 1002;
}

message GoFeatures {
  // Whether or not to generate the deprecated UnmarshalJSON method for enums.
  optional bool legacy_unmarshal_json_enum = 1 [
    retention = RETENTION_RUNTIME,
    targets = TARGET_TYPE_ENUM,
    edition_defaults = { edition: EDITION_PROTO2, value: "true" },
    edition_defaults = { edition: EDITION_PROTO3, value: "false" }
  ];
}
