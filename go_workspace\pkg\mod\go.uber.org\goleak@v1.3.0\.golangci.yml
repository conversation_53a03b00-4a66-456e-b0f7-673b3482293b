output:
  # Make output more digestible with quickfix in vim/emacs/etc.
  sort-results: true
  print-issued-lines: false

linters:
  enable:
    - gofumpt
    - nolintlint
    - revive

linters-settings:
  govet:
    # These govet checks are disabled by default, but they're useful.
    enable:
      - niliness
      - reflectvaluecompare
      - sortslice
      - unusedwrite

issues:
  # Print all issues reported by all linters.
  max-issues-per-linter: 0
  max-same-issues: 0

  # Don't ignore some of the issues that golangci-lint considers okay.
  # This includes documenting all exported entities.
  exclude-use-default: false
