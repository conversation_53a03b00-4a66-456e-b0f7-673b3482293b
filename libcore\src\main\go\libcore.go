package libcore

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"sync"
	"time"

	"github.com/sagernet/sing-box"
	"github.com/sagernet/sing-box/option"
)

// BoxInstance 代表一个sing-box实例
type BoxInstance struct {
	instance *box.Box
	cancel   context.CancelFunc
	running  bool
	mutex    sync.RWMutex
	workDir  string
}

// NewBoxInstance 创建新的BoxInstance
func NewBoxInstance() *BoxInstance {
	return &BoxInstance{
		running: false,
		workDir: "/data/data/com.example.singboxandroid/files",
	}
}

// SetWorkDir 设置工作目录
func (b *BoxInstance) SetWorkDir(dir string) {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	b.workDir = dir
}

// Start 启动sing-box实例
func (b *BoxInstance) Start(configContent string) error {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	
	if b.running {
		return fmt.Errorf("box instance is already running")
	}

	// 确保工作目录存在
	if err := os.MkdirAll(b.workDir, 0755); err != nil {
		return fmt.Errorf("failed to create work directory: %w", err)
	}

	// 解析配置
	var options option.Options
	err := json.Unmarshal([]byte(configContent), &options)
	if err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// 创建context
	ctx, cancel := context.WithCancel(context.Background())
	b.cancel = cancel

	// 创建sing-box实例
	instance, err := box.New(box.Options{
		Context: ctx,
		Options: options,
	})
	if err != nil {
		cancel()
		return fmt.Errorf("failed to create box instance: %w", err)
	}

	// 启动实例
	err = instance.Start()
	if err != nil {
		cancel()
		return fmt.Errorf("failed to start box instance: %w", err)
	}

	b.instance = instance
	b.running = true

	log.Println("sing-box instance started successfully")
	return nil
}

// Stop 停止sing-box实例
func (b *BoxInstance) Stop() error {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	
	if !b.running {
		return nil
	}

	if b.instance != nil {
		err := b.instance.Close()
		if err != nil {
			log.Printf("Error closing box instance: %v", err)
		}
	}

	if b.cancel != nil {
		b.cancel()
	}

	b.instance = nil
	b.cancel = nil
	b.running = false

	log.Println("sing-box instance stopped")
	return nil
}

// IsRunning 检查实例是否正在运行
func (b *BoxInstance) IsRunning() bool {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return b.running
}

// GetSocksPort 获取SOCKS代理端口
func (b *BoxInstance) GetSocksPort() int {
	// 默认SOCKS端口，实际应该从配置中获取
	return 1080
}

// GetHttpPort 获取HTTP代理端口
func (b *BoxInstance) GetHttpPort() int {
	// 默认HTTP端口，实际应该从配置中获取
	return 1081
}

// GenerateConfig 生成sing-box配置
func GenerateConfig(serverAddr, serverPort, password, method string) string {
	return GenerateConfigWithType("shadowsocks", serverAddr, serverPort, password, method, "", "", "")
}

// GenerateConfigWithType 根据类型生成配置
func GenerateConfigWithType(proxyType, serverAddr, serverPort, password, method, uuid, path, host string) string {
	config := map[string]interface{}{
		"log": map[string]interface{}{
			"level": "info",
		},
		"inbounds": []map[string]interface{}{
			{
				"type":        "mixed",
				"tag":         "mixed-in",
				"listen":      "127.0.0.1",
				"listen_port": 1080,
			},
			{
				"type":        "tun",
				"tag":         "tun-in",
				"interface_name": "tun0",
				"inet4_address": "**********/30",
				"mtu":         9000,
				"auto_route":  true,
				"strict_route": true,
				"stack":       "system",
			},
		},
		"outbounds": []map[string]interface{}{},
		"route": map[string]interface{}{
			"rules": []map[string]interface{}{
				{
					"geoip":    []string{"private"},
					"outbound": "direct",
				},
				{
					"geoip":    []string{"cn"},
					"outbound": "direct",
				},
			},
			"final": "proxy",
		},
	}

	// 根据代理类型创建outbound
	var proxyOutbound map[string]interface{}
	
	switch proxyType {
	case "shadowsocks":
		proxyOutbound = map[string]interface{}{
			"type":        "shadowsocks",
			"tag":         "proxy",
			"server":      serverAddr,
			"server_port": serverPort,
			"method":      method,
			"password":    password,
		}
	case "vmess":
		proxyOutbound = map[string]interface{}{
			"type":        "vmess",
			"tag":         "proxy",
			"server":      serverAddr,
			"server_port": serverPort,
			"uuid":        uuid,
			"security":    "auto",
		}
		if path != "" {
			proxyOutbound["transport"] = map[string]interface{}{
				"type": "ws",
				"path": path,
			}
			if host != "" {
				proxyOutbound["transport"].(map[string]interface{})["headers"] = map[string]interface{}{
					"Host": host,
				}
			}
		}
	case "vless":
		proxyOutbound = map[string]interface{}{
			"type":        "vless",
			"tag":         "proxy",
			"server":      serverAddr,
			"server_port": serverPort,
			"uuid":        uuid,
		}
		if path != "" {
			proxyOutbound["transport"] = map[string]interface{}{
				"type": "ws",
				"path": path,
			}
			if host != "" {
				proxyOutbound["transport"].(map[string]interface{})["headers"] = map[string]interface{}{
					"Host": host,
				}
			}
		}
	case "trojan":
		proxyOutbound = map[string]interface{}{
			"type":        "trojan",
			"tag":         "proxy",
			"server":      serverAddr,
			"server_port": serverPort,
			"password":    password,
		}
		if path != "" {
			proxyOutbound["transport"] = map[string]interface{}{
				"type": "ws",
				"path": path,
			}
			if host != "" {
				proxyOutbound["transport"].(map[string]interface{})["headers"] = map[string]interface{}{
					"Host": host,
				}
			}
		}
	default:
		// 默认使用shadowsocks
		proxyOutbound = map[string]interface{}{
			"type":        "shadowsocks",
			"tag":         "proxy",
			"server":      serverAddr,
			"server_port": serverPort,
			"method":      method,
			"password":    password,
		}
	}

	// 添加outbounds
	outbounds := []map[string]interface{}{
		proxyOutbound,
		{
			"type": "direct",
			"tag":  "direct",
		},
		{
			"type": "block",
			"tag":  "block",
		},
	}
	
	config["outbounds"] = outbounds

	configBytes, _ := json.MarshalIndent(config, "", "  ")
	return string(configBytes)
}

// TestConnection 测试连接
func TestConnection(serverAddr string, serverPort int, timeout int) bool {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", serverAddr, serverPort), time.Duration(timeout)*time.Second)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// GetVersion 获取版本信息
func GetVersion() string {
	return "1.0.0-libcore"
}

// 全局实例
var (
	globalInstance *BoxInstance
	globalMutex    sync.RWMutex
)

func init() {
	globalInstance = NewBoxInstance()
}

// SetWorkingDirectory 设置全局工作目录
func SetWorkingDirectory(dir string) {
	globalMutex.Lock()
	defer globalMutex.Unlock()
	globalInstance.SetWorkDir(dir)
}

// StartBox 启动全局sing-box实例
func StartBox(configContent string) error {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalInstance.Start(configContent)
}

// StopBox 停止全局sing-box实例
func StopBox() error {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalInstance.Stop()
}

// IsBoxRunning 检查全局实例是否运行
func IsBoxRunning() bool {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalInstance.IsRunning()
}

// GetBoxSocksPort 获取全局实例SOCKS端口
func GetBoxSocksPort() int {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalInstance.GetSocksPort()
}

// GetBoxHttpPort 获取全局实例HTTP端口
func GetBoxHttpPort() int {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalInstance.GetHttpPort()
}

// GenerateDefaultConfig 生成默认配置
func GenerateDefaultConfig() string {
	return GenerateConfig("127.0.0.1", "1080", "password", "aes-256-gcm")
}

// Cleanup 清理资源
func Cleanup() error {
	globalMutex.Lock()
	defer globalMutex.Unlock()
	return globalInstance.Stop()
}