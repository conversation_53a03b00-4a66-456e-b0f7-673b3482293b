// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cmd/protoc-gen-go/testdata/import_public/b.proto

package import_public

import (
	sub "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/import_public/sub"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type Local struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	M *sub.M `protobuf:"bytes,1,opt,name=m" json:"m,omitempty"`
	E *sub.E `protobuf:"varint,2,opt,name=e,enum=goproto.protoc.import_public.sub.E" json:"e,omitempty"`
}

func (x *Local) Reset() {
	*x = Local{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_import_public_b_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Local) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Local) ProtoMessage() {}

func (x *Local) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_import_public_b_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Local.ProtoReflect.Descriptor instead.
func (*Local) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescGZIP(), []int{0}
}

func (x *Local) GetM() *sub.M {
	if x != nil {
		return x.M
	}
	return nil
}

func (x *Local) GetE() sub.E {
	if x != nil && x.E != nil {
		return *x.E
	}
	return sub.E(0)
}

var File_cmd_protoc_gen_go_testdata_import_public_b_proto protoreflect.FileDescriptor

var file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDesc = []byte{
	0x0a, 0x30, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2f, 0x62, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x2e, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x1a, 0x34, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2f, 0x73, 0x75, 0x62, 0x2f, 0x61,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6d, 0x0a, 0x05, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x12,
	0x31, 0x0a, 0x01, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x73, 0x75, 0x62, 0x2e, 0x4d, 0x52,
	0x01, 0x6d, 0x12, 0x31, 0x0a, 0x01, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x73, 0x75, 0x62,
	0x2e, 0x45, 0x52, 0x01, 0x65, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67,
	0x65, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
}

var (
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescOnce sync.Once
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescData = file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDesc
)

func file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescGZIP() []byte {
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescOnce.Do(func() {
		file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescData = protoimpl.X.CompressGZIP(file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescData)
	})
	return file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDescData
}

var file_cmd_protoc_gen_go_testdata_import_public_b_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_cmd_protoc_gen_go_testdata_import_public_b_proto_goTypes = []interface{}{
	(*Local)(nil), // 0: goproto.protoc.import_public.Local
	(*sub.M)(nil), // 1: goproto.protoc.import_public.sub.M
	(sub.E)(0),    // 2: goproto.protoc.import_public.sub.E
}
var file_cmd_protoc_gen_go_testdata_import_public_b_proto_depIdxs = []int32{
	1, // 0: goproto.protoc.import_public.Local.m:type_name -> goproto.protoc.import_public.sub.M
	2, // 1: goproto.protoc.import_public.Local.e:type_name -> goproto.protoc.import_public.sub.E
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_cmd_protoc_gen_go_testdata_import_public_b_proto_init() }
func file_cmd_protoc_gen_go_testdata_import_public_b_proto_init() {
	if File_cmd_protoc_gen_go_testdata_import_public_b_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cmd_protoc_gen_go_testdata_import_public_b_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Local); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_protoc_gen_go_testdata_import_public_b_proto_goTypes,
		DependencyIndexes: file_cmd_protoc_gen_go_testdata_import_public_b_proto_depIdxs,
		MessageInfos:      file_cmd_protoc_gen_go_testdata_import_public_b_proto_msgTypes,
	}.Build()
	File_cmd_protoc_gen_go_testdata_import_public_b_proto = out.File
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_rawDesc = nil
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_goTypes = nil
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_depIdxs = nil
}
