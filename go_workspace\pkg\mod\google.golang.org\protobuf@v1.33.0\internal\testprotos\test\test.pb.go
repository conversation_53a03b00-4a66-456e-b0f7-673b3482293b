// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/test/test.proto

package test

import (
	enums "google.golang.org/protobuf/internal/testprotos/enums"
	proto "google.golang.org/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type ForeignEnum int32

const (
	ForeignEnum_FOREIGN_FOO ForeignEnum = 4
	ForeignEnum_FOREIGN_BAR ForeignEnum = 5
	ForeignEnum_FOREIGN_BAZ ForeignEnum = 6
)

// Enum value maps for ForeignEnum.
var (
	ForeignEnum_name = map[int32]string{
		4: "FOREIGN_FOO",
		5: "FOREIGN_BAR",
		6: "FOREIGN_BAZ",
	}
	ForeignEnum_value = map[string]int32{
		"FOREIGN_FOO": 4,
		"FOREIGN_BAR": 5,
		"FOREIGN_BAZ": 6,
	}
)

func (x ForeignEnum) Enum() *ForeignEnum {
	p := new(ForeignEnum)
	*p = x
	return p
}

func (x ForeignEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForeignEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_test_test_proto_enumTypes[0].Descriptor()
}

func (ForeignEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_test_test_proto_enumTypes[0]
}

func (x ForeignEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ForeignEnum) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ForeignEnum(num)
	return nil
}

// Deprecated: Use ForeignEnum.Descriptor instead.
func (ForeignEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0}
}

type TestReservedEnumFields int32

const (
	TestReservedEnumFields_RESERVED_ENUM TestReservedEnumFields = 0
)

// Enum value maps for TestReservedEnumFields.
var (
	TestReservedEnumFields_name = map[int32]string{
		0: "RESERVED_ENUM",
	}
	TestReservedEnumFields_value = map[string]int32{
		"RESERVED_ENUM": 0,
	}
)

func (x TestReservedEnumFields) Enum() *TestReservedEnumFields {
	p := new(TestReservedEnumFields)
	*p = x
	return p
}

func (x TestReservedEnumFields) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestReservedEnumFields) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_test_test_proto_enumTypes[1].Descriptor()
}

func (TestReservedEnumFields) Type() protoreflect.EnumType {
	return &file_internal_testprotos_test_test_proto_enumTypes[1]
}

func (x TestReservedEnumFields) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TestReservedEnumFields) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TestReservedEnumFields(num)
	return nil
}

// Deprecated: Use TestReservedEnumFields.Descriptor instead.
func (TestReservedEnumFields) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{1}
}

type TestAllTypes_NestedEnum int32

const (
	TestAllTypes_FOO TestAllTypes_NestedEnum = 0
	TestAllTypes_BAR TestAllTypes_NestedEnum = 1
	TestAllTypes_BAZ TestAllTypes_NestedEnum = 2
	TestAllTypes_NEG TestAllTypes_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllTypes_NestedEnum.
var (
	TestAllTypes_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllTypes_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllTypes_NestedEnum) Enum() *TestAllTypes_NestedEnum {
	p := new(TestAllTypes_NestedEnum)
	*p = x
	return p
}

func (x TestAllTypes_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypes_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_test_test_proto_enumTypes[2].Descriptor()
}

func (TestAllTypes_NestedEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_test_test_proto_enumTypes[2]
}

func (x TestAllTypes_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TestAllTypes_NestedEnum) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TestAllTypes_NestedEnum(num)
	return nil
}

// Deprecated: Use TestAllTypes_NestedEnum.Descriptor instead.
func (TestAllTypes_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0, 0}
}

// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
type TestDeprecatedMessage_DeprecatedEnum int32

const (
	// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
	TestDeprecatedMessage_DEPRECATED TestDeprecatedMessage_DeprecatedEnum = 0
)

// Enum value maps for TestDeprecatedMessage_DeprecatedEnum.
var (
	TestDeprecatedMessage_DeprecatedEnum_name = map[int32]string{
		0: "DEPRECATED",
	}
	TestDeprecatedMessage_DeprecatedEnum_value = map[string]int32{
		"DEPRECATED": 0,
	}
)

func (x TestDeprecatedMessage_DeprecatedEnum) Enum() *TestDeprecatedMessage_DeprecatedEnum {
	p := new(TestDeprecatedMessage_DeprecatedEnum)
	*p = x
	return p
}

func (x TestDeprecatedMessage_DeprecatedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestDeprecatedMessage_DeprecatedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_test_test_proto_enumTypes[3].Descriptor()
}

func (TestDeprecatedMessage_DeprecatedEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_test_test_proto_enumTypes[3]
}

func (x TestDeprecatedMessage_DeprecatedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TestDeprecatedMessage_DeprecatedEnum) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TestDeprecatedMessage_DeprecatedEnum(num)
	return nil
}

// Deprecated: Use TestDeprecatedMessage_DeprecatedEnum.Descriptor instead.
func (TestDeprecatedMessage_DeprecatedEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{1, 0}
}

type TestAllTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionalInt32          *int32                                 `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalInt64          *int64                                 `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalUint32         *uint32                                `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalUint64         *uint64                                `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalSint32         *int32                                 `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalSint64         *int64                                 `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalFixed32        *uint32                                `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalFixed64        *uint64                                `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed32       *int32                                 `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalSfixed64       *int64                                 `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalFloat          *float32                               `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalDouble         *float64                               `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalBool           *bool                                  `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalString         *string                                `protobuf:"bytes,14,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                                 `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	Optionalgroup          *TestAllTypes_OptionalGroup            `protobuf:"group,16,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	OptionalNestedMessage  *TestAllTypes_NestedMessage            `protobuf:"bytes,18,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	OptionalForeignMessage *ForeignMessage                        `protobuf:"bytes,19,opt,name=optional_foreign_message,json=optionalForeignMessage" json:"optional_foreign_message,omitempty"`
	OptionalImportMessage  *ImportMessage                         `protobuf:"bytes,20,opt,name=optional_import_message,json=optionalImportMessage" json:"optional_import_message,omitempty"`
	OptionalNestedEnum     *TestAllTypes_NestedEnum               `protobuf:"varint,21,opt,name=optional_nested_enum,json=optionalNestedEnum,enum=goproto.proto.test.TestAllTypes_NestedEnum" json:"optional_nested_enum,omitempty"`
	OptionalForeignEnum    *ForeignEnum                           `protobuf:"varint,22,opt,name=optional_foreign_enum,json=optionalForeignEnum,enum=goproto.proto.test.ForeignEnum" json:"optional_foreign_enum,omitempty"`
	OptionalImportEnum     *ImportEnum                            `protobuf:"varint,23,opt,name=optional_import_enum,json=optionalImportEnum,enum=goproto.proto.test.ImportEnum" json:"optional_import_enum,omitempty"`
	RepeatedInt32          []int32                                `protobuf:"varint,31,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedInt64          []int64                                `protobuf:"varint,32,rep,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedUint32         []uint32                               `protobuf:"varint,33,rep,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedUint64         []uint64                               `protobuf:"varint,34,rep,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedSint32         []int32                                `protobuf:"zigzag32,35,rep,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedSint64         []int64                                `protobuf:"zigzag64,36,rep,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedFixed32        []uint32                               `protobuf:"fixed32,37,rep,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedFixed64        []uint64                               `protobuf:"fixed64,38,rep,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed32       []int32                                `protobuf:"fixed32,39,rep,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedSfixed64       []int64                                `protobuf:"fixed64,40,rep,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedFloat          []float32                              `protobuf:"fixed32,41,rep,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedDouble         []float64                              `protobuf:"fixed64,42,rep,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedBool           []bool                                 `protobuf:"varint,43,rep,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedString         []string                               `protobuf:"bytes,44,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                               `protobuf:"bytes,45,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	Repeatedgroup          []*TestAllTypes_RepeatedGroup          `protobuf:"group,46,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
	RepeatedNestedMessage  []*TestAllTypes_NestedMessage          `protobuf:"bytes,48,rep,name=repeated_nested_message,json=repeatedNestedMessage" json:"repeated_nested_message,omitempty"`
	RepeatedForeignMessage []*ForeignMessage                      `protobuf:"bytes,49,rep,name=repeated_foreign_message,json=repeatedForeignMessage" json:"repeated_foreign_message,omitempty"`
	RepeatedImportmessage  []*ImportMessage                       `protobuf:"bytes,50,rep,name=repeated_importmessage,json=repeatedImportmessage" json:"repeated_importmessage,omitempty"`
	RepeatedNestedEnum     []TestAllTypes_NestedEnum              `protobuf:"varint,51,rep,name=repeated_nested_enum,json=repeatedNestedEnum,enum=goproto.proto.test.TestAllTypes_NestedEnum" json:"repeated_nested_enum,omitempty"`
	RepeatedForeignEnum    []ForeignEnum                          `protobuf:"varint,52,rep,name=repeated_foreign_enum,json=repeatedForeignEnum,enum=goproto.proto.test.ForeignEnum" json:"repeated_foreign_enum,omitempty"`
	RepeatedImportenum     []ImportEnum                           `protobuf:"varint,53,rep,name=repeated_importenum,json=repeatedImportenum,enum=goproto.proto.test.ImportEnum" json:"repeated_importenum,omitempty"`
	MapInt32Int32          map[int32]int32                        `protobuf:"bytes,56,rep,name=map_int32_int32,json=mapInt32Int32" json:"map_int32_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Int64          map[int64]int64                        `protobuf:"bytes,57,rep,name=map_int64_int64,json=mapInt64Int64" json:"map_int64_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Uint32        map[uint32]uint32                      `protobuf:"bytes,58,rep,name=map_uint32_uint32,json=mapUint32Uint32" json:"map_uint32_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Uint64        map[uint64]uint64                      `protobuf:"bytes,59,rep,name=map_uint64_uint64,json=mapUint64Uint64" json:"map_uint64_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Sint32        map[int32]int32                        `protobuf:"bytes,60,rep,name=map_sint32_sint32,json=mapSint32Sint32" json:"map_sint32_sint32,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapSint64Sint64        map[int64]int64                        `protobuf:"bytes,61,rep,name=map_sint64_sint64,json=mapSint64Sint64" json:"map_sint64_sint64,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapFixed32Fixed32      map[uint32]uint32                      `protobuf:"bytes,62,rep,name=map_fixed32_fixed32,json=mapFixed32Fixed32" json:"map_fixed32_fixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapFixed64Fixed64      map[uint64]uint64                      `protobuf:"bytes,63,rep,name=map_fixed64_fixed64,json=mapFixed64Fixed64" json:"map_fixed64_fixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapSfixed32Sfixed32    map[int32]int32                        `protobuf:"bytes,64,rep,name=map_sfixed32_sfixed32,json=mapSfixed32Sfixed32" json:"map_sfixed32_sfixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapSfixed64Sfixed64    map[int64]int64                        `protobuf:"bytes,65,rep,name=map_sfixed64_sfixed64,json=mapSfixed64Sfixed64" json:"map_sfixed64_sfixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapInt32Float          map[int32]float32                      `protobuf:"bytes,66,rep,name=map_int32_float,json=mapInt32Float" json:"map_int32_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapInt32Double         map[int32]float64                      `protobuf:"bytes,67,rep,name=map_int32_double,json=mapInt32Double" json:"map_int32_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolBool            map[bool]bool                          `protobuf:"bytes,68,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringString        map[string]string                      `protobuf:"bytes,69,rep,name=map_string_string,json=mapStringString" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringBytes         map[string][]byte                      `protobuf:"bytes,70,rep,name=map_string_bytes,json=mapStringBytes" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedMessage map[string]*TestAllTypes_NestedMessage `protobuf:"bytes,71,rep,name=map_string_nested_message,json=mapStringNestedMessage" json:"map_string_nested_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedEnum    map[string]TestAllTypes_NestedEnum     `protobuf:"bytes,73,rep,name=map_string_nested_enum,json=mapStringNestedEnum" json:"map_string_nested_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=goproto.proto.test.TestAllTypes_NestedEnum"`
	// Singular with defaults
	DefaultInt32       *int32                   `protobuf:"varint,81,opt,name=default_int32,json=defaultInt32,def=81" json:"default_int32,omitempty"`
	DefaultInt64       *int64                   `protobuf:"varint,82,opt,name=default_int64,json=defaultInt64,def=82" json:"default_int64,omitempty"`
	DefaultUint32      *uint32                  `protobuf:"varint,83,opt,name=default_uint32,json=defaultUint32,def=83" json:"default_uint32,omitempty"`
	DefaultUint64      *uint64                  `protobuf:"varint,84,opt,name=default_uint64,json=defaultUint64,def=84" json:"default_uint64,omitempty"`
	DefaultSint32      *int32                   `protobuf:"zigzag32,85,opt,name=default_sint32,json=defaultSint32,def=-85" json:"default_sint32,omitempty"`
	DefaultSint64      *int64                   `protobuf:"zigzag64,86,opt,name=default_sint64,json=defaultSint64,def=86" json:"default_sint64,omitempty"`
	DefaultFixed32     *uint32                  `protobuf:"fixed32,87,opt,name=default_fixed32,json=defaultFixed32,def=87" json:"default_fixed32,omitempty"`
	DefaultFixed64     *uint64                  `protobuf:"fixed64,88,opt,name=default_fixed64,json=defaultFixed64,def=88" json:"default_fixed64,omitempty"`
	DefaultSfixed32    *int32                   `protobuf:"fixed32,89,opt,name=default_sfixed32,json=defaultSfixed32,def=89" json:"default_sfixed32,omitempty"`
	DefaultSfixed64    *int64                   `protobuf:"fixed64,80,opt,name=default_sfixed64,json=defaultSfixed64,def=-90" json:"default_sfixed64,omitempty"`
	DefaultFloat       *float32                 `protobuf:"fixed32,91,opt,name=default_float,json=defaultFloat,def=91.5" json:"default_float,omitempty"`
	DefaultDouble      *float64                 `protobuf:"fixed64,92,opt,name=default_double,json=defaultDouble,def=92000" json:"default_double,omitempty"`
	DefaultBool        *bool                    `protobuf:"varint,93,opt,name=default_bool,json=defaultBool,def=1" json:"default_bool,omitempty"`
	DefaultString      *string                  `protobuf:"bytes,94,opt,name=default_string,json=defaultString,def=hello" json:"default_string,omitempty"`
	DefaultBytes       []byte                   `protobuf:"bytes,95,opt,name=default_bytes,json=defaultBytes,def=world" json:"default_bytes,omitempty"`
	DefaultNestedEnum  *TestAllTypes_NestedEnum `protobuf:"varint,96,opt,name=default_nested_enum,json=defaultNestedEnum,enum=goproto.proto.test.TestAllTypes_NestedEnum,def=1" json:"default_nested_enum,omitempty"`
	DefaultForeignEnum *ForeignEnum             `protobuf:"varint,97,opt,name=default_foreign_enum,json=defaultForeignEnum,enum=goproto.proto.test.ForeignEnum,def=5" json:"default_foreign_enum,omitempty"`
	// Types that are assignable to OneofField:
	//
	//	*TestAllTypes_OneofUint32
	//	*TestAllTypes_OneofNestedMessage
	//	*TestAllTypes_OneofString
	//	*TestAllTypes_OneofBytes
	//	*TestAllTypes_OneofBool
	//	*TestAllTypes_OneofUint64
	//	*TestAllTypes_OneofFloat
	//	*TestAllTypes_OneofDouble
	//	*TestAllTypes_OneofEnum
	//	*TestAllTypes_Oneofgroup
	OneofField isTestAllTypes_OneofField `protobuf_oneof:"oneof_field"`
	// A oneof with exactly one field.
	//
	// Types that are assignable to OneofOptional:
	//
	//	*TestAllTypes_OneofOptionalUint32
	OneofOptional isTestAllTypes_OneofOptional `protobuf_oneof:"oneof_optional"`
}

// Default values for TestAllTypes fields.
const (
	Default_TestAllTypes_DefaultInt32       = int32(81)
	Default_TestAllTypes_DefaultInt64       = int64(82)
	Default_TestAllTypes_DefaultUint32      = uint32(83)
	Default_TestAllTypes_DefaultUint64      = uint64(84)
	Default_TestAllTypes_DefaultSint32      = int32(-85)
	Default_TestAllTypes_DefaultSint64      = int64(86)
	Default_TestAllTypes_DefaultFixed32     = uint32(87)
	Default_TestAllTypes_DefaultFixed64     = uint64(88)
	Default_TestAllTypes_DefaultSfixed32    = int32(89)
	Default_TestAllTypes_DefaultSfixed64    = int64(-90)
	Default_TestAllTypes_DefaultFloat       = float32(91.5)
	Default_TestAllTypes_DefaultDouble      = float64(92000)
	Default_TestAllTypes_DefaultBool        = bool(true)
	Default_TestAllTypes_DefaultString      = string("hello")
	Default_TestAllTypes_DefaultNestedEnum  = TestAllTypes_BAR
	Default_TestAllTypes_DefaultForeignEnum = ForeignEnum_FOREIGN_BAR
)

// Default values for TestAllTypes fields.
var (
	Default_TestAllTypes_DefaultBytes = []byte("world")
)

func (x *TestAllTypes) Reset() {
	*x = TestAllTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes) ProtoMessage() {}

func (x *TestAllTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes.ProtoReflect.Descriptor instead.
func (*TestAllTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0}
}

func (x *TestAllTypes) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

func (x *TestAllTypes) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

func (x *TestAllTypes) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *TestAllTypes) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *TestAllTypes) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *TestAllTypes) GetOptionalgroup() *TestAllTypes_OptionalGroup {
	if x != nil {
		return x.Optionalgroup
	}
	return nil
}

func (x *TestAllTypes) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalForeignMessage() *ForeignMessage {
	if x != nil {
		return x.OptionalForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalImportMessage() *ImportMessage {
	if x != nil {
		return x.OptionalImportMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalNestedEnum() TestAllTypes_NestedEnum {
	if x != nil && x.OptionalNestedEnum != nil {
		return *x.OptionalNestedEnum
	}
	return TestAllTypes_FOO
}

func (x *TestAllTypes) GetOptionalForeignEnum() ForeignEnum {
	if x != nil && x.OptionalForeignEnum != nil {
		return *x.OptionalForeignEnum
	}
	return ForeignEnum_FOREIGN_FOO
}

func (x *TestAllTypes) GetOptionalImportEnum() ImportEnum {
	if x != nil && x.OptionalImportEnum != nil {
		return *x.OptionalImportEnum
	}
	return ImportEnum_IMPORT_ZERO
}

func (x *TestAllTypes) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedgroup() []*TestAllTypes_RepeatedGroup {
	if x != nil {
		return x.Repeatedgroup
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedNestedMessage() []*TestAllTypes_NestedMessage {
	if x != nil {
		return x.RepeatedNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedForeignMessage() []*ForeignMessage {
	if x != nil {
		return x.RepeatedForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedImportmessage() []*ImportMessage {
	if x != nil {
		return x.RepeatedImportmessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedNestedEnum() []TestAllTypes_NestedEnum {
	if x != nil {
		return x.RepeatedNestedEnum
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedForeignEnum() []ForeignEnum {
	if x != nil {
		return x.RepeatedForeignEnum
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedImportenum() []ImportEnum {
	if x != nil {
		return x.RepeatedImportenum
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Int32() map[int32]int32 {
	if x != nil {
		return x.MapInt32Int32
	}
	return nil
}

func (x *TestAllTypes) GetMapInt64Int64() map[int64]int64 {
	if x != nil {
		return x.MapInt64Int64
	}
	return nil
}

func (x *TestAllTypes) GetMapUint32Uint32() map[uint32]uint32 {
	if x != nil {
		return x.MapUint32Uint32
	}
	return nil
}

func (x *TestAllTypes) GetMapUint64Uint64() map[uint64]uint64 {
	if x != nil {
		return x.MapUint64Uint64
	}
	return nil
}

func (x *TestAllTypes) GetMapSint32Sint32() map[int32]int32 {
	if x != nil {
		return x.MapSint32Sint32
	}
	return nil
}

func (x *TestAllTypes) GetMapSint64Sint64() map[int64]int64 {
	if x != nil {
		return x.MapSint64Sint64
	}
	return nil
}

func (x *TestAllTypes) GetMapFixed32Fixed32() map[uint32]uint32 {
	if x != nil {
		return x.MapFixed32Fixed32
	}
	return nil
}

func (x *TestAllTypes) GetMapFixed64Fixed64() map[uint64]uint64 {
	if x != nil {
		return x.MapFixed64Fixed64
	}
	return nil
}

func (x *TestAllTypes) GetMapSfixed32Sfixed32() map[int32]int32 {
	if x != nil {
		return x.MapSfixed32Sfixed32
	}
	return nil
}

func (x *TestAllTypes) GetMapSfixed64Sfixed64() map[int64]int64 {
	if x != nil {
		return x.MapSfixed64Sfixed64
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Float() map[int32]float32 {
	if x != nil {
		return x.MapInt32Float
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Double() map[int32]float64 {
	if x != nil {
		return x.MapInt32Double
	}
	return nil
}

func (x *TestAllTypes) GetMapBoolBool() map[bool]bool {
	if x != nil {
		return x.MapBoolBool
	}
	return nil
}

func (x *TestAllTypes) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

func (x *TestAllTypes) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

func (x *TestAllTypes) GetMapStringNestedMessage() map[string]*TestAllTypes_NestedMessage {
	if x != nil {
		return x.MapStringNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetMapStringNestedEnum() map[string]TestAllTypes_NestedEnum {
	if x != nil {
		return x.MapStringNestedEnum
	}
	return nil
}

func (x *TestAllTypes) GetDefaultInt32() int32 {
	if x != nil && x.DefaultInt32 != nil {
		return *x.DefaultInt32
	}
	return Default_TestAllTypes_DefaultInt32
}

func (x *TestAllTypes) GetDefaultInt64() int64 {
	if x != nil && x.DefaultInt64 != nil {
		return *x.DefaultInt64
	}
	return Default_TestAllTypes_DefaultInt64
}

func (x *TestAllTypes) GetDefaultUint32() uint32 {
	if x != nil && x.DefaultUint32 != nil {
		return *x.DefaultUint32
	}
	return Default_TestAllTypes_DefaultUint32
}

func (x *TestAllTypes) GetDefaultUint64() uint64 {
	if x != nil && x.DefaultUint64 != nil {
		return *x.DefaultUint64
	}
	return Default_TestAllTypes_DefaultUint64
}

func (x *TestAllTypes) GetDefaultSint32() int32 {
	if x != nil && x.DefaultSint32 != nil {
		return *x.DefaultSint32
	}
	return Default_TestAllTypes_DefaultSint32
}

func (x *TestAllTypes) GetDefaultSint64() int64 {
	if x != nil && x.DefaultSint64 != nil {
		return *x.DefaultSint64
	}
	return Default_TestAllTypes_DefaultSint64
}

func (x *TestAllTypes) GetDefaultFixed32() uint32 {
	if x != nil && x.DefaultFixed32 != nil {
		return *x.DefaultFixed32
	}
	return Default_TestAllTypes_DefaultFixed32
}

func (x *TestAllTypes) GetDefaultFixed64() uint64 {
	if x != nil && x.DefaultFixed64 != nil {
		return *x.DefaultFixed64
	}
	return Default_TestAllTypes_DefaultFixed64
}

func (x *TestAllTypes) GetDefaultSfixed32() int32 {
	if x != nil && x.DefaultSfixed32 != nil {
		return *x.DefaultSfixed32
	}
	return Default_TestAllTypes_DefaultSfixed32
}

func (x *TestAllTypes) GetDefaultSfixed64() int64 {
	if x != nil && x.DefaultSfixed64 != nil {
		return *x.DefaultSfixed64
	}
	return Default_TestAllTypes_DefaultSfixed64
}

func (x *TestAllTypes) GetDefaultFloat() float32 {
	if x != nil && x.DefaultFloat != nil {
		return *x.DefaultFloat
	}
	return Default_TestAllTypes_DefaultFloat
}

func (x *TestAllTypes) GetDefaultDouble() float64 {
	if x != nil && x.DefaultDouble != nil {
		return *x.DefaultDouble
	}
	return Default_TestAllTypes_DefaultDouble
}

func (x *TestAllTypes) GetDefaultBool() bool {
	if x != nil && x.DefaultBool != nil {
		return *x.DefaultBool
	}
	return Default_TestAllTypes_DefaultBool
}

func (x *TestAllTypes) GetDefaultString() string {
	if x != nil && x.DefaultString != nil {
		return *x.DefaultString
	}
	return Default_TestAllTypes_DefaultString
}

func (x *TestAllTypes) GetDefaultBytes() []byte {
	if x != nil && x.DefaultBytes != nil {
		return x.DefaultBytes
	}
	return append([]byte(nil), Default_TestAllTypes_DefaultBytes...)
}

func (x *TestAllTypes) GetDefaultNestedEnum() TestAllTypes_NestedEnum {
	if x != nil && x.DefaultNestedEnum != nil {
		return *x.DefaultNestedEnum
	}
	return Default_TestAllTypes_DefaultNestedEnum
}

func (x *TestAllTypes) GetDefaultForeignEnum() ForeignEnum {
	if x != nil && x.DefaultForeignEnum != nil {
		return *x.DefaultForeignEnum
	}
	return Default_TestAllTypes_DefaultForeignEnum
}

func (m *TestAllTypes) GetOneofField() isTestAllTypes_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestAllTypes) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *TestAllTypes) GetOneofNestedMessage() *TestAllTypes_NestedMessage {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofNestedMessage); ok {
		return x.OneofNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetOneofString() string {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *TestAllTypes) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *TestAllTypes) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *TestAllTypes) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *TestAllTypes) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *TestAllTypes) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *TestAllTypes) GetOneofEnum() TestAllTypes_NestedEnum {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofEnum); ok {
		return x.OneofEnum
	}
	return TestAllTypes_FOO
}

func (x *TestAllTypes) GetOneofgroup() *TestAllTypes_OneofGroup {
	if x, ok := x.GetOneofField().(*TestAllTypes_Oneofgroup); ok {
		return x.Oneofgroup
	}
	return nil
}

func (m *TestAllTypes) GetOneofOptional() isTestAllTypes_OneofOptional {
	if m != nil {
		return m.OneofOptional
	}
	return nil
}

func (x *TestAllTypes) GetOneofOptionalUint32() uint32 {
	if x, ok := x.GetOneofOptional().(*TestAllTypes_OneofOptionalUint32); ok {
		return x.OneofOptionalUint32
	}
	return 0
}

type isTestAllTypes_OneofField interface {
	isTestAllTypes_OneofField()
}

type TestAllTypes_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,111,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type TestAllTypes_OneofNestedMessage struct {
	OneofNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,112,opt,name=oneof_nested_message,json=oneofNestedMessage,oneof"`
}

type TestAllTypes_OneofString struct {
	OneofString string `protobuf:"bytes,113,opt,name=oneof_string,json=oneofString,oneof"`
}

type TestAllTypes_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,114,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type TestAllTypes_OneofBool struct {
	OneofBool bool `protobuf:"varint,115,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type TestAllTypes_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,116,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type TestAllTypes_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,117,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type TestAllTypes_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,118,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type TestAllTypes_OneofEnum struct {
	OneofEnum TestAllTypes_NestedEnum `protobuf:"varint,119,opt,name=oneof_enum,json=oneofEnum,enum=goproto.proto.test.TestAllTypes_NestedEnum,oneof"`
}

type TestAllTypes_Oneofgroup struct {
	Oneofgroup *TestAllTypes_OneofGroup `protobuf:"group,121,opt,name=OneofGroup,json=oneofgroup,oneof"`
}

func (*TestAllTypes_OneofUint32) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofNestedMessage) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofString) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofBytes) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofBool) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofUint64) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofFloat) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofDouble) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofEnum) isTestAllTypes_OneofField() {}

func (*TestAllTypes_Oneofgroup) isTestAllTypes_OneofField() {}

type isTestAllTypes_OneofOptional interface {
	isTestAllTypes_OneofOptional()
}

type TestAllTypes_OneofOptionalUint32 struct {
	OneofOptionalUint32 uint32 `protobuf:"varint,120,opt,name=oneof_optional_uint32,json=oneofOptionalUint32,oneof"`
}

func (*TestAllTypes_OneofOptionalUint32) isTestAllTypes_OneofOptional() {}

// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
type TestDeprecatedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
	DeprecatedInt32 *int32 `protobuf:"varint,1,opt,name=deprecated_int32,json=deprecatedInt32" json:"deprecated_int32,omitempty"`
	// Types that are assignable to DeprecatedOneof:
	//
	//	*TestDeprecatedMessage_DeprecatedOneofField
	DeprecatedOneof isTestDeprecatedMessage_DeprecatedOneof `protobuf_oneof:"deprecated_oneof"`
}

func (x *TestDeprecatedMessage) Reset() {
	*x = TestDeprecatedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestDeprecatedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestDeprecatedMessage) ProtoMessage() {}

func (x *TestDeprecatedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestDeprecatedMessage.ProtoReflect.Descriptor instead.
func (*TestDeprecatedMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
func (x *TestDeprecatedMessage) GetDeprecatedInt32() int32 {
	if x != nil && x.DeprecatedInt32 != nil {
		return *x.DeprecatedInt32
	}
	return 0
}

func (m *TestDeprecatedMessage) GetDeprecatedOneof() isTestDeprecatedMessage_DeprecatedOneof {
	if m != nil {
		return m.DeprecatedOneof
	}
	return nil
}

// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
func (x *TestDeprecatedMessage) GetDeprecatedOneofField() int32 {
	if x, ok := x.GetDeprecatedOneof().(*TestDeprecatedMessage_DeprecatedOneofField); ok {
		return x.DeprecatedOneofField
	}
	return 0
}

type isTestDeprecatedMessage_DeprecatedOneof interface {
	isTestDeprecatedMessage_DeprecatedOneof()
}

type TestDeprecatedMessage_DeprecatedOneofField struct {
	// Deprecated: Marked as deprecated in internal/testprotos/test/test.proto.
	DeprecatedOneofField int32 `protobuf:"varint,2,opt,name=deprecated_oneof_field,json=deprecatedOneofField,oneof"`
}

func (*TestDeprecatedMessage_DeprecatedOneofField) isTestDeprecatedMessage_DeprecatedOneof() {}

type ForeignMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"varint,1,opt,name=c" json:"c,omitempty"`
	D *int32 `protobuf:"varint,2,opt,name=d" json:"d,omitempty"`
}

func (x *ForeignMessage) Reset() {
	*x = ForeignMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeignMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeignMessage) ProtoMessage() {}

func (x *ForeignMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeignMessage.ProtoReflect.Descriptor instead.
func (*ForeignMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{2}
}

func (x *ForeignMessage) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

func (x *ForeignMessage) GetD() int32 {
	if x != nil && x.D != nil {
		return *x.D
	}
	return 0
}

type TestReservedFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestReservedFields) Reset() {
	*x = TestReservedFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReservedFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReservedFields) ProtoMessage() {}

func (x *TestReservedFields) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReservedFields.ProtoReflect.Descriptor instead.
func (*TestReservedFields) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{3}
}

type TestAllExtensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestAllExtensions) Reset() {
	*x = TestAllExtensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllExtensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllExtensions) ProtoMessage() {}

func (x *TestAllExtensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllExtensions.ProtoReflect.Descriptor instead.
func (*TestAllExtensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{4}
}

type OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                           `protobuf:"varint,17,opt,name=a" json:"a,omitempty"`
	SameFieldNumber       *int32                           `protobuf:"varint,16,opt,name=same_field_number,json=sameFieldNumber" json:"same_field_number,omitempty"`
	OptionalNestedMessage *TestAllExtensions_NestedMessage `protobuf:"bytes,1000,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
}

func (x *OptionalGroup) Reset() {
	*x = OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionalGroup) ProtoMessage() {}

func (x *OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionalGroup.ProtoReflect.Descriptor instead.
func (*OptionalGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{5}
}

func (x *OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *OptionalGroup) GetSameFieldNumber() int32 {
	if x != nil && x.SameFieldNumber != nil {
		return *x.SameFieldNumber
	}
	return 0
}

func (x *OptionalGroup) GetOptionalNestedMessage() *TestAllExtensions_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

type RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                           `protobuf:"varint,47,opt,name=a" json:"a,omitempty"`
	OptionalNestedMessage *TestAllExtensions_NestedMessage `protobuf:"bytes,1001,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
}

func (x *RepeatedGroup) Reset() {
	*x = RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedGroup) ProtoMessage() {}

func (x *RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedGroup.ProtoReflect.Descriptor instead.
func (*RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{6}
}

func (x *RepeatedGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *RepeatedGroup) GetOptionalNestedMessage() *TestAllExtensions_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

type TestNestedExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestNestedExtension) Reset() {
	*x = TestNestedExtension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestNestedExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestNestedExtension) ProtoMessage() {}

func (x *TestNestedExtension) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestNestedExtension.ProtoReflect.Descriptor instead.
func (*TestNestedExtension) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{7}
}

type TestRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequiredField *int32 `protobuf:"varint,1,req,name=required_field,json=requiredField" json:"required_field,omitempty"`
}

func (x *TestRequired) Reset() {
	*x = TestRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequired) ProtoMessage() {}

func (x *TestRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequired.ProtoReflect.Descriptor instead.
func (*TestRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{8}
}

func (x *TestRequired) GetRequiredField() int32 {
	if x != nil && x.RequiredField != nil {
		return *x.RequiredField
	}
	return 0
}

type TestRequiredForeign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionalMessage *TestRequired           `protobuf:"bytes,1,opt,name=optional_message,json=optionalMessage" json:"optional_message,omitempty"`
	RepeatedMessage []*TestRequired         `protobuf:"bytes,2,rep,name=repeated_message,json=repeatedMessage" json:"repeated_message,omitempty"`
	MapMessage      map[int32]*TestRequired `protobuf:"bytes,3,rep,name=map_message,json=mapMessage" json:"map_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Types that are assignable to OneofField:
	//
	//	*TestRequiredForeign_OneofMessage
	OneofField isTestRequiredForeign_OneofField `protobuf_oneof:"oneof_field"`
}

func (x *TestRequiredForeign) Reset() {
	*x = TestRequiredForeign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredForeign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredForeign) ProtoMessage() {}

func (x *TestRequiredForeign) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredForeign.ProtoReflect.Descriptor instead.
func (*TestRequiredForeign) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{9}
}

func (x *TestRequiredForeign) GetOptionalMessage() *TestRequired {
	if x != nil {
		return x.OptionalMessage
	}
	return nil
}

func (x *TestRequiredForeign) GetRepeatedMessage() []*TestRequired {
	if x != nil {
		return x.RepeatedMessage
	}
	return nil
}

func (x *TestRequiredForeign) GetMapMessage() map[int32]*TestRequired {
	if x != nil {
		return x.MapMessage
	}
	return nil
}

func (m *TestRequiredForeign) GetOneofField() isTestRequiredForeign_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestRequiredForeign) GetOneofMessage() *TestRequired {
	if x, ok := x.GetOneofField().(*TestRequiredForeign_OneofMessage); ok {
		return x.OneofMessage
	}
	return nil
}

type isTestRequiredForeign_OneofField interface {
	isTestRequiredForeign_OneofField()
}

type TestRequiredForeign_OneofMessage struct {
	OneofMessage *TestRequired `protobuf:"bytes,4,opt,name=oneof_message,json=oneofMessage,oneof"`
}

func (*TestRequiredForeign_OneofMessage) isTestRequiredForeign_OneofField() {}

type TestRequiredGroupFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Optionalgroup *TestRequiredGroupFields_OptionalGroup   `protobuf:"group,1,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	Repeatedgroup []*TestRequiredGroupFields_RepeatedGroup `protobuf:"group,3,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
}

func (x *TestRequiredGroupFields) Reset() {
	*x = TestRequiredGroupFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredGroupFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredGroupFields) ProtoMessage() {}

func (x *TestRequiredGroupFields) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredGroupFields.ProtoReflect.Descriptor instead.
func (*TestRequiredGroupFields) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{10}
}

func (x *TestRequiredGroupFields) GetOptionalgroup() *TestRequiredGroupFields_OptionalGroup {
	if x != nil {
		return x.Optionalgroup
	}
	return nil
}

func (x *TestRequiredGroupFields) GetRepeatedgroup() []*TestRequiredGroupFields_RepeatedGroup {
	if x != nil {
		return x.Repeatedgroup
	}
	return nil
}

type TestWeak struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	weakFields    protoimpl.WeakFields
	unknownFields protoimpl.UnknownFields

	XXX_weak_WeakMessage1 struct{} `protobuf:"bytes,1,opt,name=weak_message1,json=weakMessage1,weak=goproto.proto.test.weak.WeakImportMessage1" json:"weak_message1,omitempty"`
	XXX_weak_WeakMessage2 struct{} `protobuf:"bytes,2,opt,name=weak_message2,json=weakMessage2,weak=goproto.proto.test.weak.WeakImportMessage2" json:"weak_message2,omitempty"`
}

func (x *TestWeak) Reset() {
	*x = TestWeak{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestWeak) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestWeak) ProtoMessage() {}

func (x *TestWeak) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestWeak.ProtoReflect.Descriptor instead.
func (*TestWeak) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{11}
}

func (x *TestWeak) GetWeakMessage1() proto.Message {
	var w protoimpl.WeakFields
	if x != nil {
		w = x.weakFields
	}
	return protoimpl.X.GetWeak(w, 1, "goproto.proto.test.weak.WeakImportMessage1")
}

func (x *TestWeak) GetWeakMessage2() proto.Message {
	var w protoimpl.WeakFields
	if x != nil {
		w = x.weakFields
	}
	return protoimpl.X.GetWeak(w, 2, "goproto.proto.test.weak.WeakImportMessage2")
}

func (x *TestWeak) SetWeakMessage1(v proto.Message) {
	var w *protoimpl.WeakFields
	if x != nil {
		w = &x.weakFields
	}
	protoimpl.X.SetWeak(w, 1, "goproto.proto.test.weak.WeakImportMessage1", v)
}

func (x *TestWeak) SetWeakMessage2(v proto.Message) {
	var w *protoimpl.WeakFields
	if x != nil {
		w = &x.weakFields
	}
	protoimpl.X.SetWeak(w, 2, "goproto.proto.test.weak.WeakImportMessage2", v)
}

type TestPackedTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackedInt32    []int32       `protobuf:"varint,90,rep,packed,name=packed_int32,json=packedInt32" json:"packed_int32,omitempty"`
	PackedInt64    []int64       `protobuf:"varint,91,rep,packed,name=packed_int64,json=packedInt64" json:"packed_int64,omitempty"`
	PackedUint32   []uint32      `protobuf:"varint,92,rep,packed,name=packed_uint32,json=packedUint32" json:"packed_uint32,omitempty"`
	PackedUint64   []uint64      `protobuf:"varint,93,rep,packed,name=packed_uint64,json=packedUint64" json:"packed_uint64,omitempty"`
	PackedSint32   []int32       `protobuf:"zigzag32,94,rep,packed,name=packed_sint32,json=packedSint32" json:"packed_sint32,omitempty"`
	PackedSint64   []int64       `protobuf:"zigzag64,95,rep,packed,name=packed_sint64,json=packedSint64" json:"packed_sint64,omitempty"`
	PackedFixed32  []uint32      `protobuf:"fixed32,96,rep,packed,name=packed_fixed32,json=packedFixed32" json:"packed_fixed32,omitempty"`
	PackedFixed64  []uint64      `protobuf:"fixed64,97,rep,packed,name=packed_fixed64,json=packedFixed64" json:"packed_fixed64,omitempty"`
	PackedSfixed32 []int32       `protobuf:"fixed32,98,rep,packed,name=packed_sfixed32,json=packedSfixed32" json:"packed_sfixed32,omitempty"`
	PackedSfixed64 []int64       `protobuf:"fixed64,99,rep,packed,name=packed_sfixed64,json=packedSfixed64" json:"packed_sfixed64,omitempty"`
	PackedFloat    []float32     `protobuf:"fixed32,100,rep,packed,name=packed_float,json=packedFloat" json:"packed_float,omitempty"`
	PackedDouble   []float64     `protobuf:"fixed64,101,rep,packed,name=packed_double,json=packedDouble" json:"packed_double,omitempty"`
	PackedBool     []bool        `protobuf:"varint,102,rep,packed,name=packed_bool,json=packedBool" json:"packed_bool,omitempty"`
	PackedEnum     []ForeignEnum `protobuf:"varint,103,rep,packed,name=packed_enum,json=packedEnum,enum=goproto.proto.test.ForeignEnum" json:"packed_enum,omitempty"`
}

func (x *TestPackedTypes) Reset() {
	*x = TestPackedTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestPackedTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestPackedTypes) ProtoMessage() {}

func (x *TestPackedTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestPackedTypes.ProtoReflect.Descriptor instead.
func (*TestPackedTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{12}
}

func (x *TestPackedTypes) GetPackedInt32() []int32 {
	if x != nil {
		return x.PackedInt32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedInt64() []int64 {
	if x != nil {
		return x.PackedInt64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedUint32() []uint32 {
	if x != nil {
		return x.PackedUint32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedUint64() []uint64 {
	if x != nil {
		return x.PackedUint64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSint32() []int32 {
	if x != nil {
		return x.PackedSint32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSint64() []int64 {
	if x != nil {
		return x.PackedSint64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedFixed32() []uint32 {
	if x != nil {
		return x.PackedFixed32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedFixed64() []uint64 {
	if x != nil {
		return x.PackedFixed64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSfixed32() []int32 {
	if x != nil {
		return x.PackedSfixed32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSfixed64() []int64 {
	if x != nil {
		return x.PackedSfixed64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedFloat() []float32 {
	if x != nil {
		return x.PackedFloat
	}
	return nil
}

func (x *TestPackedTypes) GetPackedDouble() []float64 {
	if x != nil {
		return x.PackedDouble
	}
	return nil
}

func (x *TestPackedTypes) GetPackedBool() []bool {
	if x != nil {
		return x.PackedBool
	}
	return nil
}

func (x *TestPackedTypes) GetPackedEnum() []ForeignEnum {
	if x != nil {
		return x.PackedEnum
	}
	return nil
}

type TestUnpackedTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UnpackedInt32    []int32       `protobuf:"varint,90,rep,name=unpacked_int32,json=unpackedInt32" json:"unpacked_int32,omitempty"`
	UnpackedInt64    []int64       `protobuf:"varint,91,rep,name=unpacked_int64,json=unpackedInt64" json:"unpacked_int64,omitempty"`
	UnpackedUint32   []uint32      `protobuf:"varint,92,rep,name=unpacked_uint32,json=unpackedUint32" json:"unpacked_uint32,omitempty"`
	UnpackedUint64   []uint64      `protobuf:"varint,93,rep,name=unpacked_uint64,json=unpackedUint64" json:"unpacked_uint64,omitempty"`
	UnpackedSint32   []int32       `protobuf:"zigzag32,94,rep,name=unpacked_sint32,json=unpackedSint32" json:"unpacked_sint32,omitempty"`
	UnpackedSint64   []int64       `protobuf:"zigzag64,95,rep,name=unpacked_sint64,json=unpackedSint64" json:"unpacked_sint64,omitempty"`
	UnpackedFixed32  []uint32      `protobuf:"fixed32,96,rep,name=unpacked_fixed32,json=unpackedFixed32" json:"unpacked_fixed32,omitempty"`
	UnpackedFixed64  []uint64      `protobuf:"fixed64,97,rep,name=unpacked_fixed64,json=unpackedFixed64" json:"unpacked_fixed64,omitempty"`
	UnpackedSfixed32 []int32       `protobuf:"fixed32,98,rep,name=unpacked_sfixed32,json=unpackedSfixed32" json:"unpacked_sfixed32,omitempty"`
	UnpackedSfixed64 []int64       `protobuf:"fixed64,99,rep,name=unpacked_sfixed64,json=unpackedSfixed64" json:"unpacked_sfixed64,omitempty"`
	UnpackedFloat    []float32     `protobuf:"fixed32,100,rep,name=unpacked_float,json=unpackedFloat" json:"unpacked_float,omitempty"`
	UnpackedDouble   []float64     `protobuf:"fixed64,101,rep,name=unpacked_double,json=unpackedDouble" json:"unpacked_double,omitempty"`
	UnpackedBool     []bool        `protobuf:"varint,102,rep,name=unpacked_bool,json=unpackedBool" json:"unpacked_bool,omitempty"`
	UnpackedEnum     []ForeignEnum `protobuf:"varint,103,rep,name=unpacked_enum,json=unpackedEnum,enum=goproto.proto.test.ForeignEnum" json:"unpacked_enum,omitempty"`
}

func (x *TestUnpackedTypes) Reset() {
	*x = TestUnpackedTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestUnpackedTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestUnpackedTypes) ProtoMessage() {}

func (x *TestUnpackedTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestUnpackedTypes.ProtoReflect.Descriptor instead.
func (*TestUnpackedTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{13}
}

func (x *TestUnpackedTypes) GetUnpackedInt32() []int32 {
	if x != nil {
		return x.UnpackedInt32
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedInt64() []int64 {
	if x != nil {
		return x.UnpackedInt64
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedUint32() []uint32 {
	if x != nil {
		return x.UnpackedUint32
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedUint64() []uint64 {
	if x != nil {
		return x.UnpackedUint64
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedSint32() []int32 {
	if x != nil {
		return x.UnpackedSint32
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedSint64() []int64 {
	if x != nil {
		return x.UnpackedSint64
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedFixed32() []uint32 {
	if x != nil {
		return x.UnpackedFixed32
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedFixed64() []uint64 {
	if x != nil {
		return x.UnpackedFixed64
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedSfixed32() []int32 {
	if x != nil {
		return x.UnpackedSfixed32
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedSfixed64() []int64 {
	if x != nil {
		return x.UnpackedSfixed64
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedFloat() []float32 {
	if x != nil {
		return x.UnpackedFloat
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedDouble() []float64 {
	if x != nil {
		return x.UnpackedDouble
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedBool() []bool {
	if x != nil {
		return x.UnpackedBool
	}
	return nil
}

func (x *TestUnpackedTypes) GetUnpackedEnum() []ForeignEnum {
	if x != nil {
		return x.UnpackedEnum
	}
	return nil
}

type TestPackedExtensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestPackedExtensions) Reset() {
	*x = TestPackedExtensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestPackedExtensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestPackedExtensions) ProtoMessage() {}

func (x *TestPackedExtensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestPackedExtensions.ProtoReflect.Descriptor instead.
func (*TestPackedExtensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{14}
}

type TestUnpackedExtensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestUnpackedExtensions) Reset() {
	*x = TestUnpackedExtensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestUnpackedExtensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestUnpackedExtensions) ProtoMessage() {}

func (x *TestUnpackedExtensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestUnpackedExtensions.ProtoReflect.Descriptor instead.
func (*TestUnpackedExtensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{15}
}

// Test that RPC services work.
type FooRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FooRequest) Reset() {
	*x = FooRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FooRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FooRequest) ProtoMessage() {}

func (x *FooRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FooRequest.ProtoReflect.Descriptor instead.
func (*FooRequest) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{16}
}

type FooResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FooResponse) Reset() {
	*x = FooResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FooResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FooResponse) ProtoMessage() {}

func (x *FooResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FooResponse.ProtoReflect.Descriptor instead.
func (*FooResponse) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{17}
}

type WeirdDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeirdDefault []byte `protobuf:"bytes,1,opt,name=weird_default,json=weirdDefault,def=hello, \\\"world!\\\"\\ndead\\336\\255\\276\\357beef\x60" json:"weird_default,omitempty"`
}

// Default values for WeirdDefault fields.
var (
	Default_WeirdDefault_WeirdDefault = []byte("hello, \"world!\"\ndeadޭ\xbe\xefbeef`")
)

func (x *WeirdDefault) Reset() {
	*x = WeirdDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeirdDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeirdDefault) ProtoMessage() {}

func (x *WeirdDefault) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeirdDefault.ProtoReflect.Descriptor instead.
func (*WeirdDefault) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{18}
}

func (x *WeirdDefault) GetWeirdDefault() []byte {
	if x != nil && x.WeirdDefault != nil {
		return x.WeirdDefault
	}
	return append([]byte(nil), Default_WeirdDefault_WeirdDefault...)
}

type RemoteDefault struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Default     *enums.Enum `protobuf:"varint,1,opt,name=default,enum=goproto.proto.enums.Enum" json:"default,omitempty"`
	Zero        *enums.Enum `protobuf:"varint,2,opt,name=zero,enum=goproto.proto.enums.Enum,def=0" json:"zero,omitempty"`
	One         *enums.Enum `protobuf:"varint,3,opt,name=one,enum=goproto.proto.enums.Enum,def=1" json:"one,omitempty"`
	Elevent     *enums.Enum `protobuf:"varint,4,opt,name=elevent,enum=goproto.proto.enums.Enum,def=11" json:"elevent,omitempty"`
	Seventeen   *enums.Enum `protobuf:"varint,5,opt,name=seventeen,enum=goproto.proto.enums.Enum,def=17" json:"seventeen,omitempty"`
	Thirtyseven *enums.Enum `protobuf:"varint,6,opt,name=thirtyseven,enum=goproto.proto.enums.Enum,def=37" json:"thirtyseven,omitempty"`
	Sixtyseven  *enums.Enum `protobuf:"varint,7,opt,name=sixtyseven,enum=goproto.proto.enums.Enum,def=67" json:"sixtyseven,omitempty"`
	Negative    *enums.Enum `protobuf:"varint,8,opt,name=negative,enum=goproto.proto.enums.Enum,def=-1" json:"negative,omitempty"`
}

// Default values for RemoteDefault fields.
const (
	Default_RemoteDefault_Zero        = enums.Enum(0)  // enums.Enum_ZERO
	Default_RemoteDefault_One         = enums.Enum(1)  // enums.Enum_ONE
	Default_RemoteDefault_Elevent     = enums.Enum(11) // enums.Enum_ELEVENT
	Default_RemoteDefault_Seventeen   = enums.Enum(17) // enums.Enum_SEVENTEEN
	Default_RemoteDefault_Thirtyseven = enums.Enum(37) // enums.Enum_THIRTYSEVEN
	Default_RemoteDefault_Sixtyseven  = enums.Enum(67) // enums.Enum_SIXTYSEVEN
	Default_RemoteDefault_Negative    = enums.Enum(-1) // enums.Enum_NEGATIVE
)

func (x *RemoteDefault) Reset() {
	*x = RemoteDefault{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteDefault) ProtoMessage() {}

func (x *RemoteDefault) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteDefault.ProtoReflect.Descriptor instead.
func (*RemoteDefault) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{19}
}

func (x *RemoteDefault) GetDefault() enums.Enum {
	if x != nil && x.Default != nil {
		return *x.Default
	}
	return enums.Enum(1337)
}

func (x *RemoteDefault) GetZero() enums.Enum {
	if x != nil && x.Zero != nil {
		return *x.Zero
	}
	return Default_RemoteDefault_Zero
}

func (x *RemoteDefault) GetOne() enums.Enum {
	if x != nil && x.One != nil {
		return *x.One
	}
	return Default_RemoteDefault_One
}

func (x *RemoteDefault) GetElevent() enums.Enum {
	if x != nil && x.Elevent != nil {
		return *x.Elevent
	}
	return Default_RemoteDefault_Elevent
}

func (x *RemoteDefault) GetSeventeen() enums.Enum {
	if x != nil && x.Seventeen != nil {
		return *x.Seventeen
	}
	return Default_RemoteDefault_Seventeen
}

func (x *RemoteDefault) GetThirtyseven() enums.Enum {
	if x != nil && x.Thirtyseven != nil {
		return *x.Thirtyseven
	}
	return Default_RemoteDefault_Thirtyseven
}

func (x *RemoteDefault) GetSixtyseven() enums.Enum {
	if x != nil && x.Sixtyseven != nil {
		return *x.Sixtyseven
	}
	return Default_RemoteDefault_Sixtyseven
}

func (x *RemoteDefault) GetNegative() enums.Enum {
	if x != nil && x.Negative != nil {
		return *x.Negative
	}
	return Default_RemoteDefault_Negative
}

type TestAllTypes_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           *int32        `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllTypes `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllTypes_NestedMessage) Reset() {
	*x = TestAllTypes_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_NestedMessage) ProtoMessage() {}

func (x *TestAllTypes_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllTypes_NestedMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllTypes_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_NestedMessage) GetCorecursive() *TestAllTypes {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

type TestAllTypes_OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                      `protobuf:"varint,17,opt,name=a" json:"a,omitempty"`
	OptionalNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,1000,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	SameFieldNumber       *int32                      `protobuf:"varint,16,opt,name=same_field_number,json=sameFieldNumber" json:"same_field_number,omitempty"`
}

func (x *TestAllTypes_OptionalGroup) Reset() {
	*x = TestAllTypes_OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_OptionalGroup) ProtoMessage() {}

func (x *TestAllTypes_OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_OptionalGroup.ProtoReflect.Descriptor instead.
func (*TestAllTypes_OptionalGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0, 1}
}

func (x *TestAllTypes_OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_OptionalGroup) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypes_OptionalGroup) GetSameFieldNumber() int32 {
	if x != nil && x.SameFieldNumber != nil {
		return *x.SameFieldNumber
	}
	return 0
}

type TestAllTypes_RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                      `protobuf:"varint,47,opt,name=a" json:"a,omitempty"`
	OptionalNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,1001,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
}

func (x *TestAllTypes_RepeatedGroup) Reset() {
	*x = TestAllTypes_RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_RepeatedGroup) ProtoMessage() {}

func (x *TestAllTypes_RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_RepeatedGroup.ProtoReflect.Descriptor instead.
func (*TestAllTypes_RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0, 2}
}

func (x *TestAllTypes_RepeatedGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_RepeatedGroup) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

type TestAllTypes_OneofGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	B *int32 `protobuf:"varint,2,opt,name=b" json:"b,omitempty"`
}

func (x *TestAllTypes_OneofGroup) Reset() {
	*x = TestAllTypes_OneofGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_OneofGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_OneofGroup) ProtoMessage() {}

func (x *TestAllTypes_OneofGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_OneofGroup.ProtoReflect.Descriptor instead.
func (*TestAllTypes_OneofGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{0, 20}
}

func (x *TestAllTypes_OneofGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_OneofGroup) GetB() int32 {
	if x != nil && x.B != nil {
		return *x.B
	}
	return 0
}

type TestAllExtensions_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           *int32             `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllExtensions `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllExtensions_NestedMessage) Reset() {
	*x = TestAllExtensions_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllExtensions_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllExtensions_NestedMessage) ProtoMessage() {}

func (x *TestAllExtensions_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllExtensions_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllExtensions_NestedMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{4, 0}
}

func (x *TestAllExtensions_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllExtensions_NestedMessage) GetCorecursive() *TestAllExtensions {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

type TestRequiredGroupFields_OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,2,req,name=a" json:"a,omitempty"`
}

func (x *TestRequiredGroupFields_OptionalGroup) Reset() {
	*x = TestRequiredGroupFields_OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredGroupFields_OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredGroupFields_OptionalGroup) ProtoMessage() {}

func (x *TestRequiredGroupFields_OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredGroupFields_OptionalGroup.ProtoReflect.Descriptor instead.
func (*TestRequiredGroupFields_OptionalGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{10, 0}
}

func (x *TestRequiredGroupFields_OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

type TestRequiredGroupFields_RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,4,req,name=a" json:"a,omitempty"`
}

func (x *TestRequiredGroupFields_RepeatedGroup) Reset() {
	*x = TestRequiredGroupFields_RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test_test_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredGroupFields_RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredGroupFields_RepeatedGroup) ProtoMessage() {}

func (x *TestRequiredGroupFields_RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test_test_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredGroupFields_RepeatedGroup.ProtoReflect.Descriptor instead.
func (*TestRequiredGroupFields_RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test_test_proto_rawDescGZIP(), []int{10, 1}
}

func (x *TestRequiredGroupFields_RepeatedGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

var file_internal_testprotos_test_test_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1,
		Name:          "goproto.proto.test.optional_int32",
		Tag:           "varint,1,opt,name=optional_int32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2,
		Name:          "goproto.proto.test.optional_int64",
		Tag:           "varint,2,opt,name=optional_int64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         3,
		Name:          "goproto.proto.test.optional_uint32",
		Tag:           "varint,3,opt,name=optional_uint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         4,
		Name:          "goproto.proto.test.optional_uint64",
		Tag:           "varint,4,opt,name=optional_uint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         5,
		Name:          "goproto.proto.test.optional_sint32",
		Tag:           "zigzag32,5,opt,name=optional_sint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         6,
		Name:          "goproto.proto.test.optional_sint64",
		Tag:           "zigzag64,6,opt,name=optional_sint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         7,
		Name:          "goproto.proto.test.optional_fixed32",
		Tag:           "fixed32,7,opt,name=optional_fixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         8,
		Name:          "goproto.proto.test.optional_fixed64",
		Tag:           "fixed64,8,opt,name=optional_fixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         9,
		Name:          "goproto.proto.test.optional_sfixed32",
		Tag:           "fixed32,9,opt,name=optional_sfixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         10,
		Name:          "goproto.proto.test.optional_sfixed64",
		Tag:           "fixed64,10,opt,name=optional_sfixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         11,
		Name:          "goproto.proto.test.optional_float",
		Tag:           "fixed32,11,opt,name=optional_float",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         12,
		Name:          "goproto.proto.test.optional_double",
		Tag:           "fixed64,12,opt,name=optional_double",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         13,
		Name:          "goproto.proto.test.optional_bool",
		Tag:           "varint,13,opt,name=optional_bool",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         14,
		Name:          "goproto.proto.test.optional_string",
		Tag:           "bytes,14,opt,name=optional_string",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         15,
		Name:          "goproto.proto.test.optional_bytes",
		Tag:           "bytes,15,opt,name=optional_bytes",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*OptionalGroup)(nil),
		Field:         16,
		Name:          "goproto.proto.test.optionalgroup",
		Tag:           "group,16,opt,name=OptionalGroup",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*TestAllExtensions_NestedMessage)(nil),
		Field:         18,
		Name:          "goproto.proto.test.optional_nested_message",
		Tag:           "bytes,18,opt,name=optional_nested_message",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*TestAllTypes_NestedEnum)(nil),
		Field:         21,
		Name:          "goproto.proto.test.optional_nested_enum",
		Tag:           "varint,21,opt,name=optional_nested_enum,enum=goproto.proto.test.TestAllTypes_NestedEnum",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         31,
		Name:          "goproto.proto.test.repeated_int32",
		Tag:           "varint,31,rep,name=repeated_int32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         32,
		Name:          "goproto.proto.test.repeated_int64",
		Tag:           "varint,32,rep,name=repeated_int64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         33,
		Name:          "goproto.proto.test.repeated_uint32",
		Tag:           "varint,33,rep,name=repeated_uint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         34,
		Name:          "goproto.proto.test.repeated_uint64",
		Tag:           "varint,34,rep,name=repeated_uint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         35,
		Name:          "goproto.proto.test.repeated_sint32",
		Tag:           "zigzag32,35,rep,name=repeated_sint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         36,
		Name:          "goproto.proto.test.repeated_sint64",
		Tag:           "zigzag64,36,rep,name=repeated_sint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         37,
		Name:          "goproto.proto.test.repeated_fixed32",
		Tag:           "fixed32,37,rep,name=repeated_fixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         38,
		Name:          "goproto.proto.test.repeated_fixed64",
		Tag:           "fixed64,38,rep,name=repeated_fixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         39,
		Name:          "goproto.proto.test.repeated_sfixed32",
		Tag:           "fixed32,39,rep,name=repeated_sfixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         40,
		Name:          "goproto.proto.test.repeated_sfixed64",
		Tag:           "fixed64,40,rep,name=repeated_sfixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         41,
		Name:          "goproto.proto.test.repeated_float",
		Tag:           "fixed32,41,rep,name=repeated_float",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         42,
		Name:          "goproto.proto.test.repeated_double",
		Tag:           "fixed64,42,rep,name=repeated_double",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         43,
		Name:          "goproto.proto.test.repeated_bool",
		Tag:           "varint,43,rep,name=repeated_bool",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         44,
		Name:          "goproto.proto.test.repeated_string",
		Tag:           "bytes,44,rep,name=repeated_string",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([][]byte)(nil),
		Field:         45,
		Name:          "goproto.proto.test.repeated_bytes",
		Tag:           "bytes,45,rep,name=repeated_bytes",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]*RepeatedGroup)(nil),
		Field:         46,
		Name:          "goproto.proto.test.repeatedgroup",
		Tag:           "group,46,rep,name=RepeatedGroup",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]*TestAllExtensions_NestedMessage)(nil),
		Field:         48,
		Name:          "goproto.proto.test.repeated_nested_message",
		Tag:           "bytes,48,rep,name=repeated_nested_message",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]TestAllTypes_NestedEnum)(nil),
		Field:         51,
		Name:          "goproto.proto.test.repeated_nested_enum",
		Tag:           "varint,51,rep,name=repeated_nested_enum,enum=goproto.proto.test.TestAllTypes_NestedEnum",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         81,
		Name:          "goproto.proto.test.default_int32",
		Tag:           "varint,81,opt,name=default_int32,def=81",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         82,
		Name:          "goproto.proto.test.default_int64",
		Tag:           "varint,82,opt,name=default_int64,def=82",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         83,
		Name:          "goproto.proto.test.default_uint32",
		Tag:           "varint,83,opt,name=default_uint32,def=83",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         84,
		Name:          "goproto.proto.test.default_uint64",
		Tag:           "varint,84,opt,name=default_uint64,def=84",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         85,
		Name:          "goproto.proto.test.default_sint32",
		Tag:           "zigzag32,85,opt,name=default_sint32,def=-85",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         86,
		Name:          "goproto.proto.test.default_sint64",
		Tag:           "zigzag64,86,opt,name=default_sint64,def=86",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         87,
		Name:          "goproto.proto.test.default_fixed32",
		Tag:           "fixed32,87,opt,name=default_fixed32,def=87",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         88,
		Name:          "goproto.proto.test.default_fixed64",
		Tag:           "fixed64,88,opt,name=default_fixed64,def=88",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         89,
		Name:          "goproto.proto.test.default_sfixed32",
		Tag:           "fixed32,89,opt,name=default_sfixed32,def=89",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         80,
		Name:          "goproto.proto.test.default_sfixed64",
		Tag:           "fixed64,80,opt,name=default_sfixed64,def=-90",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         91,
		Name:          "goproto.proto.test.default_float",
		Tag:           "fixed32,91,opt,name=default_float,def=91.5",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         92,
		Name:          "goproto.proto.test.default_double",
		Tag:           "fixed64,92,opt,name=default_double,def=92000",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         93,
		Name:          "goproto.proto.test.default_bool",
		Tag:           "varint,93,opt,name=default_bool,def=1",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         94,
		Name:          "goproto.proto.test.default_string",
		Tag:           "bytes,94,opt,name=default_string,def=hello",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         95,
		Name:          "goproto.proto.test.default_bytes",
		Tag:           "bytes,95,opt,name=default_bytes,def=world",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         90,
		Name:          "goproto.proto.test.packed_int32",
		Tag:           "varint,90,rep,packed,name=packed_int32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         91,
		Name:          "goproto.proto.test.packed_int64",
		Tag:           "varint,91,rep,packed,name=packed_int64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         92,
		Name:          "goproto.proto.test.packed_uint32",
		Tag:           "varint,92,rep,packed,name=packed_uint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         93,
		Name:          "goproto.proto.test.packed_uint64",
		Tag:           "varint,93,rep,packed,name=packed_uint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         94,
		Name:          "goproto.proto.test.packed_sint32",
		Tag:           "zigzag32,94,rep,packed,name=packed_sint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         95,
		Name:          "goproto.proto.test.packed_sint64",
		Tag:           "zigzag64,95,rep,packed,name=packed_sint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         96,
		Name:          "goproto.proto.test.packed_fixed32",
		Tag:           "fixed32,96,rep,packed,name=packed_fixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         97,
		Name:          "goproto.proto.test.packed_fixed64",
		Tag:           "fixed64,97,rep,packed,name=packed_fixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         98,
		Name:          "goproto.proto.test.packed_sfixed32",
		Tag:           "fixed32,98,rep,packed,name=packed_sfixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         99,
		Name:          "goproto.proto.test.packed_sfixed64",
		Tag:           "fixed64,99,rep,packed,name=packed_sfixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         100,
		Name:          "goproto.proto.test.packed_float",
		Tag:           "fixed32,100,rep,packed,name=packed_float",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         101,
		Name:          "goproto.proto.test.packed_double",
		Tag:           "fixed64,101,rep,packed,name=packed_double",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         102,
		Name:          "goproto.proto.test.packed_bool",
		Tag:           "varint,102,rep,packed,name=packed_bool",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]ForeignEnum)(nil),
		Field:         103,
		Name:          "goproto.proto.test.packed_enum",
		Tag:           "varint,103,rep,packed,name=packed_enum,enum=goproto.proto.test.ForeignEnum",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         90,
		Name:          "goproto.proto.test.unpacked_int32",
		Tag:           "varint,90,rep,name=unpacked_int32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         91,
		Name:          "goproto.proto.test.unpacked_int64",
		Tag:           "varint,91,rep,name=unpacked_int64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         92,
		Name:          "goproto.proto.test.unpacked_uint32",
		Tag:           "varint,92,rep,name=unpacked_uint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         93,
		Name:          "goproto.proto.test.unpacked_uint64",
		Tag:           "varint,93,rep,name=unpacked_uint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         94,
		Name:          "goproto.proto.test.unpacked_sint32",
		Tag:           "zigzag32,94,rep,name=unpacked_sint32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         95,
		Name:          "goproto.proto.test.unpacked_sint64",
		Tag:           "zigzag64,95,rep,name=unpacked_sint64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         96,
		Name:          "goproto.proto.test.unpacked_fixed32",
		Tag:           "fixed32,96,rep,name=unpacked_fixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         97,
		Name:          "goproto.proto.test.unpacked_fixed64",
		Tag:           "fixed64,97,rep,name=unpacked_fixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         98,
		Name:          "goproto.proto.test.unpacked_sfixed32",
		Tag:           "fixed32,98,rep,name=unpacked_sfixed32",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         99,
		Name:          "goproto.proto.test.unpacked_sfixed64",
		Tag:           "fixed64,99,rep,name=unpacked_sfixed64",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         100,
		Name:          "goproto.proto.test.unpacked_float",
		Tag:           "fixed32,100,rep,name=unpacked_float",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         101,
		Name:          "goproto.proto.test.unpacked_double",
		Tag:           "fixed64,101,rep,name=unpacked_double",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         102,
		Name:          "goproto.proto.test.unpacked_bool",
		Tag:           "varint,102,rep,name=unpacked_bool",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestUnpackedExtensions)(nil),
		ExtensionType: ([]ForeignEnum)(nil),
		Field:         103,
		Name:          "goproto.proto.test.unpacked_enum",
		Tag:           "varint,103,rep,name=unpacked_enum,enum=goproto.proto.test.ForeignEnum",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         1003,
		Name:          "goproto.proto.test.TestNestedExtension.nested_string_extension",
		Tag:           "bytes,1003,opt,name=nested_string_extension",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*TestRequired)(nil),
		Field:         1000,
		Name:          "goproto.proto.test.TestRequired.single",
		Tag:           "bytes,1000,opt,name=single",
		Filename:      "internal/testprotos/test/test.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]*TestRequired)(nil),
		Field:         1001,
		Name:          "goproto.proto.test.TestRequired.multi",
		Tag:           "bytes,1001,rep,name=multi",
		Filename:      "internal/testprotos/test/test.proto",
	},
}

// Extension fields to TestAllExtensions.
var (
	// optional int32 optional_int32 = 1;
	E_OptionalInt32 = &file_internal_testprotos_test_test_proto_extTypes[0]
	// optional int64 optional_int64 = 2;
	E_OptionalInt64 = &file_internal_testprotos_test_test_proto_extTypes[1]
	// optional uint32 optional_uint32 = 3;
	E_OptionalUint32 = &file_internal_testprotos_test_test_proto_extTypes[2]
	// optional uint64 optional_uint64 = 4;
	E_OptionalUint64 = &file_internal_testprotos_test_test_proto_extTypes[3]
	// optional sint32 optional_sint32 = 5;
	E_OptionalSint32 = &file_internal_testprotos_test_test_proto_extTypes[4]
	// optional sint64 optional_sint64 = 6;
	E_OptionalSint64 = &file_internal_testprotos_test_test_proto_extTypes[5]
	// optional fixed32 optional_fixed32 = 7;
	E_OptionalFixed32 = &file_internal_testprotos_test_test_proto_extTypes[6]
	// optional fixed64 optional_fixed64 = 8;
	E_OptionalFixed64 = &file_internal_testprotos_test_test_proto_extTypes[7]
	// optional sfixed32 optional_sfixed32 = 9;
	E_OptionalSfixed32 = &file_internal_testprotos_test_test_proto_extTypes[8]
	// optional sfixed64 optional_sfixed64 = 10;
	E_OptionalSfixed64 = &file_internal_testprotos_test_test_proto_extTypes[9]
	// optional float optional_float = 11;
	E_OptionalFloat = &file_internal_testprotos_test_test_proto_extTypes[10]
	// optional double optional_double = 12;
	E_OptionalDouble = &file_internal_testprotos_test_test_proto_extTypes[11]
	// optional bool optional_bool = 13;
	E_OptionalBool = &file_internal_testprotos_test_test_proto_extTypes[12]
	// optional string optional_string = 14;
	E_OptionalString = &file_internal_testprotos_test_test_proto_extTypes[13]
	// optional bytes optional_bytes = 15;
	E_OptionalBytes = &file_internal_testprotos_test_test_proto_extTypes[14]
	// optional goproto.proto.test.OptionalGroup optionalgroup = 16;
	E_Optionalgroup = &file_internal_testprotos_test_test_proto_extTypes[15]
	// optional goproto.proto.test.TestAllExtensions.NestedMessage optional_nested_message = 18;
	E_OptionalNestedMessage = &file_internal_testprotos_test_test_proto_extTypes[16]
	// optional goproto.proto.test.TestAllTypes.NestedEnum optional_nested_enum = 21;
	E_OptionalNestedEnum = &file_internal_testprotos_test_test_proto_extTypes[17]
	// repeated int32 repeated_int32 = 31;
	E_RepeatedInt32 = &file_internal_testprotos_test_test_proto_extTypes[18]
	// repeated int64 repeated_int64 = 32;
	E_RepeatedInt64 = &file_internal_testprotos_test_test_proto_extTypes[19]
	// repeated uint32 repeated_uint32 = 33;
	E_RepeatedUint32 = &file_internal_testprotos_test_test_proto_extTypes[20]
	// repeated uint64 repeated_uint64 = 34;
	E_RepeatedUint64 = &file_internal_testprotos_test_test_proto_extTypes[21]
	// repeated sint32 repeated_sint32 = 35;
	E_RepeatedSint32 = &file_internal_testprotos_test_test_proto_extTypes[22]
	// repeated sint64 repeated_sint64 = 36;
	E_RepeatedSint64 = &file_internal_testprotos_test_test_proto_extTypes[23]
	// repeated fixed32 repeated_fixed32 = 37;
	E_RepeatedFixed32 = &file_internal_testprotos_test_test_proto_extTypes[24]
	// repeated fixed64 repeated_fixed64 = 38;
	E_RepeatedFixed64 = &file_internal_testprotos_test_test_proto_extTypes[25]
	// repeated sfixed32 repeated_sfixed32 = 39;
	E_RepeatedSfixed32 = &file_internal_testprotos_test_test_proto_extTypes[26]
	// repeated sfixed64 repeated_sfixed64 = 40;
	E_RepeatedSfixed64 = &file_internal_testprotos_test_test_proto_extTypes[27]
	// repeated float repeated_float = 41;
	E_RepeatedFloat = &file_internal_testprotos_test_test_proto_extTypes[28]
	// repeated double repeated_double = 42;
	E_RepeatedDouble = &file_internal_testprotos_test_test_proto_extTypes[29]
	// repeated bool repeated_bool = 43;
	E_RepeatedBool = &file_internal_testprotos_test_test_proto_extTypes[30]
	// repeated string repeated_string = 44;
	E_RepeatedString = &file_internal_testprotos_test_test_proto_extTypes[31]
	// repeated bytes repeated_bytes = 45;
	E_RepeatedBytes = &file_internal_testprotos_test_test_proto_extTypes[32]
	// repeated goproto.proto.test.RepeatedGroup repeatedgroup = 46;
	E_Repeatedgroup = &file_internal_testprotos_test_test_proto_extTypes[33]
	// repeated goproto.proto.test.TestAllExtensions.NestedMessage repeated_nested_message = 48;
	E_RepeatedNestedMessage = &file_internal_testprotos_test_test_proto_extTypes[34]
	// repeated goproto.proto.test.TestAllTypes.NestedEnum repeated_nested_enum = 51;
	E_RepeatedNestedEnum = &file_internal_testprotos_test_test_proto_extTypes[35]
	// optional int32 default_int32 = 81;
	E_DefaultInt32 = &file_internal_testprotos_test_test_proto_extTypes[36]
	// optional int64 default_int64 = 82;
	E_DefaultInt64 = &file_internal_testprotos_test_test_proto_extTypes[37]
	// optional uint32 default_uint32 = 83;
	E_DefaultUint32 = &file_internal_testprotos_test_test_proto_extTypes[38]
	// optional uint64 default_uint64 = 84;
	E_DefaultUint64 = &file_internal_testprotos_test_test_proto_extTypes[39]
	// optional sint32 default_sint32 = 85;
	E_DefaultSint32 = &file_internal_testprotos_test_test_proto_extTypes[40]
	// optional sint64 default_sint64 = 86;
	E_DefaultSint64 = &file_internal_testprotos_test_test_proto_extTypes[41]
	// optional fixed32 default_fixed32 = 87;
	E_DefaultFixed32 = &file_internal_testprotos_test_test_proto_extTypes[42]
	// optional fixed64 default_fixed64 = 88;
	E_DefaultFixed64 = &file_internal_testprotos_test_test_proto_extTypes[43]
	// optional sfixed32 default_sfixed32 = 89;
	E_DefaultSfixed32 = &file_internal_testprotos_test_test_proto_extTypes[44]
	// optional sfixed64 default_sfixed64 = 80;
	E_DefaultSfixed64 = &file_internal_testprotos_test_test_proto_extTypes[45]
	// optional float default_float = 91;
	E_DefaultFloat = &file_internal_testprotos_test_test_proto_extTypes[46]
	// optional double default_double = 92;
	E_DefaultDouble = &file_internal_testprotos_test_test_proto_extTypes[47]
	// optional bool default_bool = 93;
	E_DefaultBool = &file_internal_testprotos_test_test_proto_extTypes[48]
	// optional string default_string = 94;
	E_DefaultString = &file_internal_testprotos_test_test_proto_extTypes[49]
	// optional bytes default_bytes = 95;
	E_DefaultBytes = &file_internal_testprotos_test_test_proto_extTypes[50]
	// optional string nested_string_extension = 1003;
	E_TestNestedExtension_NestedStringExtension = &file_internal_testprotos_test_test_proto_extTypes[79]
	// optional goproto.proto.test.TestRequired single = 1000;
	E_TestRequired_Single = &file_internal_testprotos_test_test_proto_extTypes[80]
	// repeated goproto.proto.test.TestRequired multi = 1001;
	E_TestRequired_Multi = &file_internal_testprotos_test_test_proto_extTypes[81]
)

// Extension fields to TestPackedExtensions.
var (
	// repeated int32 packed_int32 = 90;
	E_PackedInt32 = &file_internal_testprotos_test_test_proto_extTypes[51]
	// repeated int64 packed_int64 = 91;
	E_PackedInt64 = &file_internal_testprotos_test_test_proto_extTypes[52]
	// repeated uint32 packed_uint32 = 92;
	E_PackedUint32 = &file_internal_testprotos_test_test_proto_extTypes[53]
	// repeated uint64 packed_uint64 = 93;
	E_PackedUint64 = &file_internal_testprotos_test_test_proto_extTypes[54]
	// repeated sint32 packed_sint32 = 94;
	E_PackedSint32 = &file_internal_testprotos_test_test_proto_extTypes[55]
	// repeated sint64 packed_sint64 = 95;
	E_PackedSint64 = &file_internal_testprotos_test_test_proto_extTypes[56]
	// repeated fixed32 packed_fixed32 = 96;
	E_PackedFixed32 = &file_internal_testprotos_test_test_proto_extTypes[57]
	// repeated fixed64 packed_fixed64 = 97;
	E_PackedFixed64 = &file_internal_testprotos_test_test_proto_extTypes[58]
	// repeated sfixed32 packed_sfixed32 = 98;
	E_PackedSfixed32 = &file_internal_testprotos_test_test_proto_extTypes[59]
	// repeated sfixed64 packed_sfixed64 = 99;
	E_PackedSfixed64 = &file_internal_testprotos_test_test_proto_extTypes[60]
	// repeated float packed_float = 100;
	E_PackedFloat = &file_internal_testprotos_test_test_proto_extTypes[61]
	// repeated double packed_double = 101;
	E_PackedDouble = &file_internal_testprotos_test_test_proto_extTypes[62]
	// repeated bool packed_bool = 102;
	E_PackedBool = &file_internal_testprotos_test_test_proto_extTypes[63]
	// repeated goproto.proto.test.ForeignEnum packed_enum = 103;
	E_PackedEnum = &file_internal_testprotos_test_test_proto_extTypes[64]
)

// Extension fields to TestUnpackedExtensions.
var (
	// repeated int32 unpacked_int32 = 90;
	E_UnpackedInt32 = &file_internal_testprotos_test_test_proto_extTypes[65]
	// repeated int64 unpacked_int64 = 91;
	E_UnpackedInt64 = &file_internal_testprotos_test_test_proto_extTypes[66]
	// repeated uint32 unpacked_uint32 = 92;
	E_UnpackedUint32 = &file_internal_testprotos_test_test_proto_extTypes[67]
	// repeated uint64 unpacked_uint64 = 93;
	E_UnpackedUint64 = &file_internal_testprotos_test_test_proto_extTypes[68]
	// repeated sint32 unpacked_sint32 = 94;
	E_UnpackedSint32 = &file_internal_testprotos_test_test_proto_extTypes[69]
	// repeated sint64 unpacked_sint64 = 95;
	E_UnpackedSint64 = &file_internal_testprotos_test_test_proto_extTypes[70]
	// repeated fixed32 unpacked_fixed32 = 96;
	E_UnpackedFixed32 = &file_internal_testprotos_test_test_proto_extTypes[71]
	// repeated fixed64 unpacked_fixed64 = 97;
	E_UnpackedFixed64 = &file_internal_testprotos_test_test_proto_extTypes[72]
	// repeated sfixed32 unpacked_sfixed32 = 98;
	E_UnpackedSfixed32 = &file_internal_testprotos_test_test_proto_extTypes[73]
	// repeated sfixed64 unpacked_sfixed64 = 99;
	E_UnpackedSfixed64 = &file_internal_testprotos_test_test_proto_extTypes[74]
	// repeated float unpacked_float = 100;
	E_UnpackedFloat = &file_internal_testprotos_test_test_proto_extTypes[75]
	// repeated double unpacked_double = 101;
	E_UnpackedDouble = &file_internal_testprotos_test_test_proto_extTypes[76]
	// repeated bool unpacked_bool = 102;
	E_UnpackedBool = &file_internal_testprotos_test_test_proto_extTypes[77]
	// repeated goproto.proto.test.ForeignEnum unpacked_enum = 103;
	E_UnpackedEnum = &file_internal_testprotos_test_test_proto_extTypes[78]
)

var File_internal_testprotos_test_test_proto protoreflect.FileDescriptor

var file_internal_testprotos_test_test_proto_rawDesc = []byte{
	0x0a, 0x23, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74,
	0x65, 0x73, 0x74, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f,
	0x77, 0x65, 0x61, 0x6b, 0x31, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f,
	0x77, 0x65, 0x61, 0x6b, 0x32, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa5, 0x3a, 0x0a, 0x0c, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x11, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06,
	0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b,
	0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x54, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0a, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x66, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5c,
	0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x59, 0x0a, 0x17,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x53, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x50, 0x0a, 0x14, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x25, 0x0a,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x1f, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x20, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x21,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x23, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x24, 0x20, 0x03, 0x28, 0x12, 0x52,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x18, 0x25, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x26,
	0x20, 0x03, 0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0f,
	0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x18, 0x29, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c,
	0x18, 0x2b, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x2d, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x2e, 0x20, 0x03, 0x28, 0x0a, 0x32, 0x2e, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0d, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x66, 0x0a, 0x17, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x5c, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x31,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x58, 0x0a, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x32, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x14, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x53, 0x0a, 0x15, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x18, 0x34, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x4f,
	0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x35, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x6e, 0x75, 0x6d, 0x12,
	0x5b, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x38, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d,
	0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x5b, 0x0a, 0x0f,
	0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x39, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x61, 0x0a, 0x11, 0x6d, 0x61, 0x70,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x61, 0x0a, 0x11,
	0x6d, 0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x3b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x61, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0x3c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x61, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x67, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x3e, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x67,
	0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x3f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61,
	0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x6d, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x18, 0x40, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x6d, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18,
	0x41, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x5b, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x42, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x5e, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d,
	0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x12, 0x55, 0x0a, 0x0d, 0x6d, 0x61, 0x70, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x18, 0x44, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x42,
	0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61,
	0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x61, 0x0a, 0x11, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x45,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x5e, 0x0a, 0x10,
	0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x18, 0x46, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x77, 0x0a, 0x19,
	0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x47, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x6d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x6e, 0x0a, 0x16, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x49, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x51, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x02, 0x38, 0x31,
	0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27,
	0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x52, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x02, 0x38, 0x32, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x02, 0x38, 0x33, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x54, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x02, 0x38, 0x34, 0x52, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a,
	0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x55, 0x20, 0x01, 0x28, 0x11, 0x3a, 0x03, 0x2d, 0x38, 0x35, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x56, 0x20, 0x01, 0x28,
	0x12, 0x3a, 0x02, 0x38, 0x36, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x57, 0x20, 0x01, 0x28, 0x07, 0x3a, 0x02, 0x38,
	0x37, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x2b, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0x58, 0x20, 0x01, 0x28, 0x06, 0x3a, 0x02, 0x38, 0x38, 0x52, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d,
	0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x18, 0x59, 0x20, 0x01, 0x28, 0x0f, 0x3a, 0x02, 0x38, 0x39, 0x52, 0x0f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2e, 0x0a,
	0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0x50, 0x20, 0x01, 0x28, 0x10, 0x3a, 0x03, 0x2d, 0x39, 0x30, 0x52, 0x0f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x29, 0x0a,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x5b,
	0x20, 0x01, 0x28, 0x02, 0x3a, 0x04, 0x39, 0x31, 0x2e, 0x35, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x01,
	0x3a, 0x05, 0x39, 0x32, 0x30, 0x30, 0x30, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72,
	0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12,
	0x2c, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x05, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x52, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x0a,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x5f,
	0x20, 0x01, 0x28, 0x0c, 0x3a, 0x05, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x0c, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x60, 0x0a, 0x13, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x18, 0x60, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x3a, 0x03, 0x42, 0x41, 0x52, 0x52, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x5e, 0x0a, 0x14, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x61, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x0b, 0x46, 0x4f, 0x52, 0x45,
	0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x52, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x6f, 0x20, 0x01, 0x28,
	0x0d, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x62, 0x0a, 0x14, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00,
	0x52, 0x12, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x71, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x72, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00,
	0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0a,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x73, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x23, 0x0a,
	0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x74, 0x20,
	0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x18, 0x75, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x76, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x4c, 0x0a, 0x0a, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x77, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x00, 0x52, 0x09, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x4d, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x79, 0x20, 0x01, 0x28, 0x0a, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4f,
	0x6e, 0x65, 0x6f, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x34, 0x0a, 0x15, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x78, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x13, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x1a, 0x61, 0x0a,
	0x0d, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c,
	0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x42, 0x0a, 0x0b,
	0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65,
	0x1a, 0xb2, 0x01, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61,
	0x12, 0x67, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe8, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x61, 0x6d,
	0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6d, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x1a, 0x86, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x2f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x67, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x40,
	0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61,
	0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x11, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42,
	0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x12, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x07, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46,
	0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x10, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40,
	0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f,
	0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x79, 0x0a, 0x1b, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x44, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x73, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x28, 0x0a, 0x0a, 0x4f, 0x6e,
	0x65, 0x6f, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x0c, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x62, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42,
	0x41, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x03, 0x4e, 0x45, 0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x42, 0x10,
	0x0a, 0x0e, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x22, 0xc4, 0x01, 0x0a, 0x15, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x10, 0x64, 0x65,
	0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x3a, 0x0a, 0x16, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52,
	0x14, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x6e, 0x65, 0x6f, 0x66,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x22, 0x28, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x0a, 0x44, 0x45, 0x50, 0x52, 0x45,
	0x43, 0x41, 0x54, 0x45, 0x44, 0x10, 0x00, 0x1a, 0x02, 0x08, 0x01, 0x1a, 0x02, 0x18, 0x01, 0x3a,
	0x02, 0x18, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x2c, 0x0a, 0x0e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x63, 0x12, 0x0c, 0x0a, 0x01, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x64, 0x22, 0x30, 0x0a, 0x12, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x4a, 0x04, 0x08, 0x02, 0x10,
	0x03, 0x4a, 0x04, 0x08, 0x0f, 0x10, 0x10, 0x4a, 0x04, 0x08, 0x09, 0x10, 0x0c, 0x52, 0x03, 0x62,
	0x61, 0x72, 0x52, 0x03, 0x62, 0x61, 0x7a, 0x22, 0x85, 0x01, 0x0a, 0x11, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x66, 0x0a,
	0x0d, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c,
	0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x47, 0x0a, 0x0b,
	0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x73, 0x69, 0x76, 0x65, 0x2a, 0x08, 0x08, 0x01, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22,
	0xb7, 0x01, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12,
	0x2a, 0x0a, 0x11, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6d, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x6c, 0x0a, 0x17, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe8, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x0d, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61,
	0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x6c, 0x0a, 0x17, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x75, 0x0a, 0x13, 0x54, 0x65, 0x73, 0x74, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x5e,
	0x0a, 0x17, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xeb, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf7,
	0x01, 0x0a, 0x0c, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x32, 0x60, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe8, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x32, 0x5e, 0x0a, 0x05, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe9, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x05, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x22, 0xc2, 0x03, 0x0a, 0x13, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x12, 0x4b, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4b, 0x0a,
	0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x58, 0x0a, 0x0b, 0x6d, 0x61,
	0x70, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x2e, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6d, 0x61, 0x70, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x0d, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52,
	0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x5f, 0x0a,
	0x0f, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x22, 0x99, 0x02,
	0x0a, 0x17, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x5f, 0x0a, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0a,
	0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x5f, 0x0a, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0a, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x1d, 0x0a, 0x0d, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01,
	0x61, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52, 0x01, 0x61, 0x1a, 0x1d, 0x0a, 0x0d, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61,
	0x18, 0x04, 0x20, 0x02, 0x28, 0x05, 0x52, 0x01, 0x61, 0x22, 0xb6, 0x01, 0x0a, 0x08, 0x54, 0x65,
	0x73, 0x74, 0x57, 0x65, 0x61, 0x6b, 0x12, 0x54, 0x0a, 0x0d, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x77, 0x65, 0x61, 0x6b, 0x2e, 0x57, 0x65, 0x61, 0x6b, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x31, 0x42, 0x02, 0x50, 0x01, 0x52, 0x0c,
	0x77, 0x65, 0x61, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x31, 0x12, 0x54, 0x0a, 0x0d,
	0x77, 0x65, 0x61, 0x6b, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x77, 0x65, 0x61, 0x6b, 0x2e, 0x57, 0x65,
	0x61, 0x6b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32,
	0x42, 0x02, 0x50, 0x01, 0x52, 0x0c, 0x77, 0x65, 0x61, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x32, 0x22, 0xee, 0x04, 0x0a, 0x0f, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5a, 0x20, 0x03, 0x28, 0x05, 0x42, 0x02, 0x10, 0x01,
	0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5b, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x01, 0x52,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5d,
	0x20, 0x03, 0x28, 0x04, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x11, 0x42, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x27, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x5f, 0x20, 0x03, 0x28, 0x12, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x60, 0x20, 0x03, 0x28, 0x07,
	0x42, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x61, 0x20, 0x03, 0x28, 0x06, 0x42, 0x02, 0x10, 0x01, 0x52,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b,
	0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x62, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x0f, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x63,
	0x20, 0x03, 0x28, 0x10, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x64, 0x20, 0x03, 0x28, 0x02, 0x42, 0x02,
	0x10, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12,
	0x27, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x18, 0x65, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x66, 0x20, 0x03, 0x28, 0x08, 0x42, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x44, 0x0a,
	0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x67, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x22, 0xa8, 0x05, 0x0a, 0x11, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x0e, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5a, 0x20, 0x03, 0x28,
	0x05, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x10, 0x00,
	0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x2b, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x0f,
	0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x5d, 0x20, 0x03, 0x28, 0x04, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x0f, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5e, 0x20, 0x03,
	0x28, 0x11, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x12, 0x42,
	0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x2d, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x60, 0x20, 0x03, 0x28, 0x07, 0x42, 0x02, 0x10,
	0x00, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x2d, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x61, 0x20, 0x03, 0x28, 0x06, 0x42, 0x02, 0x10, 0x00,
	0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x2f, 0x0a, 0x11, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x62, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x02, 0x10, 0x00,
	0x52, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x2f, 0x0a, 0x11, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x63, 0x20, 0x03, 0x28, 0x10, 0x42, 0x02, 0x10,
	0x00, 0x52, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x64, 0x20, 0x03, 0x28, 0x02, 0x42, 0x02, 0x10, 0x00, 0x52,
	0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2b,
	0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x18, 0x65, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x66, 0x20, 0x03,
	0x28, 0x08, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0c, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x48, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x67, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02, 0x10, 0x00,
	0x52, 0x0c, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x22, 0x20,
	0x0a, 0x14, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2a, 0x08, 0x08, 0x01, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02,
	0x22, 0x22, 0x0a, 0x16, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2a, 0x08, 0x08, 0x01, 0x10, 0x80,
	0x80, 0x80, 0x80, 0x02, 0x22, 0x0c, 0x0a, 0x0a, 0x46, 0x6f, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x0d, 0x0a, 0x0b, 0x46, 0x6f, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x61, 0x0a, 0x0c, 0x57, 0x65, 0x69, 0x72, 0x64, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x12, 0x51, 0x0a, 0x0d, 0x77, 0x65, 0x69, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x2c, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x2c,
	0x20, 0x5c, 0x22, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x21, 0x5c, 0x22, 0x5c, 0x6e, 0x64, 0x65, 0x61,
	0x64, 0x5c, 0x33, 0x33, 0x36, 0x5c, 0x32, 0x35, 0x35, 0x5c, 0x32, 0x37, 0x36, 0x5c, 0x33, 0x35,
	0x37, 0x62, 0x65, 0x65, 0x66, 0x60, 0x52, 0x0c, 0x77, 0x65, 0x69, 0x72, 0x64, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x22, 0xff, 0x03, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x7a,
	0x65, 0x72, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x04, 0x5a, 0x45, 0x52, 0x4f, 0x52, 0x04, 0x7a, 0x65, 0x72, 0x6f,
	0x12, 0x30, 0x0a, 0x03, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x03, 0x4f, 0x4e, 0x45, 0x52, 0x03, 0x6f,
	0x6e, 0x65, 0x12, 0x3c, 0x0a, 0x07, 0x65, 0x6c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x07,
	0x45, 0x4c, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x52, 0x07, 0x65, 0x6c, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x42, 0x0a, 0x09, 0x73, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x65, 0x65, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x09,
	0x53, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x45, 0x45, 0x4e, 0x52, 0x09, 0x73, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x65, 0x65, 0x6e, 0x12, 0x48, 0x0a, 0x0b, 0x74, 0x68, 0x69, 0x72, 0x74, 0x79, 0x73, 0x65,
	0x76, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x0b, 0x54, 0x48, 0x49, 0x52, 0x54, 0x59, 0x53, 0x45, 0x56, 0x45,
	0x4e, 0x52, 0x0b, 0x74, 0x68, 0x69, 0x72, 0x74, 0x79, 0x73, 0x65, 0x76, 0x65, 0x6e, 0x12, 0x45,
	0x0a, 0x0a, 0x73, 0x69, 0x78, 0x74, 0x79, 0x73, 0x65, 0x76, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x0a, 0x53,
	0x49, 0x58, 0x54, 0x59, 0x53, 0x45, 0x56, 0x45, 0x4e, 0x52, 0x0a, 0x73, 0x69, 0x78, 0x74, 0x79,
	0x73, 0x65, 0x76, 0x65, 0x6e, 0x12, 0x3f, 0x0a, 0x08, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x3a, 0x08, 0x4e, 0x45, 0x47, 0x41, 0x54, 0x49, 0x56, 0x45, 0x52, 0x08, 0x6e, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2a, 0x40, 0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e,
	0x5f, 0x46, 0x4f, 0x4f, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47,
	0x4e, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49,
	0x47, 0x4e, 0x5f, 0x42, 0x41, 0x5a, 0x10, 0x06, 0x2a, 0x47, 0x0a, 0x16, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x5f, 0x45,
	0x4e, 0x55, 0x4d, 0x10, 0x00, 0x22, 0x04, 0x08, 0x02, 0x10, 0x02, 0x22, 0x04, 0x08, 0x0f, 0x10,
	0x0f, 0x22, 0x04, 0x08, 0x09, 0x10, 0x0b, 0x2a, 0x03, 0x42, 0x41, 0x52, 0x2a, 0x03, 0x42, 0x41,
	0x5a, 0x32, 0xa8, 0x01, 0x0a, 0x0b, 0x54, 0x65, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x46, 0x0a, 0x03, 0x46, 0x6f, 0x6f, 0x12, 0x1e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0a, 0x54, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x1e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x30, 0x01, 0x32, 0x85, 0x01, 0x0a,
	0x15, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x12, 0x29, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65,
	0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a,
	0x29, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x1a,
	0x03, 0x88, 0x02, 0x01, 0x3a, 0x4c, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x3a, 0x4c, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x4e, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x3a, 0x4e, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x4e, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x3a, 0x4e, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x12,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x50, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x3a, 0x50, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x3a, 0x52, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x52, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x4c, 0x0a, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x25,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x4e, 0x0a, 0x0f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x4a, 0x0a, 0x0d, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x25, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x4e, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x4c, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x3a, 0x6e, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0a, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x3a, 0x92, 0x01, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x84, 0x01, 0x0a, 0x14, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x3a, 0x4c, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4c,
	0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x4e, 0x0a, 0x0f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4e, 0x0a, 0x0f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x4e, 0x0a, 0x0f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4e, 0x0a, 0x0f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x12, 0x52, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x50, 0x0a, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x25, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x50,
	0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x26, 0x20, 0x03, 0x28, 0x06, 0x52,
	0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x3a, 0x52, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x27, 0x20, 0x03,
	0x28, 0x0f, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x3a, 0x52, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x4c, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x29, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x4e, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x4a, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2b,
	0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f,
	0x6f, 0x6c, 0x3a, 0x4e, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2c, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x3a, 0x4c, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2d, 0x20, 0x03, 0x28,
	0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x3a, 0x6e, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2e, 0x20, 0x03, 0x28, 0x0a, 0x32, 0x21,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x3a, 0x92, 0x01, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x84, 0x01, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x25,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x4e, 0x0a, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x51, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x02, 0x38, 0x31, 0x52, 0x0c,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4e, 0x0a, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x25, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x52, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x02, 0x38, 0x32, 0x52, 0x0c,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x50, 0x0a, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x02, 0x38, 0x33, 0x52,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x50,
	0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x54, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x02, 0x38,
	0x34, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x51, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x55, 0x20, 0x01, 0x28, 0x11, 0x3a,
	0x03, 0x2d, 0x38, 0x35, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x3a, 0x50, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x56, 0x20, 0x01,
	0x28, 0x12, 0x3a, 0x02, 0x38, 0x36, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x52, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x57, 0x20, 0x01, 0x28, 0x07, 0x3a, 0x02, 0x38, 0x37, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x52, 0x0a, 0x0f, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x58, 0x20, 0x01, 0x28, 0x06, 0x3a, 0x02, 0x38, 0x38, 0x52, 0x0e, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x54, 0x0a,
	0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x59, 0x20, 0x01, 0x28, 0x0f, 0x3a, 0x02,
	0x38, 0x39, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x50,
	0x20, 0x01, 0x28, 0x10, 0x3a, 0x03, 0x2d, 0x39, 0x30, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x50, 0x0a, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x25, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x04, 0x39, 0x31, 0x2e, 0x35, 0x52, 0x0c,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x53, 0x0a, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x25,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x05, 0x39, 0x32, 0x30,
	0x30, 0x30, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x3a, 0x4e, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x6f, 0x6f,
	0x6c, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04,
	0x74, 0x72, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f,
	0x6c, 0x3a, 0x53, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x09,
	0x3a, 0x05, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x51, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5f,
	0x20, 0x01, 0x28, 0x0c, 0x3a, 0x05, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x0c, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x3a, 0x4f, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x5a, 0x20, 0x03, 0x28, 0x05, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4f, 0x0a, 0x0c, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x51, 0x0a, 0x0d, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x51,
	0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x04, 0x42,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x3a, 0x51, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5e, 0x20, 0x03,
	0x28, 0x11, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x3a, 0x51, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x5f, 0x20, 0x03, 0x28, 0x12, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x53, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x60, 0x20, 0x03, 0x28, 0x07, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x53, 0x0a, 0x0e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x28,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x61, 0x20, 0x03, 0x28, 0x06, 0x42, 0x02,
	0x10, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x3a, 0x55, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x62,
	0x20, 0x03, 0x28, 0x0f, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x28, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x63, 0x20, 0x03, 0x28, 0x10, 0x42, 0x02, 0x10, 0x01, 0x52,
	0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a,
	0x4f, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12,
	0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x64, 0x20, 0x03, 0x28, 0x02, 0x42,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x3a, 0x51, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x65, 0x20, 0x03, 0x28,
	0x01, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x3a, 0x4d, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x66, 0x20, 0x03,
	0x28, 0x08, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f,
	0x6f, 0x6c, 0x3a, 0x6e, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x67, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x3a, 0x55, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x5a, 0x20, 0x03, 0x28, 0x05, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x0e, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x10,
	0x00, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x57, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x5c, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x57, 0x0a, 0x0f, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x04, 0x42, 0x02,
	0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x3a, 0x57, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x11, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x57, 0x0a, 0x0f, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x12,
	0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x59, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x60, 0x20, 0x03, 0x28, 0x07, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0f,
	0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a,
	0x59, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x61, 0x20, 0x03, 0x28, 0x06, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x5b, 0x0a, 0x11, 0x75, 0x6e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x62, 0x20, 0x03, 0x28,
	0x0f, 0x42, 0x02, 0x10, 0x00, 0x52, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x5b, 0x0a, 0x11, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x63, 0x20, 0x03, 0x28, 0x10, 0x42, 0x02,
	0x10, 0x00, 0x52, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x3a, 0x55, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x64, 0x20, 0x03, 0x28, 0x02, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0d, 0x75, 0x6e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x57, 0x0a, 0x0f, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2a,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x65, 0x20, 0x03, 0x28, 0x01,
	0x42, 0x02, 0x10, 0x00, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x3a, 0x53, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x55,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x66, 0x20, 0x03, 0x28, 0x08, 0x42, 0x02, 0x10, 0x00, 0x52, 0x0c, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x74, 0x0a, 0x0d, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x67, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02, 0x10,
	0x00, 0x52, 0x0c, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x35, 0x5a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x50, 0x00, 0x58, 0x01, 0x58, 0x02,
}

var (
	file_internal_testprotos_test_test_proto_rawDescOnce sync.Once
	file_internal_testprotos_test_test_proto_rawDescData = file_internal_testprotos_test_test_proto_rawDesc
)

func file_internal_testprotos_test_test_proto_rawDescGZIP() []byte {
	file_internal_testprotos_test_test_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_test_test_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_test_test_proto_rawDescData)
	})
	return file_internal_testprotos_test_test_proto_rawDescData
}

var file_internal_testprotos_test_test_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_internal_testprotos_test_test_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_internal_testprotos_test_test_proto_goTypes = []interface{}{
	(ForeignEnum)(0),                          // 0: goproto.proto.test.ForeignEnum
	(TestReservedEnumFields)(0),               // 1: goproto.proto.test.TestReservedEnumFields
	(TestAllTypes_NestedEnum)(0),              // 2: goproto.proto.test.TestAllTypes.NestedEnum
	(TestDeprecatedMessage_DeprecatedEnum)(0), // 3: goproto.proto.test.TestDeprecatedMessage.DeprecatedEnum
	(*TestAllTypes)(nil),                      // 4: goproto.proto.test.TestAllTypes
	(*TestDeprecatedMessage)(nil),             // 5: goproto.proto.test.TestDeprecatedMessage
	(*ForeignMessage)(nil),                    // 6: goproto.proto.test.ForeignMessage
	(*TestReservedFields)(nil),                // 7: goproto.proto.test.TestReservedFields
	(*TestAllExtensions)(nil),                 // 8: goproto.proto.test.TestAllExtensions
	(*OptionalGroup)(nil),                     // 9: goproto.proto.test.OptionalGroup
	(*RepeatedGroup)(nil),                     // 10: goproto.proto.test.RepeatedGroup
	(*TestNestedExtension)(nil),               // 11: goproto.proto.test.TestNestedExtension
	(*TestRequired)(nil),                      // 12: goproto.proto.test.TestRequired
	(*TestRequiredForeign)(nil),               // 13: goproto.proto.test.TestRequiredForeign
	(*TestRequiredGroupFields)(nil),           // 14: goproto.proto.test.TestRequiredGroupFields
	(*TestWeak)(nil),                          // 15: goproto.proto.test.TestWeak
	(*TestPackedTypes)(nil),                   // 16: goproto.proto.test.TestPackedTypes
	(*TestUnpackedTypes)(nil),                 // 17: goproto.proto.test.TestUnpackedTypes
	(*TestPackedExtensions)(nil),              // 18: goproto.proto.test.TestPackedExtensions
	(*TestUnpackedExtensions)(nil),            // 19: goproto.proto.test.TestUnpackedExtensions
	(*FooRequest)(nil),                        // 20: goproto.proto.test.FooRequest
	(*FooResponse)(nil),                       // 21: goproto.proto.test.FooResponse
	(*WeirdDefault)(nil),                      // 22: goproto.proto.test.WeirdDefault
	(*RemoteDefault)(nil),                     // 23: goproto.proto.test.RemoteDefault
	(*TestAllTypes_NestedMessage)(nil),        // 24: goproto.proto.test.TestAllTypes.NestedMessage
	(*TestAllTypes_OptionalGroup)(nil),        // 25: goproto.proto.test.TestAllTypes.OptionalGroup
	(*TestAllTypes_RepeatedGroup)(nil),        // 26: goproto.proto.test.TestAllTypes.RepeatedGroup
	nil,                                       // 27: goproto.proto.test.TestAllTypes.MapInt32Int32Entry
	nil,                                       // 28: goproto.proto.test.TestAllTypes.MapInt64Int64Entry
	nil,                                       // 29: goproto.proto.test.TestAllTypes.MapUint32Uint32Entry
	nil,                                       // 30: goproto.proto.test.TestAllTypes.MapUint64Uint64Entry
	nil,                                       // 31: goproto.proto.test.TestAllTypes.MapSint32Sint32Entry
	nil,                                       // 32: goproto.proto.test.TestAllTypes.MapSint64Sint64Entry
	nil,                                       // 33: goproto.proto.test.TestAllTypes.MapFixed32Fixed32Entry
	nil,                                       // 34: goproto.proto.test.TestAllTypes.MapFixed64Fixed64Entry
	nil,                                       // 35: goproto.proto.test.TestAllTypes.MapSfixed32Sfixed32Entry
	nil,                                       // 36: goproto.proto.test.TestAllTypes.MapSfixed64Sfixed64Entry
	nil,                                       // 37: goproto.proto.test.TestAllTypes.MapInt32FloatEntry
	nil,                                       // 38: goproto.proto.test.TestAllTypes.MapInt32DoubleEntry
	nil,                                       // 39: goproto.proto.test.TestAllTypes.MapBoolBoolEntry
	nil,                                       // 40: goproto.proto.test.TestAllTypes.MapStringStringEntry
	nil,                                       // 41: goproto.proto.test.TestAllTypes.MapStringBytesEntry
	nil,                                       // 42: goproto.proto.test.TestAllTypes.MapStringNestedMessageEntry
	nil,                                       // 43: goproto.proto.test.TestAllTypes.MapStringNestedEnumEntry
	(*TestAllTypes_OneofGroup)(nil),           // 44: goproto.proto.test.TestAllTypes.OneofGroup
	(*TestAllExtensions_NestedMessage)(nil),   // 45: goproto.proto.test.TestAllExtensions.NestedMessage
	nil,                                       // 46: goproto.proto.test.TestRequiredForeign.MapMessageEntry
	(*TestRequiredGroupFields_OptionalGroup)(nil), // 47: goproto.proto.test.TestRequiredGroupFields.OptionalGroup
	(*TestRequiredGroupFields_RepeatedGroup)(nil), // 48: goproto.proto.test.TestRequiredGroupFields.RepeatedGroup
	(*ImportMessage)(nil),                         // 49: goproto.proto.test.ImportMessage
	(ImportEnum)(0),                               // 50: goproto.proto.test.ImportEnum
	(enums.Enum)(0),                               // 51: goproto.proto.enums.Enum
}
var file_internal_testprotos_test_test_proto_depIdxs = []int32{
	25,  // 0: goproto.proto.test.TestAllTypes.optionalgroup:type_name -> goproto.proto.test.TestAllTypes.OptionalGroup
	24,  // 1: goproto.proto.test.TestAllTypes.optional_nested_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	6,   // 2: goproto.proto.test.TestAllTypes.optional_foreign_message:type_name -> goproto.proto.test.ForeignMessage
	49,  // 3: goproto.proto.test.TestAllTypes.optional_import_message:type_name -> goproto.proto.test.ImportMessage
	2,   // 4: goproto.proto.test.TestAllTypes.optional_nested_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	0,   // 5: goproto.proto.test.TestAllTypes.optional_foreign_enum:type_name -> goproto.proto.test.ForeignEnum
	50,  // 6: goproto.proto.test.TestAllTypes.optional_import_enum:type_name -> goproto.proto.test.ImportEnum
	26,  // 7: goproto.proto.test.TestAllTypes.repeatedgroup:type_name -> goproto.proto.test.TestAllTypes.RepeatedGroup
	24,  // 8: goproto.proto.test.TestAllTypes.repeated_nested_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	6,   // 9: goproto.proto.test.TestAllTypes.repeated_foreign_message:type_name -> goproto.proto.test.ForeignMessage
	49,  // 10: goproto.proto.test.TestAllTypes.repeated_importmessage:type_name -> goproto.proto.test.ImportMessage
	2,   // 11: goproto.proto.test.TestAllTypes.repeated_nested_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	0,   // 12: goproto.proto.test.TestAllTypes.repeated_foreign_enum:type_name -> goproto.proto.test.ForeignEnum
	50,  // 13: goproto.proto.test.TestAllTypes.repeated_importenum:type_name -> goproto.proto.test.ImportEnum
	27,  // 14: goproto.proto.test.TestAllTypes.map_int32_int32:type_name -> goproto.proto.test.TestAllTypes.MapInt32Int32Entry
	28,  // 15: goproto.proto.test.TestAllTypes.map_int64_int64:type_name -> goproto.proto.test.TestAllTypes.MapInt64Int64Entry
	29,  // 16: goproto.proto.test.TestAllTypes.map_uint32_uint32:type_name -> goproto.proto.test.TestAllTypes.MapUint32Uint32Entry
	30,  // 17: goproto.proto.test.TestAllTypes.map_uint64_uint64:type_name -> goproto.proto.test.TestAllTypes.MapUint64Uint64Entry
	31,  // 18: goproto.proto.test.TestAllTypes.map_sint32_sint32:type_name -> goproto.proto.test.TestAllTypes.MapSint32Sint32Entry
	32,  // 19: goproto.proto.test.TestAllTypes.map_sint64_sint64:type_name -> goproto.proto.test.TestAllTypes.MapSint64Sint64Entry
	33,  // 20: goproto.proto.test.TestAllTypes.map_fixed32_fixed32:type_name -> goproto.proto.test.TestAllTypes.MapFixed32Fixed32Entry
	34,  // 21: goproto.proto.test.TestAllTypes.map_fixed64_fixed64:type_name -> goproto.proto.test.TestAllTypes.MapFixed64Fixed64Entry
	35,  // 22: goproto.proto.test.TestAllTypes.map_sfixed32_sfixed32:type_name -> goproto.proto.test.TestAllTypes.MapSfixed32Sfixed32Entry
	36,  // 23: goproto.proto.test.TestAllTypes.map_sfixed64_sfixed64:type_name -> goproto.proto.test.TestAllTypes.MapSfixed64Sfixed64Entry
	37,  // 24: goproto.proto.test.TestAllTypes.map_int32_float:type_name -> goproto.proto.test.TestAllTypes.MapInt32FloatEntry
	38,  // 25: goproto.proto.test.TestAllTypes.map_int32_double:type_name -> goproto.proto.test.TestAllTypes.MapInt32DoubleEntry
	39,  // 26: goproto.proto.test.TestAllTypes.map_bool_bool:type_name -> goproto.proto.test.TestAllTypes.MapBoolBoolEntry
	40,  // 27: goproto.proto.test.TestAllTypes.map_string_string:type_name -> goproto.proto.test.TestAllTypes.MapStringStringEntry
	41,  // 28: goproto.proto.test.TestAllTypes.map_string_bytes:type_name -> goproto.proto.test.TestAllTypes.MapStringBytesEntry
	42,  // 29: goproto.proto.test.TestAllTypes.map_string_nested_message:type_name -> goproto.proto.test.TestAllTypes.MapStringNestedMessageEntry
	43,  // 30: goproto.proto.test.TestAllTypes.map_string_nested_enum:type_name -> goproto.proto.test.TestAllTypes.MapStringNestedEnumEntry
	2,   // 31: goproto.proto.test.TestAllTypes.default_nested_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	0,   // 32: goproto.proto.test.TestAllTypes.default_foreign_enum:type_name -> goproto.proto.test.ForeignEnum
	24,  // 33: goproto.proto.test.TestAllTypes.oneof_nested_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	2,   // 34: goproto.proto.test.TestAllTypes.oneof_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	44,  // 35: goproto.proto.test.TestAllTypes.oneofgroup:type_name -> goproto.proto.test.TestAllTypes.OneofGroup
	45,  // 36: goproto.proto.test.OptionalGroup.optional_nested_message:type_name -> goproto.proto.test.TestAllExtensions.NestedMessage
	45,  // 37: goproto.proto.test.RepeatedGroup.optional_nested_message:type_name -> goproto.proto.test.TestAllExtensions.NestedMessage
	12,  // 38: goproto.proto.test.TestRequiredForeign.optional_message:type_name -> goproto.proto.test.TestRequired
	12,  // 39: goproto.proto.test.TestRequiredForeign.repeated_message:type_name -> goproto.proto.test.TestRequired
	46,  // 40: goproto.proto.test.TestRequiredForeign.map_message:type_name -> goproto.proto.test.TestRequiredForeign.MapMessageEntry
	12,  // 41: goproto.proto.test.TestRequiredForeign.oneof_message:type_name -> goproto.proto.test.TestRequired
	47,  // 42: goproto.proto.test.TestRequiredGroupFields.optionalgroup:type_name -> goproto.proto.test.TestRequiredGroupFields.OptionalGroup
	48,  // 43: goproto.proto.test.TestRequiredGroupFields.repeatedgroup:type_name -> goproto.proto.test.TestRequiredGroupFields.RepeatedGroup
	0,   // 44: goproto.proto.test.TestPackedTypes.packed_enum:type_name -> goproto.proto.test.ForeignEnum
	0,   // 45: goproto.proto.test.TestUnpackedTypes.unpacked_enum:type_name -> goproto.proto.test.ForeignEnum
	51,  // 46: goproto.proto.test.RemoteDefault.default:type_name -> goproto.proto.enums.Enum
	51,  // 47: goproto.proto.test.RemoteDefault.zero:type_name -> goproto.proto.enums.Enum
	51,  // 48: goproto.proto.test.RemoteDefault.one:type_name -> goproto.proto.enums.Enum
	51,  // 49: goproto.proto.test.RemoteDefault.elevent:type_name -> goproto.proto.enums.Enum
	51,  // 50: goproto.proto.test.RemoteDefault.seventeen:type_name -> goproto.proto.enums.Enum
	51,  // 51: goproto.proto.test.RemoteDefault.thirtyseven:type_name -> goproto.proto.enums.Enum
	51,  // 52: goproto.proto.test.RemoteDefault.sixtyseven:type_name -> goproto.proto.enums.Enum
	51,  // 53: goproto.proto.test.RemoteDefault.negative:type_name -> goproto.proto.enums.Enum
	4,   // 54: goproto.proto.test.TestAllTypes.NestedMessage.corecursive:type_name -> goproto.proto.test.TestAllTypes
	24,  // 55: goproto.proto.test.TestAllTypes.OptionalGroup.optional_nested_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	24,  // 56: goproto.proto.test.TestAllTypes.RepeatedGroup.optional_nested_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	24,  // 57: goproto.proto.test.TestAllTypes.MapStringNestedMessageEntry.value:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	2,   // 58: goproto.proto.test.TestAllTypes.MapStringNestedEnumEntry.value:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	8,   // 59: goproto.proto.test.TestAllExtensions.NestedMessage.corecursive:type_name -> goproto.proto.test.TestAllExtensions
	12,  // 60: goproto.proto.test.TestRequiredForeign.MapMessageEntry.value:type_name -> goproto.proto.test.TestRequired
	8,   // 61: goproto.proto.test.optional_int32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 62: goproto.proto.test.optional_int64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 63: goproto.proto.test.optional_uint32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 64: goproto.proto.test.optional_uint64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 65: goproto.proto.test.optional_sint32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 66: goproto.proto.test.optional_sint64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 67: goproto.proto.test.optional_fixed32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 68: goproto.proto.test.optional_fixed64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 69: goproto.proto.test.optional_sfixed32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 70: goproto.proto.test.optional_sfixed64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 71: goproto.proto.test.optional_float:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 72: goproto.proto.test.optional_double:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 73: goproto.proto.test.optional_bool:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 74: goproto.proto.test.optional_string:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 75: goproto.proto.test.optional_bytes:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 76: goproto.proto.test.optionalgroup:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 77: goproto.proto.test.optional_nested_message:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 78: goproto.proto.test.optional_nested_enum:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 79: goproto.proto.test.repeated_int32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 80: goproto.proto.test.repeated_int64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 81: goproto.proto.test.repeated_uint32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 82: goproto.proto.test.repeated_uint64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 83: goproto.proto.test.repeated_sint32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 84: goproto.proto.test.repeated_sint64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 85: goproto.proto.test.repeated_fixed32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 86: goproto.proto.test.repeated_fixed64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 87: goproto.proto.test.repeated_sfixed32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 88: goproto.proto.test.repeated_sfixed64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 89: goproto.proto.test.repeated_float:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 90: goproto.proto.test.repeated_double:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 91: goproto.proto.test.repeated_bool:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 92: goproto.proto.test.repeated_string:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 93: goproto.proto.test.repeated_bytes:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 94: goproto.proto.test.repeatedgroup:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 95: goproto.proto.test.repeated_nested_message:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 96: goproto.proto.test.repeated_nested_enum:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 97: goproto.proto.test.default_int32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 98: goproto.proto.test.default_int64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 99: goproto.proto.test.default_uint32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 100: goproto.proto.test.default_uint64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 101: goproto.proto.test.default_sint32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 102: goproto.proto.test.default_sint64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 103: goproto.proto.test.default_fixed32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 104: goproto.proto.test.default_fixed64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 105: goproto.proto.test.default_sfixed32:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 106: goproto.proto.test.default_sfixed64:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 107: goproto.proto.test.default_float:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 108: goproto.proto.test.default_double:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 109: goproto.proto.test.default_bool:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 110: goproto.proto.test.default_string:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 111: goproto.proto.test.default_bytes:extendee -> goproto.proto.test.TestAllExtensions
	18,  // 112: goproto.proto.test.packed_int32:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 113: goproto.proto.test.packed_int64:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 114: goproto.proto.test.packed_uint32:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 115: goproto.proto.test.packed_uint64:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 116: goproto.proto.test.packed_sint32:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 117: goproto.proto.test.packed_sint64:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 118: goproto.proto.test.packed_fixed32:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 119: goproto.proto.test.packed_fixed64:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 120: goproto.proto.test.packed_sfixed32:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 121: goproto.proto.test.packed_sfixed64:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 122: goproto.proto.test.packed_float:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 123: goproto.proto.test.packed_double:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 124: goproto.proto.test.packed_bool:extendee -> goproto.proto.test.TestPackedExtensions
	18,  // 125: goproto.proto.test.packed_enum:extendee -> goproto.proto.test.TestPackedExtensions
	19,  // 126: goproto.proto.test.unpacked_int32:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 127: goproto.proto.test.unpacked_int64:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 128: goproto.proto.test.unpacked_uint32:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 129: goproto.proto.test.unpacked_uint64:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 130: goproto.proto.test.unpacked_sint32:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 131: goproto.proto.test.unpacked_sint64:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 132: goproto.proto.test.unpacked_fixed32:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 133: goproto.proto.test.unpacked_fixed64:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 134: goproto.proto.test.unpacked_sfixed32:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 135: goproto.proto.test.unpacked_sfixed64:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 136: goproto.proto.test.unpacked_float:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 137: goproto.proto.test.unpacked_double:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 138: goproto.proto.test.unpacked_bool:extendee -> goproto.proto.test.TestUnpackedExtensions
	19,  // 139: goproto.proto.test.unpacked_enum:extendee -> goproto.proto.test.TestUnpackedExtensions
	8,   // 140: goproto.proto.test.TestNestedExtension.nested_string_extension:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 141: goproto.proto.test.TestRequired.single:extendee -> goproto.proto.test.TestAllExtensions
	8,   // 142: goproto.proto.test.TestRequired.multi:extendee -> goproto.proto.test.TestAllExtensions
	9,   // 143: goproto.proto.test.optionalgroup:type_name -> goproto.proto.test.OptionalGroup
	45,  // 144: goproto.proto.test.optional_nested_message:type_name -> goproto.proto.test.TestAllExtensions.NestedMessage
	2,   // 145: goproto.proto.test.optional_nested_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	10,  // 146: goproto.proto.test.repeatedgroup:type_name -> goproto.proto.test.RepeatedGroup
	45,  // 147: goproto.proto.test.repeated_nested_message:type_name -> goproto.proto.test.TestAllExtensions.NestedMessage
	2,   // 148: goproto.proto.test.repeated_nested_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	0,   // 149: goproto.proto.test.packed_enum:type_name -> goproto.proto.test.ForeignEnum
	0,   // 150: goproto.proto.test.unpacked_enum:type_name -> goproto.proto.test.ForeignEnum
	12,  // 151: goproto.proto.test.TestRequired.single:type_name -> goproto.proto.test.TestRequired
	12,  // 152: goproto.proto.test.TestRequired.multi:type_name -> goproto.proto.test.TestRequired
	20,  // 153: goproto.proto.test.TestService.Foo:input_type -> goproto.proto.test.FooRequest
	20,  // 154: goproto.proto.test.TestService.TestStream:input_type -> goproto.proto.test.FooRequest
	5,   // 155: goproto.proto.test.TestDeprecatedService.Deprecated:input_type -> goproto.proto.test.TestDeprecatedMessage
	21,  // 156: goproto.proto.test.TestService.Foo:output_type -> goproto.proto.test.FooResponse
	21,  // 157: goproto.proto.test.TestService.TestStream:output_type -> goproto.proto.test.FooResponse
	5,   // 158: goproto.proto.test.TestDeprecatedService.Deprecated:output_type -> goproto.proto.test.TestDeprecatedMessage
	156, // [156:159] is the sub-list for method output_type
	153, // [153:156] is the sub-list for method input_type
	143, // [143:153] is the sub-list for extension type_name
	61,  // [61:143] is the sub-list for extension extendee
	0,   // [0:61] is the sub-list for field type_name
}

func init() { file_internal_testprotos_test_test_proto_init() }
func file_internal_testprotos_test_test_proto_init() {
	if File_internal_testprotos_test_test_proto != nil {
		return
	}
	file_internal_testprotos_test_test_public_proto_init()
	file_internal_testprotos_test_test_import_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_test_test_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestDeprecatedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeignMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReservedFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllExtensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestNestedExtension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredForeign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredGroupFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestWeak); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.weakFields
			case 3:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestPackedTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestUnpackedTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestPackedExtensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestUnpackedExtensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FooRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FooResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeirdDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteDefault); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_OneofGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllExtensions_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredGroupFields_OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test_test_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredGroupFields_RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_internal_testprotos_test_test_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TestAllTypes_OneofUint32)(nil),
		(*TestAllTypes_OneofNestedMessage)(nil),
		(*TestAllTypes_OneofString)(nil),
		(*TestAllTypes_OneofBytes)(nil),
		(*TestAllTypes_OneofBool)(nil),
		(*TestAllTypes_OneofUint64)(nil),
		(*TestAllTypes_OneofFloat)(nil),
		(*TestAllTypes_OneofDouble)(nil),
		(*TestAllTypes_OneofEnum)(nil),
		(*TestAllTypes_Oneofgroup)(nil),
		(*TestAllTypes_OneofOptionalUint32)(nil),
	}
	file_internal_testprotos_test_test_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*TestDeprecatedMessage_DeprecatedOneofField)(nil),
	}
	file_internal_testprotos_test_test_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*TestRequiredForeign_OneofMessage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_test_test_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   45,
			NumExtensions: 82,
			NumServices:   2,
		},
		GoTypes:           file_internal_testprotos_test_test_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_test_test_proto_depIdxs,
		EnumInfos:         file_internal_testprotos_test_test_proto_enumTypes,
		MessageInfos:      file_internal_testprotos_test_test_proto_msgTypes,
		ExtensionInfos:    file_internal_testprotos_test_test_proto_extTypes,
	}.Build()
	File_internal_testprotos_test_test_proto = out.File
	file_internal_testprotos_test_test_proto_rawDesc = nil
	file_internal_testprotos_test_test_proto_goTypes = nil
	file_internal_testprotos_test_test_proto_depIdxs = nil
}
