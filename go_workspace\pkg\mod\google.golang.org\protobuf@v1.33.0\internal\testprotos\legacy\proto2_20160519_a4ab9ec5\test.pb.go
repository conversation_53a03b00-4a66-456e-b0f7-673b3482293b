// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go.
// source: proto2_20160519_a4ab9ec5/test.proto
// DO NOT EDIT!

/*
Package proto2_20160519_a4ab9ec5 is a generated protocol buffer package.

It is generated from these files:

	proto2_20160519_a4ab9ec5/test.proto

It has these top-level messages:

	SiblingMessage
	Message
*/
package proto2_20160519_a4ab9ec5

import proto "google.golang.org/protobuf/internal/protolegacy"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
const _ = proto.ProtoPackageIsVersion1

type SiblingEnum int32

const (
	SiblingEnum_ALPHA   SiblingEnum = 0
	SiblingEnum_BRAVO   SiblingEnum = 10
	SiblingEnum_CHARLIE SiblingEnum = 200
)

var SiblingEnum_name = map[int32]string{
	0:   "ALPHA",
	10:  "BRAVO",
	200: "CHARLIE",
}
var SiblingEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   10,
	"CHARLIE": 200,
}

func (x SiblingEnum) Enum() *SiblingEnum {
	p := new(SiblingEnum)
	*p = x
	return p
}
func (x SiblingEnum) String() string {
	return proto.EnumName(SiblingEnum_name, int32(x))
}
func (x *SiblingEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SiblingEnum_value, data, "SiblingEnum")
	if err != nil {
		return err
	}
	*x = SiblingEnum(value)
	return nil
}
func (SiblingEnum) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

type Message_ChildEnum int32

const (
	Message_ALPHA   Message_ChildEnum = 0
	Message_BRAVO   Message_ChildEnum = 1
	Message_CHARLIE Message_ChildEnum = 2
)

var Message_ChildEnum_name = map[int32]string{
	0: "ALPHA",
	1: "BRAVO",
	2: "CHARLIE",
}
var Message_ChildEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   1,
	"CHARLIE": 2,
}

func (x Message_ChildEnum) Enum() *Message_ChildEnum {
	p := new(Message_ChildEnum)
	*p = x
	return p
}
func (x Message_ChildEnum) String() string {
	return proto.EnumName(Message_ChildEnum_name, int32(x))
}
func (x *Message_ChildEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Message_ChildEnum_value, data, "Message_ChildEnum")
	if err != nil {
		return err
	}
	*x = Message_ChildEnum(value)
	return nil
}
func (Message_ChildEnum) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 0} }

type SiblingMessage struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	F4               *Message `protobuf:"bytes,4,opt,name=f4" json:"f4,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *SiblingMessage) Reset()                    { *m = SiblingMessage{} }
func (m *SiblingMessage) String() string            { return proto.CompactTextString(m) }
func (*SiblingMessage) ProtoMessage()               {}
func (*SiblingMessage) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *SiblingMessage) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *SiblingMessage) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *SiblingMessage) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func (m *SiblingMessage) GetF4() *Message {
	if m != nil {
		return m.F4
	}
	return nil
}

type Message struct {
	Namedgroup *Message_NamedGroup `protobuf:"group,1,opt,name=NamedGroup,json=namedgroup" json:"namedgroup,omitempty"`
	// Optional fields.
	OptionalBool           *bool                  `protobuf:"varint,100,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalInt32          *int32                 `protobuf:"varint,101,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalSint32         *int32                 `protobuf:"zigzag32,102,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalUint32         *uint32                `protobuf:"varint,103,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalInt64          *int64                 `protobuf:"varint,104,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalSint64         *int64                 `protobuf:"zigzag64,105,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalUint64         *uint64                `protobuf:"varint,106,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalFixed32        *uint32                `protobuf:"fixed32,107,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalSfixed32       *int32                 `protobuf:"fixed32,108,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalFloat          *float32               `protobuf:"fixed32,109,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalFixed64        *uint64                `protobuf:"fixed64,110,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed64       *int64                 `protobuf:"fixed64,111,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalDouble         *float64               `protobuf:"fixed64,112,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalString         *string                `protobuf:"bytes,113,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                 `protobuf:"bytes,114,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	OptionalChildEnum      *Message_ChildEnum     `protobuf:"varint,115,opt,name=optional_child_enum,json=optionalChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum" json:"optional_child_enum,omitempty"`
	OptionalChildMessage   *Message_ChildMessage  `protobuf:"bytes,116,opt,name=optional_child_message,json=optionalChildMessage" json:"optional_child_message,omitempty"`
	OptionalNamedGroup     *Message_NamedGroup    `protobuf:"bytes,117,opt,name=optional_named_group,json=optionalNamedGroup" json:"optional_named_group,omitempty"`
	OptionalSiblingEnum    *SiblingEnum           `protobuf:"varint,118,opt,name=optional_sibling_enum,json=optionalSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum" json:"optional_sibling_enum,omitempty"`
	OptionalSiblingMessage *SiblingMessage        `protobuf:"bytes,119,opt,name=optional_sibling_message,json=optionalSiblingMessage" json:"optional_sibling_message,omitempty"`
	Optionalgroup          *Message_OptionalGroup `protobuf:"group,120,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	// Optional default fields.
	DefaultedBool        *bool              `protobuf:"varint,200,opt,name=defaulted_bool,json=defaultedBool,def=1" json:"defaulted_bool,omitempty"`
	DefaultedInt32       *int32             `protobuf:"varint,201,opt,name=defaulted_int32,json=defaultedInt32,def=-12345" json:"defaulted_int32,omitempty"`
	DefaultedSint32      *int32             `protobuf:"zigzag32,202,opt,name=defaulted_sint32,json=defaultedSint32,def=-3200" json:"defaulted_sint32,omitempty"`
	DefaultedUint32      *uint32            `protobuf:"varint,203,opt,name=defaulted_uint32,json=defaultedUint32,def=3200" json:"defaulted_uint32,omitempty"`
	DefaultedInt64       *int64             `protobuf:"varint,204,opt,name=defaulted_int64,json=defaultedInt64,def=-123456789" json:"defaulted_int64,omitempty"`
	DefaultedSint64      *int64             `protobuf:"zigzag64,205,opt,name=defaulted_sint64,json=defaultedSint64,def=-6400" json:"defaulted_sint64,omitempty"`
	DefaultedUint64      *uint64            `protobuf:"varint,206,opt,name=defaulted_uint64,json=defaultedUint64,def=6400" json:"defaulted_uint64,omitempty"`
	DefaultedFixed32     *uint32            `protobuf:"fixed32,207,opt,name=defaulted_fixed32,json=defaultedFixed32,def=320000" json:"defaulted_fixed32,omitempty"`
	DefaultedSfixed32    *int32             `protobuf:"fixed32,208,opt,name=defaulted_sfixed32,json=defaultedSfixed32,def=-320000" json:"defaulted_sfixed32,omitempty"`
	DefaultedFloat       *float32           `protobuf:"fixed32,209,opt,name=defaulted_float,json=defaultedFloat,def=3.14159" json:"defaulted_float,omitempty"`
	DefaultedFixed64     *uint64            `protobuf:"fixed64,210,opt,name=defaulted_fixed64,json=defaultedFixed64,def=640000" json:"defaulted_fixed64,omitempty"`
	DefaultedSfixed64    *int64             `protobuf:"fixed64,211,opt,name=defaulted_sfixed64,json=defaultedSfixed64,def=-640000" json:"defaulted_sfixed64,omitempty"`
	DefaultedDouble      *float64           `protobuf:"fixed64,212,opt,name=defaulted_double,json=defaultedDouble,def=3.14159265359" json:"defaulted_double,omitempty"`
	DefaultedString      *string            `protobuf:"bytes,213,opt,name=defaulted_string,json=defaultedString,def=hello, \"world!\"\n" json:"defaulted_string,omitempty"`
	DefaultedBytes       []byte             `protobuf:"bytes,214,opt,name=defaulted_bytes,json=defaultedBytes,def=dead\\336\\255\\276\\357beef" json:"defaulted_bytes,omitempty"`
	DefaultedChildEnum   *Message_ChildEnum `protobuf:"varint,215,opt,name=defaulted_child_enum,json=defaultedChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum,def=0" json:"defaulted_child_enum,omitempty"`
	DefaultedSiblingEnum *SiblingEnum       `protobuf:"varint,216,opt,name=defaulted_sibling_enum,json=defaultedSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum,def=0" json:"defaulted_sibling_enum,omitempty"`
	// Required fields.
	RequiredBool           *bool                  `protobuf:"varint,300,req,name=required_bool,json=requiredBool" json:"required_bool,omitempty"`
	RequiredInt32          *int32                 `protobuf:"varint,301,req,name=required_int32,json=requiredInt32" json:"required_int32,omitempty"`
	RequiredSint32         *int32                 `protobuf:"zigzag32,302,req,name=required_sint32,json=requiredSint32" json:"required_sint32,omitempty"`
	RequiredUint32         *uint32                `protobuf:"varint,303,req,name=required_uint32,json=requiredUint32" json:"required_uint32,omitempty"`
	RequiredInt64          *int64                 `protobuf:"varint,304,req,name=required_int64,json=requiredInt64" json:"required_int64,omitempty"`
	RequiredSint64         *int64                 `protobuf:"zigzag64,305,req,name=required_sint64,json=requiredSint64" json:"required_sint64,omitempty"`
	RequiredUint64         *uint64                `protobuf:"varint,306,req,name=required_uint64,json=requiredUint64" json:"required_uint64,omitempty"`
	RequiredFixed32        *uint32                `protobuf:"fixed32,307,req,name=required_fixed32,json=requiredFixed32" json:"required_fixed32,omitempty"`
	RequiredSfixed32       *int32                 `protobuf:"fixed32,308,req,name=required_sfixed32,json=requiredSfixed32" json:"required_sfixed32,omitempty"`
	RequiredFloat          *float32               `protobuf:"fixed32,309,req,name=required_float,json=requiredFloat" json:"required_float,omitempty"`
	RequiredFixed64        *uint64                `protobuf:"fixed64,310,req,name=required_fixed64,json=requiredFixed64" json:"required_fixed64,omitempty"`
	RequiredSfixed64       *int64                 `protobuf:"fixed64,311,req,name=required_sfixed64,json=requiredSfixed64" json:"required_sfixed64,omitempty"`
	RequiredDouble         *float64               `protobuf:"fixed64,312,req,name=required_double,json=requiredDouble" json:"required_double,omitempty"`
	RequiredString         *string                `protobuf:"bytes,313,req,name=required_string,json=requiredString" json:"required_string,omitempty"`
	RequiredBytes          []byte                 `protobuf:"bytes,314,req,name=required_bytes,json=requiredBytes" json:"required_bytes,omitempty"`
	RequiredChildEnum      *Message_ChildEnum     `protobuf:"varint,315,req,name=required_child_enum,json=requiredChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum" json:"required_child_enum,omitempty"`
	RequiredChildMessage   *Message_ChildMessage  `protobuf:"bytes,316,req,name=required_child_message,json=requiredChildMessage" json:"required_child_message,omitempty"`
	RequiredNamedGroup     *Message_NamedGroup    `protobuf:"bytes,317,req,name=required_named_group,json=requiredNamedGroup" json:"required_named_group,omitempty"`
	RequiredSiblingEnum    *SiblingEnum           `protobuf:"varint,318,req,name=required_sibling_enum,json=requiredSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum" json:"required_sibling_enum,omitempty"`
	RequiredSiblingMessage *SiblingMessage        `protobuf:"bytes,319,req,name=required_sibling_message,json=requiredSiblingMessage" json:"required_sibling_message,omitempty"`
	Requiredgroup          *Message_RequiredGroup `protobuf:"group,320,req,name=RequiredGroup,json=requiredgroup" json:"requiredgroup,omitempty"`
	// Required default fields.
	RequiredDefaultedBool        *bool              `protobuf:"varint,400,req,name=required_defaulted_bool,json=requiredDefaultedBool,def=1" json:"required_defaulted_bool,omitempty"`
	RequiredDefaultedInt32       *int32             `protobuf:"varint,401,req,name=required_defaulted_int32,json=requiredDefaultedInt32,def=-12345" json:"required_defaulted_int32,omitempty"`
	RequiredDefaultedSint32      *int32             `protobuf:"zigzag32,402,req,name=required_defaulted_sint32,json=requiredDefaultedSint32,def=-3200" json:"required_defaulted_sint32,omitempty"`
	RequiredDefaultedUint32      *uint32            `protobuf:"varint,403,req,name=required_defaulted_uint32,json=requiredDefaultedUint32,def=3200" json:"required_defaulted_uint32,omitempty"`
	RequiredDefaultedInt64       *int64             `protobuf:"varint,404,req,name=required_defaulted_int64,json=requiredDefaultedInt64,def=-123456789" json:"required_defaulted_int64,omitempty"`
	RequiredDefaultedSint64      *int64             `protobuf:"zigzag64,405,req,name=required_defaulted_sint64,json=requiredDefaultedSint64,def=-6400" json:"required_defaulted_sint64,omitempty"`
	RequiredDefaultedUint64      *uint64            `protobuf:"varint,406,req,name=required_defaulted_uint64,json=requiredDefaultedUint64,def=6400" json:"required_defaulted_uint64,omitempty"`
	RequiredDefaultedFixed32     *uint32            `protobuf:"fixed32,407,req,name=required_defaulted_fixed32,json=requiredDefaultedFixed32,def=320000" json:"required_defaulted_fixed32,omitempty"`
	RequiredDefaultedSfixed32    *int32             `protobuf:"fixed32,408,req,name=required_defaulted_sfixed32,json=requiredDefaultedSfixed32,def=-320000" json:"required_defaulted_sfixed32,omitempty"`
	RequiredDefaultedFloat       *float32           `protobuf:"fixed32,409,req,name=required_defaulted_float,json=requiredDefaultedFloat,def=3.14159" json:"required_defaulted_float,omitempty"`
	RequiredDefaultedFixed64     *uint64            `protobuf:"fixed64,410,req,name=required_defaulted_fixed64,json=requiredDefaultedFixed64,def=640000" json:"required_defaulted_fixed64,omitempty"`
	RequiredDefaultedSfixed64    *int64             `protobuf:"fixed64,411,req,name=required_defaulted_sfixed64,json=requiredDefaultedSfixed64,def=-640000" json:"required_defaulted_sfixed64,omitempty"`
	RequiredDefaultedDouble      *float64           `protobuf:"fixed64,412,req,name=required_defaulted_double,json=requiredDefaultedDouble,def=3.14159265359" json:"required_defaulted_double,omitempty"`
	RequiredDefaultedString      *string            `protobuf:"bytes,413,req,name=required_defaulted_string,json=requiredDefaultedString,def=hello, \"world!\"\n" json:"required_defaulted_string,omitempty"`
	RequiredDefaultedBytes       []byte             `protobuf:"bytes,414,req,name=required_defaulted_bytes,json=requiredDefaultedBytes,def=dead\\336\\255\\276\\357beef" json:"required_defaulted_bytes,omitempty"`
	RequiredDefaultedChildEnum   *Message_ChildEnum `protobuf:"varint,415,req,name=required_defaulted_child_enum,json=requiredDefaultedChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum,def=0" json:"required_defaulted_child_enum,omitempty"`
	RequiredDefaultedSiblingEnum *SiblingEnum       `protobuf:"varint,416,req,name=required_defaulted_sibling_enum,json=requiredDefaultedSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum,def=0" json:"required_defaulted_sibling_enum,omitempty"`
	// Repeated fields.
	RepeatedBool           []bool                   `protobuf:"varint,500,rep,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedInt32          []int32                  `protobuf:"varint,501,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedSint32         []int32                  `protobuf:"zigzag32,502,rep,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedUint32         []uint32                 `protobuf:"varint,503,rep,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedInt64          []int64                  `protobuf:"varint,504,rep,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedSint64         []int64                  `protobuf:"zigzag64,505,rep,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedUint64         []uint64                 `protobuf:"varint,506,rep,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedFixed32        []uint32                 `protobuf:"fixed32,507,rep,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedSfixed32       []int32                  `protobuf:"fixed32,508,rep,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedFloat          []float32                `protobuf:"fixed32,509,rep,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedFixed64        []uint64                 `protobuf:"fixed64,510,rep,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed64       []int64                  `protobuf:"fixed64,511,rep,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedDouble         []float64                `protobuf:"fixed64,512,rep,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedString         []string                 `protobuf:"bytes,513,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                 `protobuf:"bytes,514,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	RepeatedChildEnum      []Message_ChildEnum      `protobuf:"varint,515,rep,name=repeated_child_enum,json=repeatedChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum" json:"repeated_child_enum,omitempty"`
	RepeatedChildMessage   []*Message_ChildMessage  `protobuf:"bytes,516,rep,name=repeated_child_message,json=repeatedChildMessage" json:"repeated_child_message,omitempty"`
	RepeatedNamedGroup     []*Message_NamedGroup    `protobuf:"bytes,517,rep,name=repeated_named_group,json=repeatedNamedGroup" json:"repeated_named_group,omitempty"`
	RepeatedSiblingEnum    []SiblingEnum            `protobuf:"varint,518,rep,name=repeated_sibling_enum,json=repeatedSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum" json:"repeated_sibling_enum,omitempty"`
	RepeatedSiblingMessage []*SiblingMessage        `protobuf:"bytes,519,rep,name=repeated_sibling_message,json=repeatedSiblingMessage" json:"repeated_sibling_message,omitempty"`
	Repeatedgroup          []*Message_RepeatedGroup `protobuf:"group,520,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
	// Map fields.
	MapBoolBool           map[bool]bool                  `protobuf:"bytes,600,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolInt32          map[bool]int32                 `protobuf:"bytes,601,rep,name=map_bool_int32,json=mapBoolInt32" json:"map_bool_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolSint32         map[bool]int32                 `protobuf:"bytes,602,rep,name=map_bool_sint32,json=mapBoolSint32" json:"map_bool_sint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapBoolUint32         map[bool]uint32                `protobuf:"bytes,603,rep,name=map_bool_uint32,json=mapBoolUint32" json:"map_bool_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolInt64          map[bool]int64                 `protobuf:"bytes,604,rep,name=map_bool_int64,json=mapBoolInt64" json:"map_bool_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolSint64         map[bool]int64                 `protobuf:"bytes,605,rep,name=map_bool_sint64,json=mapBoolSint64" json:"map_bool_sint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapBoolUint64         map[bool]uint64                `protobuf:"bytes,606,rep,name=map_bool_uint64,json=mapBoolUint64" json:"map_bool_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolFixed32        map[bool]uint32                `protobuf:"bytes,607,rep,name=map_bool_fixed32,json=mapBoolFixed32" json:"map_bool_fixed32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolSfixed32       map[bool]int32                 `protobuf:"bytes,608,rep,name=map_bool_sfixed32,json=mapBoolSfixed32" json:"map_bool_sfixed32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolFloat          map[bool]float32               `protobuf:"bytes,609,rep,name=map_bool_float,json=mapBoolFloat" json:"map_bool_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolFixed64        map[bool]uint64                `protobuf:"bytes,610,rep,name=map_bool_fixed64,json=mapBoolFixed64" json:"map_bool_fixed64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolSfixed64       map[bool]int64                 `protobuf:"bytes,611,rep,name=map_bool_sfixed64,json=mapBoolSfixed64" json:"map_bool_sfixed64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolDouble         map[bool]float64               `protobuf:"bytes,612,rep,name=map_bool_double,json=mapBoolDouble" json:"map_bool_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolString         map[bool]string                `protobuf:"bytes,613,rep,name=map_bool_string,json=mapBoolString" json:"map_bool_string,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolBytes          map[bool][]byte                `protobuf:"bytes,614,rep,name=map_bool_bytes,json=mapBoolBytes" json:"map_bool_bytes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolChildEnum      map[bool]Message_ChildEnum     `protobuf:"bytes,615,rep,name=map_bool_child_enum,json=mapBoolChildEnum" json:"map_bool_child_enum,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=google.golang.org.proto2_20160519.Message_ChildEnum"`
	MapBoolChildMessage   map[bool]*Message_ChildMessage `protobuf:"bytes,616,rep,name=map_bool_child_message,json=mapBoolChildMessage" json:"map_bool_child_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolNamedGroup     map[bool]*Message_NamedGroup   `protobuf:"bytes,617,rep,name=map_bool_named_group,json=mapBoolNamedGroup" json:"map_bool_named_group,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolSiblingEnum    map[bool]SiblingEnum           `protobuf:"bytes,618,rep,name=map_bool_sibling_enum,json=mapBoolSiblingEnum" json:"map_bool_sibling_enum,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=google.golang.org.proto2_20160519.SiblingEnum"`
	MapBoolSiblingMessage map[bool]*SiblingMessage       `protobuf:"bytes,619,rep,name=map_bool_sibling_message,json=mapBoolSiblingMessage" json:"map_bool_sibling_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapInt32Bool          map[int32]bool                 `protobuf:"bytes,620,rep,name=map_int32_bool,json=mapInt32Bool" json:"map_int32_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Bool         map[int32]bool                 `protobuf:"bytes,621,rep,name=map_sint32_bool,json=mapSint32Bool" json:"map_sint32_bool,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Bool         map[uint32]bool                `protobuf:"bytes,622,rep,name=map_uint32_bool,json=mapUint32Bool" json:"map_uint32_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Bool          map[int64]bool                 `protobuf:"bytes,623,rep,name=map_int64_bool,json=mapInt64Bool" json:"map_int64_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint64Bool         map[int64]bool                 `protobuf:"bytes,624,rep,name=map_sint64_bool,json=mapSint64Bool" json:"map_sint64_bool,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Bool         map[uint64]bool                `protobuf:"bytes,625,rep,name=map_uint64_bool,json=mapUint64Bool" json:"map_uint64_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapFixed32Bool        map[uint32]bool                `protobuf:"bytes,626,rep,name=map_fixed32_bool,json=mapFixed32Bool" json:"map_fixed32_bool,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringBool         map[string]bool                `protobuf:"bytes,627,rep,name=map_string_bool,json=mapStringBool" json:"map_string_bool,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// Oneof fields.
	//
	// Types that are valid to be assigned to OneofUnion:
	//	*Message_OneofBool
	//	*Message_OneofInt32
	//	*Message_OneofSint32
	//	*Message_OneofUint32
	//	*Message_OneofInt64
	//	*Message_OneofSint64
	//	*Message_OneofUint64
	//	*Message_OneofFixed32
	//	*Message_OneofSfixed32
	//	*Message_OneofFloat
	//	*Message_OneofFixed64
	//	*Message_OneofSfixed64
	//	*Message_OneofDouble
	//	*Message_OneofString
	//	*Message_OneofBytes
	//	*Message_OneofChildEnum
	//	*Message_OneofChildMessage
	//	*Message_OneofNamedGroup
	//	*Message_OneofSiblingEnum
	//	*Message_OneofSiblingMessage
	//	*Message_Oneofgroup
	//	*Message_OneofString1
	//	*Message_OneofString2
	//	*Message_OneofString3
	OneofUnion isMessage_OneofUnion `protobuf_oneof:"oneof_union"`
	// Oneof default fields.
	//
	// Types that are valid to be assigned to OneofDefaultedUnion:
	//	*Message_OneofDefaultedBool
	//	*Message_OneofDefaultedInt32
	//	*Message_OneofDefaultedSint32
	//	*Message_OneofDefaultedUint32
	//	*Message_OneofDefaultedInt64
	//	*Message_OneofDefaultedSint64
	//	*Message_OneofDefaultedUint64
	//	*Message_OneofDefaultedFixed32
	//	*Message_OneofDefaultedSfixed32
	//	*Message_OneofDefaultedFloat
	//	*Message_OneofDefaultedFixed64
	//	*Message_OneofDefaultedSfixed64
	//	*Message_OneofDefaultedDouble
	//	*Message_OneofDefaultedString
	//	*Message_OneofDefaultedBytes
	//	*Message_OneofDefaultedChildEnum
	//	*Message_OneofDefaultedSiblingEnum
	OneofDefaultedUnion isMessage_OneofDefaultedUnion `protobuf_oneof:"oneof_defaulted_union"`
	XXX_extensions      map[int32]proto.Extension     `json:"-"`
	XXX_unrecognized    []byte                        `json:"-"`
}

func (m *Message) Reset()                    { *m = Message{} }
func (m *Message) String() string            { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()               {}
func (*Message) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

var extRange_Message = []proto.ExtensionRange{
	{10000, 536870911},
}

func (*Message) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_Message
}
func (m *Message) ExtensionMap() map[int32]proto.Extension {
	if m.XXX_extensions == nil {
		m.XXX_extensions = make(map[int32]proto.Extension)
	}
	return m.XXX_extensions
}

const Default_Message_DefaultedBool bool = true
const Default_Message_DefaultedInt32 int32 = -12345
const Default_Message_DefaultedSint32 int32 = -3200
const Default_Message_DefaultedUint32 uint32 = 3200
const Default_Message_DefaultedInt64 int64 = -123456789
const Default_Message_DefaultedSint64 int64 = -6400
const Default_Message_DefaultedUint64 uint64 = 6400
const Default_Message_DefaultedFixed32 uint32 = 320000
const Default_Message_DefaultedSfixed32 int32 = -320000
const Default_Message_DefaultedFloat float32 = 3.14159
const Default_Message_DefaultedFixed64 uint64 = 640000
const Default_Message_DefaultedSfixed64 int64 = -640000
const Default_Message_DefaultedDouble float64 = 3.14159265359
const Default_Message_DefaultedString string = "hello, \"world!\"\n"

var Default_Message_DefaultedBytes []byte = []byte("dead\\336\\255\\276\\357beef")

const Default_Message_DefaultedChildEnum Message_ChildEnum = Message_ALPHA
const Default_Message_DefaultedSiblingEnum SiblingEnum = SiblingEnum_ALPHA
const Default_Message_RequiredDefaultedBool bool = true
const Default_Message_RequiredDefaultedInt32 int32 = -12345
const Default_Message_RequiredDefaultedSint32 int32 = -3200
const Default_Message_RequiredDefaultedUint32 uint32 = 3200
const Default_Message_RequiredDefaultedInt64 int64 = -123456789
const Default_Message_RequiredDefaultedSint64 int64 = -6400
const Default_Message_RequiredDefaultedUint64 uint64 = 6400
const Default_Message_RequiredDefaultedFixed32 uint32 = 320000
const Default_Message_RequiredDefaultedSfixed32 int32 = -320000
const Default_Message_RequiredDefaultedFloat float32 = 3.14159
const Default_Message_RequiredDefaultedFixed64 uint64 = 640000
const Default_Message_RequiredDefaultedSfixed64 int64 = -640000
const Default_Message_RequiredDefaultedDouble float64 = 3.14159265359
const Default_Message_RequiredDefaultedString string = "hello, \"world!\"\n"

var Default_Message_RequiredDefaultedBytes []byte = []byte("dead\\336\\255\\276\\357beef")

const Default_Message_RequiredDefaultedChildEnum Message_ChildEnum = Message_ALPHA
const Default_Message_RequiredDefaultedSiblingEnum SiblingEnum = SiblingEnum_ALPHA
const Default_Message_OneofDefaultedBool bool = true
const Default_Message_OneofDefaultedInt32 int32 = -12345
const Default_Message_OneofDefaultedSint32 int32 = -3200
const Default_Message_OneofDefaultedUint32 uint32 = 3200
const Default_Message_OneofDefaultedInt64 int64 = -123456789
const Default_Message_OneofDefaultedSint64 int64 = -6400
const Default_Message_OneofDefaultedUint64 uint64 = 6400
const Default_Message_OneofDefaultedFixed32 uint32 = 320000
const Default_Message_OneofDefaultedSfixed32 int32 = -320000
const Default_Message_OneofDefaultedFloat float32 = 3.14159
const Default_Message_OneofDefaultedFixed64 uint64 = 640000
const Default_Message_OneofDefaultedSfixed64 int64 = -640000
const Default_Message_OneofDefaultedDouble float64 = 3.14159265359
const Default_Message_OneofDefaultedString string = "hello, \"world!\"\n"

var Default_Message_OneofDefaultedBytes []byte = []byte("dead\\336\\255\\276\\357beef")

const Default_Message_OneofDefaultedChildEnum Message_ChildEnum = Message_ALPHA
const Default_Message_OneofDefaultedSiblingEnum SiblingEnum = SiblingEnum_ALPHA

type isMessage_OneofUnion interface{ isMessage_OneofUnion() }
type isMessage_OneofDefaultedUnion interface{ isMessage_OneofDefaultedUnion() }

type Message_OneofBool struct {
	OneofBool bool `protobuf:"varint,700,opt,name=oneof_bool,json=oneofBool,oneof"`
}
type Message_OneofInt32 struct {
	OneofInt32 int32 `protobuf:"varint,701,opt,name=oneof_int32,json=oneofInt32,oneof"`
}
type Message_OneofSint32 struct {
	OneofSint32 int32 `protobuf:"zigzag32,702,opt,name=oneof_sint32,json=oneofSint32,oneof"`
}
type Message_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,703,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}
type Message_OneofInt64 struct {
	OneofInt64 int64 `protobuf:"varint,704,opt,name=oneof_int64,json=oneofInt64,oneof"`
}
type Message_OneofSint64 struct {
	OneofSint64 int64 `protobuf:"zigzag64,705,opt,name=oneof_sint64,json=oneofSint64,oneof"`
}
type Message_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,706,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}
type Message_OneofFixed32 struct {
	OneofFixed32 uint32 `protobuf:"fixed32,707,opt,name=oneof_fixed32,json=oneofFixed32,oneof"`
}
type Message_OneofSfixed32 struct {
	OneofSfixed32 int32 `protobuf:"fixed32,708,opt,name=oneof_sfixed32,json=oneofSfixed32,oneof"`
}
type Message_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,709,opt,name=oneof_float,json=oneofFloat,oneof"`
}
type Message_OneofFixed64 struct {
	OneofFixed64 uint64 `protobuf:"fixed64,710,opt,name=oneof_fixed64,json=oneofFixed64,oneof"`
}
type Message_OneofSfixed64 struct {
	OneofSfixed64 int64 `protobuf:"fixed64,711,opt,name=oneof_sfixed64,json=oneofSfixed64,oneof"`
}
type Message_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,712,opt,name=oneof_double,json=oneofDouble,oneof"`
}
type Message_OneofString struct {
	OneofString string `protobuf:"bytes,713,opt,name=oneof_string,json=oneofString,oneof"`
}
type Message_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,714,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}
type Message_OneofChildEnum struct {
	OneofChildEnum Message_ChildEnum `protobuf:"varint,715,opt,name=oneof_child_enum,json=oneofChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum,oneof"`
}
type Message_OneofChildMessage struct {
	OneofChildMessage *Message_ChildMessage `protobuf:"bytes,716,opt,name=oneof_child_message,json=oneofChildMessage,oneof"`
}
type Message_OneofNamedGroup struct {
	OneofNamedGroup *Message_NamedGroup `protobuf:"bytes,717,opt,name=oneof_named_group,json=oneofNamedGroup,oneof"`
}
type Message_OneofSiblingEnum struct {
	OneofSiblingEnum SiblingEnum `protobuf:"varint,718,opt,name=oneof_sibling_enum,json=oneofSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum,oneof"`
}
type Message_OneofSiblingMessage struct {
	OneofSiblingMessage *SiblingMessage `protobuf:"bytes,719,opt,name=oneof_sibling_message,json=oneofSiblingMessage,oneof"`
}
type Message_Oneofgroup struct {
	Oneofgroup *Message_OneofGroup `protobuf:"group,720,opt,name=OneofGroup,json=oneofgroup,oneof"`
}
type Message_OneofString1 struct {
	OneofString1 string `protobuf:"bytes,721,opt,name=oneof_string1,json=oneofString1,oneof"`
}
type Message_OneofString2 struct {
	OneofString2 string `protobuf:"bytes,722,opt,name=oneof_string2,json=oneofString2,oneof"`
}
type Message_OneofString3 struct {
	OneofString3 string `protobuf:"bytes,723,opt,name=oneof_string3,json=oneofString3,oneof"`
}
type Message_OneofDefaultedBool struct {
	OneofDefaultedBool bool `protobuf:"varint,800,opt,name=oneof_defaulted_bool,json=oneofDefaultedBool,oneof,def=1"`
}
type Message_OneofDefaultedInt32 struct {
	OneofDefaultedInt32 int32 `protobuf:"varint,801,opt,name=oneof_defaulted_int32,json=oneofDefaultedInt32,oneof,def=-12345"`
}
type Message_OneofDefaultedSint32 struct {
	OneofDefaultedSint32 int32 `protobuf:"zigzag32,802,opt,name=oneof_defaulted_sint32,json=oneofDefaultedSint32,oneof,def=-3200"`
}
type Message_OneofDefaultedUint32 struct {
	OneofDefaultedUint32 uint32 `protobuf:"varint,803,opt,name=oneof_defaulted_uint32,json=oneofDefaultedUint32,oneof,def=3200"`
}
type Message_OneofDefaultedInt64 struct {
	OneofDefaultedInt64 int64 `protobuf:"varint,804,opt,name=oneof_defaulted_int64,json=oneofDefaultedInt64,oneof,def=-123456789"`
}
type Message_OneofDefaultedSint64 struct {
	OneofDefaultedSint64 int64 `protobuf:"zigzag64,805,opt,name=oneof_defaulted_sint64,json=oneofDefaultedSint64,oneof,def=-6400"`
}
type Message_OneofDefaultedUint64 struct {
	OneofDefaultedUint64 uint64 `protobuf:"varint,806,opt,name=oneof_defaulted_uint64,json=oneofDefaultedUint64,oneof,def=6400"`
}
type Message_OneofDefaultedFixed32 struct {
	OneofDefaultedFixed32 uint32 `protobuf:"fixed32,807,opt,name=oneof_defaulted_fixed32,json=oneofDefaultedFixed32,oneof,def=320000"`
}
type Message_OneofDefaultedSfixed32 struct {
	OneofDefaultedSfixed32 int32 `protobuf:"fixed32,808,opt,name=oneof_defaulted_sfixed32,json=oneofDefaultedSfixed32,oneof,def=-320000"`
}
type Message_OneofDefaultedFloat struct {
	OneofDefaultedFloat float32 `protobuf:"fixed32,809,opt,name=oneof_defaulted_float,json=oneofDefaultedFloat,oneof,def=3.14159"`
}
type Message_OneofDefaultedFixed64 struct {
	OneofDefaultedFixed64 uint64 `protobuf:"fixed64,810,opt,name=oneof_defaulted_fixed64,json=oneofDefaultedFixed64,oneof,def=640000"`
}
type Message_OneofDefaultedSfixed64 struct {
	OneofDefaultedSfixed64 int64 `protobuf:"fixed64,811,opt,name=oneof_defaulted_sfixed64,json=oneofDefaultedSfixed64,oneof,def=-640000"`
}
type Message_OneofDefaultedDouble struct {
	OneofDefaultedDouble float64 `protobuf:"fixed64,812,opt,name=oneof_defaulted_double,json=oneofDefaultedDouble,oneof,def=3.14159265359"`
}
type Message_OneofDefaultedString struct {
	OneofDefaultedString string `protobuf:"bytes,813,opt,name=oneof_defaulted_string,json=oneofDefaultedString,oneof,def=hello, \"world!\"\n"`
}
type Message_OneofDefaultedBytes struct {
	OneofDefaultedBytes []byte `protobuf:"bytes,814,opt,name=oneof_defaulted_bytes,json=oneofDefaultedBytes,oneof,def=dead\\336\\255\\276\\357beef"`
}
type Message_OneofDefaultedChildEnum struct {
	OneofDefaultedChildEnum Message_ChildEnum `protobuf:"varint,815,opt,name=oneof_defaulted_child_enum,json=oneofDefaultedChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum,oneof,def=0"`
}
type Message_OneofDefaultedSiblingEnum struct {
	OneofDefaultedSiblingEnum SiblingEnum `protobuf:"varint,816,opt,name=oneof_defaulted_sibling_enum,json=oneofDefaultedSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum,oneof,def=0"`
}

func (*Message_OneofBool) isMessage_OneofUnion()                          {}
func (*Message_OneofInt32) isMessage_OneofUnion()                         {}
func (*Message_OneofSint32) isMessage_OneofUnion()                        {}
func (*Message_OneofUint32) isMessage_OneofUnion()                        {}
func (*Message_OneofInt64) isMessage_OneofUnion()                         {}
func (*Message_OneofSint64) isMessage_OneofUnion()                        {}
func (*Message_OneofUint64) isMessage_OneofUnion()                        {}
func (*Message_OneofFixed32) isMessage_OneofUnion()                       {}
func (*Message_OneofSfixed32) isMessage_OneofUnion()                      {}
func (*Message_OneofFloat) isMessage_OneofUnion()                         {}
func (*Message_OneofFixed64) isMessage_OneofUnion()                       {}
func (*Message_OneofSfixed64) isMessage_OneofUnion()                      {}
func (*Message_OneofDouble) isMessage_OneofUnion()                        {}
func (*Message_OneofString) isMessage_OneofUnion()                        {}
func (*Message_OneofBytes) isMessage_OneofUnion()                         {}
func (*Message_OneofChildEnum) isMessage_OneofUnion()                     {}
func (*Message_OneofChildMessage) isMessage_OneofUnion()                  {}
func (*Message_OneofNamedGroup) isMessage_OneofUnion()                    {}
func (*Message_OneofSiblingEnum) isMessage_OneofUnion()                   {}
func (*Message_OneofSiblingMessage) isMessage_OneofUnion()                {}
func (*Message_Oneofgroup) isMessage_OneofUnion()                         {}
func (*Message_OneofString1) isMessage_OneofUnion()                       {}
func (*Message_OneofString2) isMessage_OneofUnion()                       {}
func (*Message_OneofString3) isMessage_OneofUnion()                       {}
func (*Message_OneofDefaultedBool) isMessage_OneofDefaultedUnion()        {}
func (*Message_OneofDefaultedInt32) isMessage_OneofDefaultedUnion()       {}
func (*Message_OneofDefaultedSint32) isMessage_OneofDefaultedUnion()      {}
func (*Message_OneofDefaultedUint32) isMessage_OneofDefaultedUnion()      {}
func (*Message_OneofDefaultedInt64) isMessage_OneofDefaultedUnion()       {}
func (*Message_OneofDefaultedSint64) isMessage_OneofDefaultedUnion()      {}
func (*Message_OneofDefaultedUint64) isMessage_OneofDefaultedUnion()      {}
func (*Message_OneofDefaultedFixed32) isMessage_OneofDefaultedUnion()     {}
func (*Message_OneofDefaultedSfixed32) isMessage_OneofDefaultedUnion()    {}
func (*Message_OneofDefaultedFloat) isMessage_OneofDefaultedUnion()       {}
func (*Message_OneofDefaultedFixed64) isMessage_OneofDefaultedUnion()     {}
func (*Message_OneofDefaultedSfixed64) isMessage_OneofDefaultedUnion()    {}
func (*Message_OneofDefaultedDouble) isMessage_OneofDefaultedUnion()      {}
func (*Message_OneofDefaultedString) isMessage_OneofDefaultedUnion()      {}
func (*Message_OneofDefaultedBytes) isMessage_OneofDefaultedUnion()       {}
func (*Message_OneofDefaultedChildEnum) isMessage_OneofDefaultedUnion()   {}
func (*Message_OneofDefaultedSiblingEnum) isMessage_OneofDefaultedUnion() {}

func (m *Message) GetOneofUnion() isMessage_OneofUnion {
	if m != nil {
		return m.OneofUnion
	}
	return nil
}
func (m *Message) GetOneofDefaultedUnion() isMessage_OneofDefaultedUnion {
	if m != nil {
		return m.OneofDefaultedUnion
	}
	return nil
}

func (m *Message) GetNamedgroup() *Message_NamedGroup {
	if m != nil {
		return m.Namedgroup
	}
	return nil
}

func (m *Message) GetOptionalBool() bool {
	if m != nil && m.OptionalBool != nil {
		return *m.OptionalBool
	}
	return false
}

func (m *Message) GetOptionalInt32() int32 {
	if m != nil && m.OptionalInt32 != nil {
		return *m.OptionalInt32
	}
	return 0
}

func (m *Message) GetOptionalSint32() int32 {
	if m != nil && m.OptionalSint32 != nil {
		return *m.OptionalSint32
	}
	return 0
}

func (m *Message) GetOptionalUint32() uint32 {
	if m != nil && m.OptionalUint32 != nil {
		return *m.OptionalUint32
	}
	return 0
}

func (m *Message) GetOptionalInt64() int64 {
	if m != nil && m.OptionalInt64 != nil {
		return *m.OptionalInt64
	}
	return 0
}

func (m *Message) GetOptionalSint64() int64 {
	if m != nil && m.OptionalSint64 != nil {
		return *m.OptionalSint64
	}
	return 0
}

func (m *Message) GetOptionalUint64() uint64 {
	if m != nil && m.OptionalUint64 != nil {
		return *m.OptionalUint64
	}
	return 0
}

func (m *Message) GetOptionalFixed32() uint32 {
	if m != nil && m.OptionalFixed32 != nil {
		return *m.OptionalFixed32
	}
	return 0
}

func (m *Message) GetOptionalSfixed32() int32 {
	if m != nil && m.OptionalSfixed32 != nil {
		return *m.OptionalSfixed32
	}
	return 0
}

func (m *Message) GetOptionalFloat() float32 {
	if m != nil && m.OptionalFloat != nil {
		return *m.OptionalFloat
	}
	return 0
}

func (m *Message) GetOptionalFixed64() uint64 {
	if m != nil && m.OptionalFixed64 != nil {
		return *m.OptionalFixed64
	}
	return 0
}

func (m *Message) GetOptionalSfixed64() int64 {
	if m != nil && m.OptionalSfixed64 != nil {
		return *m.OptionalSfixed64
	}
	return 0
}

func (m *Message) GetOptionalDouble() float64 {
	if m != nil && m.OptionalDouble != nil {
		return *m.OptionalDouble
	}
	return 0
}

func (m *Message) GetOptionalString() string {
	if m != nil && m.OptionalString != nil {
		return *m.OptionalString
	}
	return ""
}

func (m *Message) GetOptionalBytes() []byte {
	if m != nil {
		return m.OptionalBytes
	}
	return nil
}

func (m *Message) GetOptionalChildEnum() Message_ChildEnum {
	if m != nil && m.OptionalChildEnum != nil {
		return *m.OptionalChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOptionalChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.OptionalChildMessage
	}
	return nil
}

func (m *Message) GetOptionalNamedGroup() *Message_NamedGroup {
	if m != nil {
		return m.OptionalNamedGroup
	}
	return nil
}

func (m *Message) GetOptionalSiblingEnum() SiblingEnum {
	if m != nil && m.OptionalSiblingEnum != nil {
		return *m.OptionalSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOptionalSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.OptionalSiblingMessage
	}
	return nil
}

func (m *Message) GetOptionalgroup() *Message_OptionalGroup {
	if m != nil {
		return m.Optionalgroup
	}
	return nil
}

func (m *Message) GetDefaultedBool() bool {
	if m != nil && m.DefaultedBool != nil {
		return *m.DefaultedBool
	}
	return Default_Message_DefaultedBool
}

func (m *Message) GetDefaultedInt32() int32 {
	if m != nil && m.DefaultedInt32 != nil {
		return *m.DefaultedInt32
	}
	return Default_Message_DefaultedInt32
}

func (m *Message) GetDefaultedSint32() int32 {
	if m != nil && m.DefaultedSint32 != nil {
		return *m.DefaultedSint32
	}
	return Default_Message_DefaultedSint32
}

func (m *Message) GetDefaultedUint32() uint32 {
	if m != nil && m.DefaultedUint32 != nil {
		return *m.DefaultedUint32
	}
	return Default_Message_DefaultedUint32
}

func (m *Message) GetDefaultedInt64() int64 {
	if m != nil && m.DefaultedInt64 != nil {
		return *m.DefaultedInt64
	}
	return Default_Message_DefaultedInt64
}

func (m *Message) GetDefaultedSint64() int64 {
	if m != nil && m.DefaultedSint64 != nil {
		return *m.DefaultedSint64
	}
	return Default_Message_DefaultedSint64
}

func (m *Message) GetDefaultedUint64() uint64 {
	if m != nil && m.DefaultedUint64 != nil {
		return *m.DefaultedUint64
	}
	return Default_Message_DefaultedUint64
}

func (m *Message) GetDefaultedFixed32() uint32 {
	if m != nil && m.DefaultedFixed32 != nil {
		return *m.DefaultedFixed32
	}
	return Default_Message_DefaultedFixed32
}

func (m *Message) GetDefaultedSfixed32() int32 {
	if m != nil && m.DefaultedSfixed32 != nil {
		return *m.DefaultedSfixed32
	}
	return Default_Message_DefaultedSfixed32
}

func (m *Message) GetDefaultedFloat() float32 {
	if m != nil && m.DefaultedFloat != nil {
		return *m.DefaultedFloat
	}
	return Default_Message_DefaultedFloat
}

func (m *Message) GetDefaultedFixed64() uint64 {
	if m != nil && m.DefaultedFixed64 != nil {
		return *m.DefaultedFixed64
	}
	return Default_Message_DefaultedFixed64
}

func (m *Message) GetDefaultedSfixed64() int64 {
	if m != nil && m.DefaultedSfixed64 != nil {
		return *m.DefaultedSfixed64
	}
	return Default_Message_DefaultedSfixed64
}

func (m *Message) GetDefaultedDouble() float64 {
	if m != nil && m.DefaultedDouble != nil {
		return *m.DefaultedDouble
	}
	return Default_Message_DefaultedDouble
}

func (m *Message) GetDefaultedString() string {
	if m != nil && m.DefaultedString != nil {
		return *m.DefaultedString
	}
	return Default_Message_DefaultedString
}

func (m *Message) GetDefaultedBytes() []byte {
	if m != nil && m.DefaultedBytes != nil {
		return m.DefaultedBytes
	}
	return append([]byte(nil), Default_Message_DefaultedBytes...)
}

func (m *Message) GetDefaultedChildEnum() Message_ChildEnum {
	if m != nil && m.DefaultedChildEnum != nil {
		return *m.DefaultedChildEnum
	}
	return Default_Message_DefaultedChildEnum
}

func (m *Message) GetDefaultedSiblingEnum() SiblingEnum {
	if m != nil && m.DefaultedSiblingEnum != nil {
		return *m.DefaultedSiblingEnum
	}
	return Default_Message_DefaultedSiblingEnum
}

func (m *Message) GetRequiredBool() bool {
	if m != nil && m.RequiredBool != nil {
		return *m.RequiredBool
	}
	return false
}

func (m *Message) GetRequiredInt32() int32 {
	if m != nil && m.RequiredInt32 != nil {
		return *m.RequiredInt32
	}
	return 0
}

func (m *Message) GetRequiredSint32() int32 {
	if m != nil && m.RequiredSint32 != nil {
		return *m.RequiredSint32
	}
	return 0
}

func (m *Message) GetRequiredUint32() uint32 {
	if m != nil && m.RequiredUint32 != nil {
		return *m.RequiredUint32
	}
	return 0
}

func (m *Message) GetRequiredInt64() int64 {
	if m != nil && m.RequiredInt64 != nil {
		return *m.RequiredInt64
	}
	return 0
}

func (m *Message) GetRequiredSint64() int64 {
	if m != nil && m.RequiredSint64 != nil {
		return *m.RequiredSint64
	}
	return 0
}

func (m *Message) GetRequiredUint64() uint64 {
	if m != nil && m.RequiredUint64 != nil {
		return *m.RequiredUint64
	}
	return 0
}

func (m *Message) GetRequiredFixed32() uint32 {
	if m != nil && m.RequiredFixed32 != nil {
		return *m.RequiredFixed32
	}
	return 0
}

func (m *Message) GetRequiredSfixed32() int32 {
	if m != nil && m.RequiredSfixed32 != nil {
		return *m.RequiredSfixed32
	}
	return 0
}

func (m *Message) GetRequiredFloat() float32 {
	if m != nil && m.RequiredFloat != nil {
		return *m.RequiredFloat
	}
	return 0
}

func (m *Message) GetRequiredFixed64() uint64 {
	if m != nil && m.RequiredFixed64 != nil {
		return *m.RequiredFixed64
	}
	return 0
}

func (m *Message) GetRequiredSfixed64() int64 {
	if m != nil && m.RequiredSfixed64 != nil {
		return *m.RequiredSfixed64
	}
	return 0
}

func (m *Message) GetRequiredDouble() float64 {
	if m != nil && m.RequiredDouble != nil {
		return *m.RequiredDouble
	}
	return 0
}

func (m *Message) GetRequiredString() string {
	if m != nil && m.RequiredString != nil {
		return *m.RequiredString
	}
	return ""
}

func (m *Message) GetRequiredBytes() []byte {
	if m != nil {
		return m.RequiredBytes
	}
	return nil
}

func (m *Message) GetRequiredChildEnum() Message_ChildEnum {
	if m != nil && m.RequiredChildEnum != nil {
		return *m.RequiredChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetRequiredChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.RequiredChildMessage
	}
	return nil
}

func (m *Message) GetRequiredNamedGroup() *Message_NamedGroup {
	if m != nil {
		return m.RequiredNamedGroup
	}
	return nil
}

func (m *Message) GetRequiredSiblingEnum() SiblingEnum {
	if m != nil && m.RequiredSiblingEnum != nil {
		return *m.RequiredSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetRequiredSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.RequiredSiblingMessage
	}
	return nil
}

func (m *Message) GetRequiredgroup() *Message_RequiredGroup {
	if m != nil {
		return m.Requiredgroup
	}
	return nil
}

func (m *Message) GetRequiredDefaultedBool() bool {
	if m != nil && m.RequiredDefaultedBool != nil {
		return *m.RequiredDefaultedBool
	}
	return Default_Message_RequiredDefaultedBool
}

func (m *Message) GetRequiredDefaultedInt32() int32 {
	if m != nil && m.RequiredDefaultedInt32 != nil {
		return *m.RequiredDefaultedInt32
	}
	return Default_Message_RequiredDefaultedInt32
}

func (m *Message) GetRequiredDefaultedSint32() int32 {
	if m != nil && m.RequiredDefaultedSint32 != nil {
		return *m.RequiredDefaultedSint32
	}
	return Default_Message_RequiredDefaultedSint32
}

func (m *Message) GetRequiredDefaultedUint32() uint32 {
	if m != nil && m.RequiredDefaultedUint32 != nil {
		return *m.RequiredDefaultedUint32
	}
	return Default_Message_RequiredDefaultedUint32
}

func (m *Message) GetRequiredDefaultedInt64() int64 {
	if m != nil && m.RequiredDefaultedInt64 != nil {
		return *m.RequiredDefaultedInt64
	}
	return Default_Message_RequiredDefaultedInt64
}

func (m *Message) GetRequiredDefaultedSint64() int64 {
	if m != nil && m.RequiredDefaultedSint64 != nil {
		return *m.RequiredDefaultedSint64
	}
	return Default_Message_RequiredDefaultedSint64
}

func (m *Message) GetRequiredDefaultedUint64() uint64 {
	if m != nil && m.RequiredDefaultedUint64 != nil {
		return *m.RequiredDefaultedUint64
	}
	return Default_Message_RequiredDefaultedUint64
}

func (m *Message) GetRequiredDefaultedFixed32() uint32 {
	if m != nil && m.RequiredDefaultedFixed32 != nil {
		return *m.RequiredDefaultedFixed32
	}
	return Default_Message_RequiredDefaultedFixed32
}

func (m *Message) GetRequiredDefaultedSfixed32() int32 {
	if m != nil && m.RequiredDefaultedSfixed32 != nil {
		return *m.RequiredDefaultedSfixed32
	}
	return Default_Message_RequiredDefaultedSfixed32
}

func (m *Message) GetRequiredDefaultedFloat() float32 {
	if m != nil && m.RequiredDefaultedFloat != nil {
		return *m.RequiredDefaultedFloat
	}
	return Default_Message_RequiredDefaultedFloat
}

func (m *Message) GetRequiredDefaultedFixed64() uint64 {
	if m != nil && m.RequiredDefaultedFixed64 != nil {
		return *m.RequiredDefaultedFixed64
	}
	return Default_Message_RequiredDefaultedFixed64
}

func (m *Message) GetRequiredDefaultedSfixed64() int64 {
	if m != nil && m.RequiredDefaultedSfixed64 != nil {
		return *m.RequiredDefaultedSfixed64
	}
	return Default_Message_RequiredDefaultedSfixed64
}

func (m *Message) GetRequiredDefaultedDouble() float64 {
	if m != nil && m.RequiredDefaultedDouble != nil {
		return *m.RequiredDefaultedDouble
	}
	return Default_Message_RequiredDefaultedDouble
}

func (m *Message) GetRequiredDefaultedString() string {
	if m != nil && m.RequiredDefaultedString != nil {
		return *m.RequiredDefaultedString
	}
	return Default_Message_RequiredDefaultedString
}

func (m *Message) GetRequiredDefaultedBytes() []byte {
	if m != nil && m.RequiredDefaultedBytes != nil {
		return m.RequiredDefaultedBytes
	}
	return append([]byte(nil), Default_Message_RequiredDefaultedBytes...)
}

func (m *Message) GetRequiredDefaultedChildEnum() Message_ChildEnum {
	if m != nil && m.RequiredDefaultedChildEnum != nil {
		return *m.RequiredDefaultedChildEnum
	}
	return Default_Message_RequiredDefaultedChildEnum
}

func (m *Message) GetRequiredDefaultedSiblingEnum() SiblingEnum {
	if m != nil && m.RequiredDefaultedSiblingEnum != nil {
		return *m.RequiredDefaultedSiblingEnum
	}
	return Default_Message_RequiredDefaultedSiblingEnum
}

func (m *Message) GetRepeatedBool() []bool {
	if m != nil {
		return m.RepeatedBool
	}
	return nil
}

func (m *Message) GetRepeatedInt32() []int32 {
	if m != nil {
		return m.RepeatedInt32
	}
	return nil
}

func (m *Message) GetRepeatedSint32() []int32 {
	if m != nil {
		return m.RepeatedSint32
	}
	return nil
}

func (m *Message) GetRepeatedUint32() []uint32 {
	if m != nil {
		return m.RepeatedUint32
	}
	return nil
}

func (m *Message) GetRepeatedInt64() []int64 {
	if m != nil {
		return m.RepeatedInt64
	}
	return nil
}

func (m *Message) GetRepeatedSint64() []int64 {
	if m != nil {
		return m.RepeatedSint64
	}
	return nil
}

func (m *Message) GetRepeatedUint64() []uint64 {
	if m != nil {
		return m.RepeatedUint64
	}
	return nil
}

func (m *Message) GetRepeatedFixed32() []uint32 {
	if m != nil {
		return m.RepeatedFixed32
	}
	return nil
}

func (m *Message) GetRepeatedSfixed32() []int32 {
	if m != nil {
		return m.RepeatedSfixed32
	}
	return nil
}

func (m *Message) GetRepeatedFloat() []float32 {
	if m != nil {
		return m.RepeatedFloat
	}
	return nil
}

func (m *Message) GetRepeatedFixed64() []uint64 {
	if m != nil {
		return m.RepeatedFixed64
	}
	return nil
}

func (m *Message) GetRepeatedSfixed64() []int64 {
	if m != nil {
		return m.RepeatedSfixed64
	}
	return nil
}

func (m *Message) GetRepeatedDouble() []float64 {
	if m != nil {
		return m.RepeatedDouble
	}
	return nil
}

func (m *Message) GetRepeatedString() []string {
	if m != nil {
		return m.RepeatedString
	}
	return nil
}

func (m *Message) GetRepeatedBytes() [][]byte {
	if m != nil {
		return m.RepeatedBytes
	}
	return nil
}

func (m *Message) GetRepeatedChildEnum() []Message_ChildEnum {
	if m != nil {
		return m.RepeatedChildEnum
	}
	return nil
}

func (m *Message) GetRepeatedChildMessage() []*Message_ChildMessage {
	if m != nil {
		return m.RepeatedChildMessage
	}
	return nil
}

func (m *Message) GetRepeatedNamedGroup() []*Message_NamedGroup {
	if m != nil {
		return m.RepeatedNamedGroup
	}
	return nil
}

func (m *Message) GetRepeatedSiblingEnum() []SiblingEnum {
	if m != nil {
		return m.RepeatedSiblingEnum
	}
	return nil
}

func (m *Message) GetRepeatedSiblingMessage() []*SiblingMessage {
	if m != nil {
		return m.RepeatedSiblingMessage
	}
	return nil
}

func (m *Message) GetRepeatedgroup() []*Message_RepeatedGroup {
	if m != nil {
		return m.Repeatedgroup
	}
	return nil
}

func (m *Message) GetMapBoolBool() map[bool]bool {
	if m != nil {
		return m.MapBoolBool
	}
	return nil
}

func (m *Message) GetMapBoolInt32() map[bool]int32 {
	if m != nil {
		return m.MapBoolInt32
	}
	return nil
}

func (m *Message) GetMapBoolSint32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSint32
	}
	return nil
}

func (m *Message) GetMapBoolUint32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolUint32
	}
	return nil
}

func (m *Message) GetMapBoolInt64() map[bool]int64 {
	if m != nil {
		return m.MapBoolInt64
	}
	return nil
}

func (m *Message) GetMapBoolSint64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSint64
	}
	return nil
}

func (m *Message) GetMapBoolUint64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolUint64
	}
	return nil
}

func (m *Message) GetMapBoolFixed32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolFixed32
	}
	return nil
}

func (m *Message) GetMapBoolSfixed32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSfixed32
	}
	return nil
}

func (m *Message) GetMapBoolFloat() map[bool]float32 {
	if m != nil {
		return m.MapBoolFloat
	}
	return nil
}

func (m *Message) GetMapBoolFixed64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolFixed64
	}
	return nil
}

func (m *Message) GetMapBoolSfixed64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSfixed64
	}
	return nil
}

func (m *Message) GetMapBoolDouble() map[bool]float64 {
	if m != nil {
		return m.MapBoolDouble
	}
	return nil
}

func (m *Message) GetMapBoolString() map[bool]string {
	if m != nil {
		return m.MapBoolString
	}
	return nil
}

func (m *Message) GetMapBoolBytes() map[bool][]byte {
	if m != nil {
		return m.MapBoolBytes
	}
	return nil
}

func (m *Message) GetMapBoolChildEnum() map[bool]Message_ChildEnum {
	if m != nil {
		return m.MapBoolChildEnum
	}
	return nil
}

func (m *Message) GetMapBoolChildMessage() map[bool]*Message_ChildMessage {
	if m != nil {
		return m.MapBoolChildMessage
	}
	return nil
}

func (m *Message) GetMapBoolNamedGroup() map[bool]*Message_NamedGroup {
	if m != nil {
		return m.MapBoolNamedGroup
	}
	return nil
}

func (m *Message) GetMapBoolSiblingEnum() map[bool]SiblingEnum {
	if m != nil {
		return m.MapBoolSiblingEnum
	}
	return nil
}

func (m *Message) GetMapBoolSiblingMessage() map[bool]*SiblingMessage {
	if m != nil {
		return m.MapBoolSiblingMessage
	}
	return nil
}

func (m *Message) GetMapInt32Bool() map[int32]bool {
	if m != nil {
		return m.MapInt32Bool
	}
	return nil
}

func (m *Message) GetMapSint32Bool() map[int32]bool {
	if m != nil {
		return m.MapSint32Bool
	}
	return nil
}

func (m *Message) GetMapUint32Bool() map[uint32]bool {
	if m != nil {
		return m.MapUint32Bool
	}
	return nil
}

func (m *Message) GetMapInt64Bool() map[int64]bool {
	if m != nil {
		return m.MapInt64Bool
	}
	return nil
}

func (m *Message) GetMapSint64Bool() map[int64]bool {
	if m != nil {
		return m.MapSint64Bool
	}
	return nil
}

func (m *Message) GetMapUint64Bool() map[uint64]bool {
	if m != nil {
		return m.MapUint64Bool
	}
	return nil
}

func (m *Message) GetMapFixed32Bool() map[uint32]bool {
	if m != nil {
		return m.MapFixed32Bool
	}
	return nil
}

func (m *Message) GetMapStringBool() map[string]bool {
	if m != nil {
		return m.MapStringBool
	}
	return nil
}

func (m *Message) GetOneofBool() bool {
	if x, ok := m.GetOneofUnion().(*Message_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (m *Message) GetOneofInt32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt32); ok {
		return x.OneofInt32
	}
	return 0
}

func (m *Message) GetOneofSint32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint32); ok {
		return x.OneofSint32
	}
	return 0
}

func (m *Message) GetOneofUint32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (m *Message) GetOneofInt64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt64); ok {
		return x.OneofInt64
	}
	return 0
}

func (m *Message) GetOneofSint64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint64); ok {
		return x.OneofSint64
	}
	return 0
}

func (m *Message) GetOneofUint64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (m *Message) GetOneofFixed32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed32); ok {
		return x.OneofFixed32
	}
	return 0
}

func (m *Message) GetOneofSfixed32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed32); ok {
		return x.OneofSfixed32
	}
	return 0
}

func (m *Message) GetOneofFloat() float32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (m *Message) GetOneofFixed64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed64); ok {
		return x.OneofFixed64
	}
	return 0
}

func (m *Message) GetOneofSfixed64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed64); ok {
		return x.OneofSfixed64
	}
	return 0
}

func (m *Message) GetOneofDouble() float64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (m *Message) GetOneofString() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (m *Message) GetOneofBytes() []byte {
	if x, ok := m.GetOneofUnion().(*Message_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (m *Message) GetOneofChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildEnum); ok {
		return x.OneofChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOneofChildMessage() *Message_ChildMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildMessage); ok {
		return x.OneofChildMessage
	}
	return nil
}

func (m *Message) GetOneofNamedGroup() *Message_NamedGroup {
	if x, ok := m.GetOneofUnion().(*Message_OneofNamedGroup); ok {
		return x.OneofNamedGroup
	}
	return nil
}

func (m *Message) GetOneofSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingEnum); ok {
		return x.OneofSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOneofSiblingMessage() *SiblingMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingMessage); ok {
		return x.OneofSiblingMessage
	}
	return nil
}

func (m *Message) GetOneofgroup() *Message_OneofGroup {
	if x, ok := m.GetOneofUnion().(*Message_Oneofgroup); ok {
		return x.Oneofgroup
	}
	return nil
}

func (m *Message) GetOneofString1() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString1); ok {
		return x.OneofString1
	}
	return ""
}

func (m *Message) GetOneofString2() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString2); ok {
		return x.OneofString2
	}
	return ""
}

func (m *Message) GetOneofString3() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString3); ok {
		return x.OneofString3
	}
	return ""
}

func (m *Message) GetOneofDefaultedBool() bool {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedBool); ok {
		return x.OneofDefaultedBool
	}
	return Default_Message_OneofDefaultedBool
}

func (m *Message) GetOneofDefaultedInt32() int32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedInt32); ok {
		return x.OneofDefaultedInt32
	}
	return Default_Message_OneofDefaultedInt32
}

func (m *Message) GetOneofDefaultedSint32() int32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSint32); ok {
		return x.OneofDefaultedSint32
	}
	return Default_Message_OneofDefaultedSint32
}

func (m *Message) GetOneofDefaultedUint32() uint32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedUint32); ok {
		return x.OneofDefaultedUint32
	}
	return Default_Message_OneofDefaultedUint32
}

func (m *Message) GetOneofDefaultedInt64() int64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedInt64); ok {
		return x.OneofDefaultedInt64
	}
	return Default_Message_OneofDefaultedInt64
}

func (m *Message) GetOneofDefaultedSint64() int64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSint64); ok {
		return x.OneofDefaultedSint64
	}
	return Default_Message_OneofDefaultedSint64
}

func (m *Message) GetOneofDefaultedUint64() uint64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedUint64); ok {
		return x.OneofDefaultedUint64
	}
	return Default_Message_OneofDefaultedUint64
}

func (m *Message) GetOneofDefaultedFixed32() uint32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedFixed32); ok {
		return x.OneofDefaultedFixed32
	}
	return Default_Message_OneofDefaultedFixed32
}

func (m *Message) GetOneofDefaultedSfixed32() int32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSfixed32); ok {
		return x.OneofDefaultedSfixed32
	}
	return Default_Message_OneofDefaultedSfixed32
}

func (m *Message) GetOneofDefaultedFloat() float32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedFloat); ok {
		return x.OneofDefaultedFloat
	}
	return Default_Message_OneofDefaultedFloat
}

func (m *Message) GetOneofDefaultedFixed64() uint64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedFixed64); ok {
		return x.OneofDefaultedFixed64
	}
	return Default_Message_OneofDefaultedFixed64
}

func (m *Message) GetOneofDefaultedSfixed64() int64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSfixed64); ok {
		return x.OneofDefaultedSfixed64
	}
	return Default_Message_OneofDefaultedSfixed64
}

func (m *Message) GetOneofDefaultedDouble() float64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedDouble); ok {
		return x.OneofDefaultedDouble
	}
	return Default_Message_OneofDefaultedDouble
}

func (m *Message) GetOneofDefaultedString() string {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedString); ok {
		return x.OneofDefaultedString
	}
	return Default_Message_OneofDefaultedString
}

func (m *Message) GetOneofDefaultedBytes() []byte {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedBytes); ok {
		return x.OneofDefaultedBytes
	}
	return append([]byte(nil), Default_Message_OneofDefaultedBytes...)
}

func (m *Message) GetOneofDefaultedChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedChildEnum); ok {
		return x.OneofDefaultedChildEnum
	}
	return Default_Message_OneofDefaultedChildEnum
}

func (m *Message) GetOneofDefaultedSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSiblingEnum); ok {
		return x.OneofDefaultedSiblingEnum
	}
	return Default_Message_OneofDefaultedSiblingEnum
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*Message) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _Message_OneofMarshaler, _Message_OneofUnmarshaler, _Message_OneofSizer, []interface{}{
		(*Message_OneofBool)(nil),
		(*Message_OneofInt32)(nil),
		(*Message_OneofSint32)(nil),
		(*Message_OneofUint32)(nil),
		(*Message_OneofInt64)(nil),
		(*Message_OneofSint64)(nil),
		(*Message_OneofUint64)(nil),
		(*Message_OneofFixed32)(nil),
		(*Message_OneofSfixed32)(nil),
		(*Message_OneofFloat)(nil),
		(*Message_OneofFixed64)(nil),
		(*Message_OneofSfixed64)(nil),
		(*Message_OneofDouble)(nil),
		(*Message_OneofString)(nil),
		(*Message_OneofBytes)(nil),
		(*Message_OneofChildEnum)(nil),
		(*Message_OneofChildMessage)(nil),
		(*Message_OneofNamedGroup)(nil),
		(*Message_OneofSiblingEnum)(nil),
		(*Message_OneofSiblingMessage)(nil),
		(*Message_Oneofgroup)(nil),
		(*Message_OneofString1)(nil),
		(*Message_OneofString2)(nil),
		(*Message_OneofString3)(nil),
		(*Message_OneofDefaultedBool)(nil),
		(*Message_OneofDefaultedInt32)(nil),
		(*Message_OneofDefaultedSint32)(nil),
		(*Message_OneofDefaultedUint32)(nil),
		(*Message_OneofDefaultedInt64)(nil),
		(*Message_OneofDefaultedSint64)(nil),
		(*Message_OneofDefaultedUint64)(nil),
		(*Message_OneofDefaultedFixed32)(nil),
		(*Message_OneofDefaultedSfixed32)(nil),
		(*Message_OneofDefaultedFloat)(nil),
		(*Message_OneofDefaultedFixed64)(nil),
		(*Message_OneofDefaultedSfixed64)(nil),
		(*Message_OneofDefaultedDouble)(nil),
		(*Message_OneofDefaultedString)(nil),
		(*Message_OneofDefaultedBytes)(nil),
		(*Message_OneofDefaultedChildEnum)(nil),
		(*Message_OneofDefaultedSiblingEnum)(nil),
	}
}

func _Message_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*Message)
	// oneof_union
	switch x := m.OneofUnion.(type) {
	case *Message_OneofBool:
		t := uint64(0)
		if x.OneofBool {
			t = 1
		}
		b.EncodeVarint(700<<3 | proto.WireVarint)
		b.EncodeVarint(t)
	case *Message_OneofInt32:
		b.EncodeVarint(701<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofInt32))
	case *Message_OneofSint32:
		b.EncodeVarint(702<<3 | proto.WireVarint)
		b.EncodeZigzag32(uint64(x.OneofSint32))
	case *Message_OneofUint32:
		b.EncodeVarint(703<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofUint32))
	case *Message_OneofInt64:
		b.EncodeVarint(704<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofInt64))
	case *Message_OneofSint64:
		b.EncodeVarint(705<<3 | proto.WireVarint)
		b.EncodeZigzag64(uint64(x.OneofSint64))
	case *Message_OneofUint64:
		b.EncodeVarint(706<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofUint64))
	case *Message_OneofFixed32:
		b.EncodeVarint(707<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofFixed32))
	case *Message_OneofSfixed32:
		b.EncodeVarint(708<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofSfixed32))
	case *Message_OneofFloat:
		b.EncodeVarint(709<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(math.Float32bits(x.OneofFloat)))
	case *Message_OneofFixed64:
		b.EncodeVarint(710<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofFixed64))
	case *Message_OneofSfixed64:
		b.EncodeVarint(711<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofSfixed64))
	case *Message_OneofDouble:
		b.EncodeVarint(712<<3 | proto.WireFixed64)
		b.EncodeFixed64(math.Float64bits(x.OneofDouble))
	case *Message_OneofString:
		b.EncodeVarint(713<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString)
	case *Message_OneofBytes:
		b.EncodeVarint(714<<3 | proto.WireBytes)
		b.EncodeRawBytes(x.OneofBytes)
	case *Message_OneofChildEnum:
		b.EncodeVarint(715<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofChildEnum))
	case *Message_OneofChildMessage:
		b.EncodeVarint(716<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofChildMessage); err != nil {
			return err
		}
	case *Message_OneofNamedGroup:
		b.EncodeVarint(717<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofNamedGroup); err != nil {
			return err
		}
	case *Message_OneofSiblingEnum:
		b.EncodeVarint(718<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofSiblingEnum))
	case *Message_OneofSiblingMessage:
		b.EncodeVarint(719<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofSiblingMessage); err != nil {
			return err
		}
	case *Message_Oneofgroup:
		b.EncodeVarint(720<<3 | proto.WireStartGroup)
		if err := b.Marshal(x.Oneofgroup); err != nil {
			return err
		}
		b.EncodeVarint(720<<3 | proto.WireEndGroup)
	case *Message_OneofString1:
		b.EncodeVarint(721<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString1)
	case *Message_OneofString2:
		b.EncodeVarint(722<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString2)
	case *Message_OneofString3:
		b.EncodeVarint(723<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString3)
	case nil:
	default:
		return fmt.Errorf("Message.OneofUnion has unexpected type %T", x)
	}
	// oneof_defaulted_union
	switch x := m.OneofDefaultedUnion.(type) {
	case *Message_OneofDefaultedBool:
		t := uint64(0)
		if x.OneofDefaultedBool {
			t = 1
		}
		b.EncodeVarint(800<<3 | proto.WireVarint)
		b.EncodeVarint(t)
	case *Message_OneofDefaultedInt32:
		b.EncodeVarint(801<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofDefaultedInt32))
	case *Message_OneofDefaultedSint32:
		b.EncodeVarint(802<<3 | proto.WireVarint)
		b.EncodeZigzag32(uint64(x.OneofDefaultedSint32))
	case *Message_OneofDefaultedUint32:
		b.EncodeVarint(803<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofDefaultedUint32))
	case *Message_OneofDefaultedInt64:
		b.EncodeVarint(804<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofDefaultedInt64))
	case *Message_OneofDefaultedSint64:
		b.EncodeVarint(805<<3 | proto.WireVarint)
		b.EncodeZigzag64(uint64(x.OneofDefaultedSint64))
	case *Message_OneofDefaultedUint64:
		b.EncodeVarint(806<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofDefaultedUint64))
	case *Message_OneofDefaultedFixed32:
		b.EncodeVarint(807<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofDefaultedFixed32))
	case *Message_OneofDefaultedSfixed32:
		b.EncodeVarint(808<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofDefaultedSfixed32))
	case *Message_OneofDefaultedFloat:
		b.EncodeVarint(809<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(math.Float32bits(x.OneofDefaultedFloat)))
	case *Message_OneofDefaultedFixed64:
		b.EncodeVarint(810<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofDefaultedFixed64))
	case *Message_OneofDefaultedSfixed64:
		b.EncodeVarint(811<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofDefaultedSfixed64))
	case *Message_OneofDefaultedDouble:
		b.EncodeVarint(812<<3 | proto.WireFixed64)
		b.EncodeFixed64(math.Float64bits(x.OneofDefaultedDouble))
	case *Message_OneofDefaultedString:
		b.EncodeVarint(813<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofDefaultedString)
	case *Message_OneofDefaultedBytes:
		b.EncodeVarint(814<<3 | proto.WireBytes)
		b.EncodeRawBytes(x.OneofDefaultedBytes)
	case *Message_OneofDefaultedChildEnum:
		b.EncodeVarint(815<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofDefaultedChildEnum))
	case *Message_OneofDefaultedSiblingEnum:
		b.EncodeVarint(816<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofDefaultedSiblingEnum))
	case nil:
	default:
		return fmt.Errorf("Message.OneofDefaultedUnion has unexpected type %T", x)
	}
	return nil
}

func _Message_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*Message)
	switch tag {
	case 700: // oneof_union.oneof_bool
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofBool{x != 0}
		return true, err
	case 701: // oneof_union.oneof_int32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofInt32{int32(x)}
		return true, err
	case 702: // oneof_union.oneof_sint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag32()
		m.OneofUnion = &Message_OneofSint32{int32(x)}
		return true, err
	case 703: // oneof_union.oneof_uint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofUint32{uint32(x)}
		return true, err
	case 704: // oneof_union.oneof_int64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofInt64{int64(x)}
		return true, err
	case 705: // oneof_union.oneof_sint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag64()
		m.OneofUnion = &Message_OneofSint64{int64(x)}
		return true, err
	case 706: // oneof_union.oneof_uint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofUint64{x}
		return true, err
	case 707: // oneof_union.oneof_fixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofFixed32{uint32(x)}
		return true, err
	case 708: // oneof_union.oneof_sfixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofSfixed32{int32(x)}
		return true, err
	case 709: // oneof_union.oneof_float
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofFloat{math.Float32frombits(uint32(x))}
		return true, err
	case 710: // oneof_union.oneof_fixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofFixed64{x}
		return true, err
	case 711: // oneof_union.oneof_sfixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofSfixed64{int64(x)}
		return true, err
	case 712: // oneof_union.oneof_double
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofDouble{math.Float64frombits(x)}
		return true, err
	case 713: // oneof_union.oneof_string
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString{x}
		return true, err
	case 714: // oneof_union.oneof_bytes
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeRawBytes(true)
		m.OneofUnion = &Message_OneofBytes{x}
		return true, err
	case 715: // oneof_union.oneof_child_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofChildEnum{Message_ChildEnum(x)}
		return true, err
	case 716: // oneof_union.oneof_child_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Message_ChildMessage)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofChildMessage{msg}
		return true, err
	case 717: // oneof_union.oneof_named_group
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Message_NamedGroup)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofNamedGroup{msg}
		return true, err
	case 718: // oneof_union.oneof_sibling_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofSiblingEnum{SiblingEnum(x)}
		return true, err
	case 719: // oneof_union.oneof_sibling_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SiblingMessage)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofSiblingMessage{msg}
		return true, err
	case 720: // oneof_union.oneofgroup
		if wire != proto.WireStartGroup {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Message_OneofGroup)
		err := b.DecodeGroup(msg)
		m.OneofUnion = &Message_Oneofgroup{msg}
		return true, err
	case 721: // oneof_union.oneof_string1
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString1{x}
		return true, err
	case 722: // oneof_union.oneof_string2
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString2{x}
		return true, err
	case 723: // oneof_union.oneof_string3
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString3{x}
		return true, err
	case 800: // oneof_defaulted_union.oneof_defaulted_bool
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedBool{x != 0}
		return true, err
	case 801: // oneof_defaulted_union.oneof_defaulted_int32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedInt32{int32(x)}
		return true, err
	case 802: // oneof_defaulted_union.oneof_defaulted_sint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag32()
		m.OneofDefaultedUnion = &Message_OneofDefaultedSint32{int32(x)}
		return true, err
	case 803: // oneof_defaulted_union.oneof_defaulted_uint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedUint32{uint32(x)}
		return true, err
	case 804: // oneof_defaulted_union.oneof_defaulted_int64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedInt64{int64(x)}
		return true, err
	case 805: // oneof_defaulted_union.oneof_defaulted_sint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag64()
		m.OneofDefaultedUnion = &Message_OneofDefaultedSint64{int64(x)}
		return true, err
	case 806: // oneof_defaulted_union.oneof_defaulted_uint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedUint64{x}
		return true, err
	case 807: // oneof_defaulted_union.oneof_defaulted_fixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofDefaultedUnion = &Message_OneofDefaultedFixed32{uint32(x)}
		return true, err
	case 808: // oneof_defaulted_union.oneof_defaulted_sfixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofDefaultedUnion = &Message_OneofDefaultedSfixed32{int32(x)}
		return true, err
	case 809: // oneof_defaulted_union.oneof_defaulted_float
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofDefaultedUnion = &Message_OneofDefaultedFloat{math.Float32frombits(uint32(x))}
		return true, err
	case 810: // oneof_defaulted_union.oneof_defaulted_fixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofDefaultedUnion = &Message_OneofDefaultedFixed64{x}
		return true, err
	case 811: // oneof_defaulted_union.oneof_defaulted_sfixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofDefaultedUnion = &Message_OneofDefaultedSfixed64{int64(x)}
		return true, err
	case 812: // oneof_defaulted_union.oneof_defaulted_double
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofDefaultedUnion = &Message_OneofDefaultedDouble{math.Float64frombits(x)}
		return true, err
	case 813: // oneof_defaulted_union.oneof_defaulted_string
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofDefaultedUnion = &Message_OneofDefaultedString{x}
		return true, err
	case 814: // oneof_defaulted_union.oneof_defaulted_bytes
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeRawBytes(true)
		m.OneofDefaultedUnion = &Message_OneofDefaultedBytes{x}
		return true, err
	case 815: // oneof_defaulted_union.oneof_defaulted_child_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedChildEnum{Message_ChildEnum(x)}
		return true, err
	case 816: // oneof_defaulted_union.oneof_defaulted_sibling_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofDefaultedUnion = &Message_OneofDefaultedSiblingEnum{SiblingEnum(x)}
		return true, err
	default:
		return false, nil
	}
}

func _Message_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*Message)
	// oneof_union
	switch x := m.OneofUnion.(type) {
	case *Message_OneofBool:
		n += proto.SizeVarint(700<<3 | proto.WireVarint)
		n += 1
	case *Message_OneofInt32:
		n += proto.SizeVarint(701<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofInt32))
	case *Message_OneofSint32:
		n += proto.SizeVarint(702<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64((uint32(x.OneofSint32) << 1) ^ uint32((int32(x.OneofSint32) >> 31))))
	case *Message_OneofUint32:
		n += proto.SizeVarint(703<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofUint32))
	case *Message_OneofInt64:
		n += proto.SizeVarint(704<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofInt64))
	case *Message_OneofSint64:
		n += proto.SizeVarint(705<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(uint64(x.OneofSint64<<1) ^ uint64((int64(x.OneofSint64) >> 63))))
	case *Message_OneofUint64:
		n += proto.SizeVarint(706<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofUint64))
	case *Message_OneofFixed32:
		n += proto.SizeVarint(707<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofSfixed32:
		n += proto.SizeVarint(708<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofFloat:
		n += proto.SizeVarint(709<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofFixed64:
		n += proto.SizeVarint(710<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofSfixed64:
		n += proto.SizeVarint(711<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofDouble:
		n += proto.SizeVarint(712<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofString:
		n += proto.SizeVarint(713<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString)))
		n += len(x.OneofString)
	case *Message_OneofBytes:
		n += proto.SizeVarint(714<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofBytes)))
		n += len(x.OneofBytes)
	case *Message_OneofChildEnum:
		n += proto.SizeVarint(715<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofChildEnum))
	case *Message_OneofChildMessage:
		s := proto.Size(x.OneofChildMessage)
		n += proto.SizeVarint(716<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_OneofNamedGroup:
		s := proto.Size(x.OneofNamedGroup)
		n += proto.SizeVarint(717<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_OneofSiblingEnum:
		n += proto.SizeVarint(718<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofSiblingEnum))
	case *Message_OneofSiblingMessage:
		s := proto.Size(x.OneofSiblingMessage)
		n += proto.SizeVarint(719<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_Oneofgroup:
		n += proto.SizeVarint(720<<3 | proto.WireStartGroup)
		n += proto.Size(x.Oneofgroup)
		n += proto.SizeVarint(720<<3 | proto.WireEndGroup)
	case *Message_OneofString1:
		n += proto.SizeVarint(721<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString1)))
		n += len(x.OneofString1)
	case *Message_OneofString2:
		n += proto.SizeVarint(722<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString2)))
		n += len(x.OneofString2)
	case *Message_OneofString3:
		n += proto.SizeVarint(723<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString3)))
		n += len(x.OneofString3)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	// oneof_defaulted_union
	switch x := m.OneofDefaultedUnion.(type) {
	case *Message_OneofDefaultedBool:
		n += proto.SizeVarint(800<<3 | proto.WireVarint)
		n += 1
	case *Message_OneofDefaultedInt32:
		n += proto.SizeVarint(801<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofDefaultedInt32))
	case *Message_OneofDefaultedSint32:
		n += proto.SizeVarint(802<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64((uint32(x.OneofDefaultedSint32) << 1) ^ uint32((int32(x.OneofDefaultedSint32) >> 31))))
	case *Message_OneofDefaultedUint32:
		n += proto.SizeVarint(803<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofDefaultedUint32))
	case *Message_OneofDefaultedInt64:
		n += proto.SizeVarint(804<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofDefaultedInt64))
	case *Message_OneofDefaultedSint64:
		n += proto.SizeVarint(805<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(uint64(x.OneofDefaultedSint64<<1) ^ uint64((int64(x.OneofDefaultedSint64) >> 63))))
	case *Message_OneofDefaultedUint64:
		n += proto.SizeVarint(806<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofDefaultedUint64))
	case *Message_OneofDefaultedFixed32:
		n += proto.SizeVarint(807<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofDefaultedSfixed32:
		n += proto.SizeVarint(808<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofDefaultedFloat:
		n += proto.SizeVarint(809<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofDefaultedFixed64:
		n += proto.SizeVarint(810<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofDefaultedSfixed64:
		n += proto.SizeVarint(811<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofDefaultedDouble:
		n += proto.SizeVarint(812<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofDefaultedString:
		n += proto.SizeVarint(813<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofDefaultedString)))
		n += len(x.OneofDefaultedString)
	case *Message_OneofDefaultedBytes:
		n += proto.SizeVarint(814<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofDefaultedBytes)))
		n += len(x.OneofDefaultedBytes)
	case *Message_OneofDefaultedChildEnum:
		n += proto.SizeVarint(815<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofDefaultedChildEnum))
	case *Message_OneofDefaultedSiblingEnum:
		n += proto.SizeVarint(816<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofDefaultedSiblingEnum))
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

var E_Message_ExtensionOptionalBool = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*bool)(nil),
	Field:         10000,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_bool",
	Tag:           "varint,10000,opt,name=extension_optional_bool,json=extensionOptionalBool",
}

var E_Message_ExtensionOptionalInt32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         10001,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_int32",
	Tag:           "varint,10001,opt,name=extension_optional_int32,json=extensionOptionalInt32",
}

var E_Message_ExtensionOptionalSint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         10002,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_sint32",
	Tag:           "zigzag32,10002,opt,name=extension_optional_sint32,json=extensionOptionalSint32",
}

var E_Message_ExtensionOptionalUint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         10003,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_uint32",
	Tag:           "varint,10003,opt,name=extension_optional_uint32,json=extensionOptionalUint32",
}

var E_Message_ExtensionOptionalInt64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         10004,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_int64",
	Tag:           "varint,10004,opt,name=extension_optional_int64,json=extensionOptionalInt64",
}

var E_Message_ExtensionOptionalSint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         10005,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_sint64",
	Tag:           "zigzag64,10005,opt,name=extension_optional_sint64,json=extensionOptionalSint64",
}

var E_Message_ExtensionOptionalUint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         10006,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_uint64",
	Tag:           "varint,10006,opt,name=extension_optional_uint64,json=extensionOptionalUint64",
}

var E_Message_ExtensionOptionalFixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         10007,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_fixed32",
	Tag:           "fixed32,10007,opt,name=extension_optional_fixed32,json=extensionOptionalFixed32",
}

var E_Message_ExtensionOptionalSfixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         10008,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_sfixed32",
	Tag:           "fixed32,10008,opt,name=extension_optional_sfixed32,json=extensionOptionalSfixed32",
}

var E_Message_ExtensionOptionalFloat = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float32)(nil),
	Field:         10009,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_float",
	Tag:           "fixed32,10009,opt,name=extension_optional_float,json=extensionOptionalFloat",
}

var E_Message_ExtensionOptionalFixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         10010,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_fixed64",
	Tag:           "fixed64,10010,opt,name=extension_optional_fixed64,json=extensionOptionalFixed64",
}

var E_Message_ExtensionOptionalSfixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         10011,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_sfixed64",
	Tag:           "fixed64,10011,opt,name=extension_optional_sfixed64,json=extensionOptionalSfixed64",
}

var E_Message_ExtensionOptionalDouble = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float64)(nil),
	Field:         10012,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_double",
	Tag:           "fixed64,10012,opt,name=extension_optional_double,json=extensionOptionalDouble",
}

var E_Message_ExtensionOptionalString = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*string)(nil),
	Field:         10013,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_string",
	Tag:           "bytes,10013,opt,name=extension_optional_string,json=extensionOptionalString",
}

var E_Message_ExtensionOptionalBytes = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]byte)(nil),
	Field:         10014,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_bytes",
	Tag:           "bytes,10014,opt,name=extension_optional_bytes,json=extensionOptionalBytes",
}

var E_Message_ExtensionOptionalChildEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ChildEnum)(nil),
	Field:         10015,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_child_enum",
	Tag:           "varint,10015,opt,name=extension_optional_child_enum,json=extensionOptionalChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum",
}

var E_Message_ExtensionOptionalChildMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ChildMessage)(nil),
	Field:         10016,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_child_message",
	Tag:           "bytes,10016,opt,name=extension_optional_child_message,json=extensionOptionalChildMessage",
}

var E_Message_ExtensionOptionalNamedGroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_NamedGroup)(nil),
	Field:         10017,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_named_group",
	Tag:           "bytes,10017,opt,name=extension_optional_named_group,json=extensionOptionalNamedGroup",
}

var E_Message_ExtensionOptionalSiblingEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*SiblingEnum)(nil),
	Field:         10018,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_sibling_enum",
	Tag:           "varint,10018,opt,name=extension_optional_sibling_enum,json=extensionOptionalSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum",
}

var E_Message_ExtensionOptionalSiblingMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*SiblingMessage)(nil),
	Field:         10019,
	Name:          "google.golang.org.proto2_20160519.Message.extension_optional_sibling_message",
	Tag:           "bytes,10019,opt,name=extension_optional_sibling_message,json=extensionOptionalSiblingMessage",
}

var E_Message_Extensionoptionalgroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ExtensionOptionalGroup)(nil),
	Field:         10020,
	Name:          "google.golang.org.proto2_20160519.Message.extensionoptionalgroup",
	Tag:           "group,10020,opt,name=ExtensionOptionalGroup,json=extensionoptionalgroup",
}

var E_Message_ExtensionDefaultedBool = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*bool)(nil),
	Field:         20000,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_bool",
	Tag:           "varint,20000,opt,name=extension_defaulted_bool,json=extensionDefaultedBool,def=1",
}

var E_Message_ExtensionDefaultedInt32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         20001,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_int32",
	Tag:           "varint,20001,opt,name=extension_defaulted_int32,json=extensionDefaultedInt32,def=-12345",
}

var E_Message_ExtensionDefaultedSint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         20002,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_sint32",
	Tag:           "zigzag32,20002,opt,name=extension_defaulted_sint32,json=extensionDefaultedSint32,def=-3200",
}

var E_Message_ExtensionDefaultedUint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         20003,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_uint32",
	Tag:           "varint,20003,opt,name=extension_defaulted_uint32,json=extensionDefaultedUint32,def=3200",
}

var E_Message_ExtensionDefaultedInt64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         20004,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_int64",
	Tag:           "varint,20004,opt,name=extension_defaulted_int64,json=extensionDefaultedInt64,def=-123456789",
}

var E_Message_ExtensionDefaultedSint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         20005,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_sint64",
	Tag:           "zigzag64,20005,opt,name=extension_defaulted_sint64,json=extensionDefaultedSint64,def=-6400",
}

var E_Message_ExtensionDefaultedUint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         20006,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_uint64",
	Tag:           "varint,20006,opt,name=extension_defaulted_uint64,json=extensionDefaultedUint64,def=6400",
}

var E_Message_ExtensionDefaultedFixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         20007,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_fixed32",
	Tag:           "fixed32,20007,opt,name=extension_defaulted_fixed32,json=extensionDefaultedFixed32,def=320000",
}

var E_Message_ExtensionDefaultedSfixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         20008,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_sfixed32",
	Tag:           "fixed32,20008,opt,name=extension_defaulted_sfixed32,json=extensionDefaultedSfixed32,def=-320000",
}

var E_Message_ExtensionDefaultedFloat = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float32)(nil),
	Field:         20009,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_float",
	Tag:           "fixed32,20009,opt,name=extension_defaulted_float,json=extensionDefaultedFloat,def=3.14159",
}

var E_Message_ExtensionDefaultedFixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         20010,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_fixed64",
	Tag:           "fixed64,20010,opt,name=extension_defaulted_fixed64,json=extensionDefaultedFixed64,def=640000",
}

var E_Message_ExtensionDefaultedSfixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         20011,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_sfixed64",
	Tag:           "fixed64,20011,opt,name=extension_defaulted_sfixed64,json=extensionDefaultedSfixed64,def=-640000",
}

var E_Message_ExtensionDefaultedDouble = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float64)(nil),
	Field:         20012,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_double",
	Tag:           "fixed64,20012,opt,name=extension_defaulted_double,json=extensionDefaultedDouble,def=3.14159265359",
}

var E_Message_ExtensionDefaultedString = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*string)(nil),
	Field:         20013,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_string",
	Tag:           "bytes,20013,opt,name=extension_defaulted_string,json=extensionDefaultedString,def=hello, \"world!\"\n",
}

var E_Message_ExtensionDefaultedBytes = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]byte)(nil),
	Field:         20014,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_bytes",
	Tag:           "bytes,20014,opt,name=extension_defaulted_bytes,json=extensionDefaultedBytes,def=dead\\336\\255\\276\\357beef",
}

var E_Message_ExtensionDefaultedChildEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ChildEnum)(nil),
	Field:         20015,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_child_enum",
	Tag:           "varint,20015,opt,name=extension_defaulted_child_enum,json=extensionDefaultedChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum,def=0",
}

var E_Message_ExtensionDefaultedSiblingEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*SiblingEnum)(nil),
	Field:         20016,
	Name:          "google.golang.org.proto2_20160519.Message.extension_defaulted_sibling_enum",
	Tag:           "varint,20016,opt,name=extension_defaulted_sibling_enum,json=extensionDefaultedSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum,def=0",
}

var E_Message_ExtensionRepeatedBool = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]bool)(nil),
	Field:         30000,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_bool",
	Tag:           "varint,30000,rep,name=extension_repeated_bool,json=extensionRepeatedBool",
}

var E_Message_ExtensionRepeatedInt32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int32)(nil),
	Field:         30001,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_int32",
	Tag:           "varint,30001,rep,name=extension_repeated_int32,json=extensionRepeatedInt32",
}

var E_Message_ExtensionRepeatedSint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int32)(nil),
	Field:         30002,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_sint32",
	Tag:           "zigzag32,30002,rep,name=extension_repeated_sint32,json=extensionRepeatedSint32",
}

var E_Message_ExtensionRepeatedUint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint32)(nil),
	Field:         30003,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_uint32",
	Tag:           "varint,30003,rep,name=extension_repeated_uint32,json=extensionRepeatedUint32",
}

var E_Message_ExtensionRepeatedInt64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int64)(nil),
	Field:         30004,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_int64",
	Tag:           "varint,30004,rep,name=extension_repeated_int64,json=extensionRepeatedInt64",
}

var E_Message_ExtensionRepeatedSint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int64)(nil),
	Field:         30005,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_sint64",
	Tag:           "zigzag64,30005,rep,name=extension_repeated_sint64,json=extensionRepeatedSint64",
}

var E_Message_ExtensionRepeatedUint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint64)(nil),
	Field:         30006,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_uint64",
	Tag:           "varint,30006,rep,name=extension_repeated_uint64,json=extensionRepeatedUint64",
}

var E_Message_ExtensionRepeatedFixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint32)(nil),
	Field:         30007,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_fixed32",
	Tag:           "fixed32,30007,rep,name=extension_repeated_fixed32,json=extensionRepeatedFixed32",
}

var E_Message_ExtensionRepeatedSfixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int32)(nil),
	Field:         30008,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_sfixed32",
	Tag:           "fixed32,30008,rep,name=extension_repeated_sfixed32,json=extensionRepeatedSfixed32",
}

var E_Message_ExtensionRepeatedFloat = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]float32)(nil),
	Field:         30009,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_float",
	Tag:           "fixed32,30009,rep,name=extension_repeated_float,json=extensionRepeatedFloat",
}

var E_Message_ExtensionRepeatedFixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint64)(nil),
	Field:         30010,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_fixed64",
	Tag:           "fixed64,30010,rep,name=extension_repeated_fixed64,json=extensionRepeatedFixed64",
}

var E_Message_ExtensionRepeatedSfixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int64)(nil),
	Field:         30011,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_sfixed64",
	Tag:           "fixed64,30011,rep,name=extension_repeated_sfixed64,json=extensionRepeatedSfixed64",
}

var E_Message_ExtensionRepeatedDouble = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]float64)(nil),
	Field:         30012,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_double",
	Tag:           "fixed64,30012,rep,name=extension_repeated_double,json=extensionRepeatedDouble",
}

var E_Message_ExtensionRepeatedString = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]string)(nil),
	Field:         30013,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_string",
	Tag:           "bytes,30013,rep,name=extension_repeated_string,json=extensionRepeatedString",
}

var E_Message_ExtensionRepeatedBytes = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([][]byte)(nil),
	Field:         30014,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_bytes",
	Tag:           "bytes,30014,rep,name=extension_repeated_bytes,json=extensionRepeatedBytes",
}

var E_Message_ExtensionRepeatedChildEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]Message_ChildEnum)(nil),
	Field:         30015,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_child_enum",
	Tag:           "varint,30015,rep,name=extension_repeated_child_enum,json=extensionRepeatedChildEnum,enum=google.golang.org.proto2_20160519.Message_ChildEnum",
}

var E_Message_ExtensionRepeatedChildMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*Message_ChildMessage)(nil),
	Field:         30016,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_child_message",
	Tag:           "bytes,30016,rep,name=extension_repeated_child_message,json=extensionRepeatedChildMessage",
}

var E_Message_ExtensionRepeatedNamedGroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*Message_NamedGroup)(nil),
	Field:         30017,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_named_group",
	Tag:           "bytes,30017,rep,name=extension_repeated_named_group,json=extensionRepeatedNamedGroup",
}

var E_Message_ExtensionRepeatedSiblingEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]SiblingEnum)(nil),
	Field:         30018,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_sibling_enum",
	Tag:           "varint,30018,rep,name=extension_repeated_sibling_enum,json=extensionRepeatedSiblingEnum,enum=google.golang.org.proto2_20160519.SiblingEnum",
}

var E_Message_ExtensionRepeatedSiblingMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*SiblingMessage)(nil),
	Field:         30019,
	Name:          "google.golang.org.proto2_20160519.Message.extension_repeated_sibling_message",
	Tag:           "bytes,30019,rep,name=extension_repeated_sibling_message,json=extensionRepeatedSiblingMessage",
}

var E_Message_Extensionrepeatedgroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*Message_ExtensionRepeatedGroup)(nil),
	Field:         30020,
	Name:          "google.golang.org.proto2_20160519.Message.extensionrepeatedgroup",
	Tag:           "group,30020,rep,name=ExtensionRepeatedGroup,json=extensionrepeatedgroup",
}

type Message_ChildMessage struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	F4               *Message `protobuf:"bytes,4,opt,name=f4" json:"f4,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_ChildMessage) Reset()                    { *m = Message_ChildMessage{} }
func (m *Message_ChildMessage) String() string            { return proto.CompactTextString(m) }
func (*Message_ChildMessage) ProtoMessage()               {}
func (*Message_ChildMessage) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 0} }

func (m *Message_ChildMessage) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_ChildMessage) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_ChildMessage) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func (m *Message_ChildMessage) GetF4() *Message {
	if m != nil {
		return m.F4
	}
	return nil
}

type Message_NamedGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	F4               *Message `protobuf:"bytes,4,opt,name=f4" json:"f4,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_NamedGroup) Reset()                    { *m = Message_NamedGroup{} }
func (m *Message_NamedGroup) String() string            { return proto.CompactTextString(m) }
func (*Message_NamedGroup) ProtoMessage()               {}
func (*Message_NamedGroup) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 1} }

func (m *Message_NamedGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_NamedGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_NamedGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func (m *Message_NamedGroup) GetF4() *Message {
	if m != nil {
		return m.F4
	}
	return nil
}

type Message_OptionalGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_OptionalGroup) Reset()                    { *m = Message_OptionalGroup{} }
func (m *Message_OptionalGroup) String() string            { return proto.CompactTextString(m) }
func (*Message_OptionalGroup) ProtoMessage()               {}
func (*Message_OptionalGroup) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 2} }

func (m *Message_OptionalGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_OptionalGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_OptionalGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_RequiredGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_RequiredGroup) Reset()                    { *m = Message_RequiredGroup{} }
func (m *Message_RequiredGroup) String() string            { return proto.CompactTextString(m) }
func (*Message_RequiredGroup) ProtoMessage()               {}
func (*Message_RequiredGroup) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 3} }

func (m *Message_RequiredGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_RequiredGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_RequiredGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_RepeatedGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_RepeatedGroup) Reset()                    { *m = Message_RepeatedGroup{} }
func (m *Message_RepeatedGroup) String() string            { return proto.CompactTextString(m) }
func (*Message_RepeatedGroup) ProtoMessage()               {}
func (*Message_RepeatedGroup) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 4} }

func (m *Message_RepeatedGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_RepeatedGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_RepeatedGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_OneofGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_OneofGroup) Reset()                    { *m = Message_OneofGroup{} }
func (m *Message_OneofGroup) String() string            { return proto.CompactTextString(m) }
func (*Message_OneofGroup) ProtoMessage()               {}
func (*Message_OneofGroup) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 33} }

func (m *Message_OneofGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_OneofGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_OneofGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_ExtensionOptionalGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_ExtensionOptionalGroup) Reset()         { *m = Message_ExtensionOptionalGroup{} }
func (m *Message_ExtensionOptionalGroup) String() string { return proto.CompactTextString(m) }
func (*Message_ExtensionOptionalGroup) ProtoMessage()    {}
func (*Message_ExtensionOptionalGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor0, []int{1, 34}
}

func (m *Message_ExtensionOptionalGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_ExtensionOptionalGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_ExtensionOptionalGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_ExtensionRepeatedGroup struct {
	F1               *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2               *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3               []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Message_ExtensionRepeatedGroup) Reset()         { *m = Message_ExtensionRepeatedGroup{} }
func (m *Message_ExtensionRepeatedGroup) String() string { return proto.CompactTextString(m) }
func (*Message_ExtensionRepeatedGroup) ProtoMessage()    {}
func (*Message_ExtensionRepeatedGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor0, []int{1, 35}
}

func (m *Message_ExtensionRepeatedGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_ExtensionRepeatedGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_ExtensionRepeatedGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func init() {
	proto.RegisterType((*SiblingMessage)(nil), "google.golang.org.proto2_20160519.SiblingMessage")
	proto.RegisterType((*Message)(nil), "google.golang.org.proto2_20160519.Message")
	proto.RegisterType((*Message_ChildMessage)(nil), "google.golang.org.proto2_20160519.Message.ChildMessage")
	proto.RegisterType((*Message_NamedGroup)(nil), "google.golang.org.proto2_20160519.Message.NamedGroup")
	proto.RegisterType((*Message_OptionalGroup)(nil), "google.golang.org.proto2_20160519.Message.OptionalGroup")
	proto.RegisterType((*Message_RequiredGroup)(nil), "google.golang.org.proto2_20160519.Message.RequiredGroup")
	proto.RegisterType((*Message_RepeatedGroup)(nil), "google.golang.org.proto2_20160519.Message.RepeatedGroup")
	proto.RegisterType((*Message_OneofGroup)(nil), "google.golang.org.proto2_20160519.Message.OneofGroup")
	proto.RegisterType((*Message_ExtensionOptionalGroup)(nil), "google.golang.org.proto2_20160519.Message.ExtensionOptionalGroup")
	proto.RegisterType((*Message_ExtensionRepeatedGroup)(nil), "google.golang.org.proto2_20160519.Message.ExtensionRepeatedGroup")
	proto.RegisterEnum("google.golang.org.proto2_20160519.SiblingEnum", SiblingEnum_name, SiblingEnum_value)
	proto.RegisterEnum("google.golang.org.proto2_20160519.Message_ChildEnum", Message_ChildEnum_name, Message_ChildEnum_value)
	proto.RegisterExtension(E_Message_ExtensionOptionalBool)
	proto.RegisterExtension(E_Message_ExtensionOptionalInt32)
	proto.RegisterExtension(E_Message_ExtensionOptionalSint32)
	proto.RegisterExtension(E_Message_ExtensionOptionalUint32)
	proto.RegisterExtension(E_Message_ExtensionOptionalInt64)
	proto.RegisterExtension(E_Message_ExtensionOptionalSint64)
	proto.RegisterExtension(E_Message_ExtensionOptionalUint64)
	proto.RegisterExtension(E_Message_ExtensionOptionalFixed32)
	proto.RegisterExtension(E_Message_ExtensionOptionalSfixed32)
	proto.RegisterExtension(E_Message_ExtensionOptionalFloat)
	proto.RegisterExtension(E_Message_ExtensionOptionalFixed64)
	proto.RegisterExtension(E_Message_ExtensionOptionalSfixed64)
	proto.RegisterExtension(E_Message_ExtensionOptionalDouble)
	proto.RegisterExtension(E_Message_ExtensionOptionalString)
	proto.RegisterExtension(E_Message_ExtensionOptionalBytes)
	proto.RegisterExtension(E_Message_ExtensionOptionalChildEnum)
	proto.RegisterExtension(E_Message_ExtensionOptionalChildMessage)
	proto.RegisterExtension(E_Message_ExtensionOptionalNamedGroup)
	proto.RegisterExtension(E_Message_ExtensionOptionalSiblingEnum)
	proto.RegisterExtension(E_Message_ExtensionOptionalSiblingMessage)
	proto.RegisterExtension(E_Message_Extensionoptionalgroup)
	proto.RegisterExtension(E_Message_ExtensionDefaultedBool)
	proto.RegisterExtension(E_Message_ExtensionDefaultedInt32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSint32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedUint32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedInt64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSint64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedUint64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedFixed32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSfixed32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedFloat)
	proto.RegisterExtension(E_Message_ExtensionDefaultedFixed64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSfixed64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedDouble)
	proto.RegisterExtension(E_Message_ExtensionDefaultedString)
	proto.RegisterExtension(E_Message_ExtensionDefaultedBytes)
	proto.RegisterExtension(E_Message_ExtensionDefaultedChildEnum)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSiblingEnum)
	proto.RegisterExtension(E_Message_ExtensionRepeatedBool)
	proto.RegisterExtension(E_Message_ExtensionRepeatedInt32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSint32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedUint32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedInt64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSint64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedUint64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedFixed32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSfixed32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedFloat)
	proto.RegisterExtension(E_Message_ExtensionRepeatedFixed64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSfixed64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedDouble)
	proto.RegisterExtension(E_Message_ExtensionRepeatedString)
	proto.RegisterExtension(E_Message_ExtensionRepeatedBytes)
	proto.RegisterExtension(E_Message_ExtensionRepeatedChildEnum)
	proto.RegisterExtension(E_Message_ExtensionRepeatedChildMessage)
	proto.RegisterExtension(E_Message_ExtensionRepeatedNamedGroup)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSiblingEnum)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSiblingMessage)
	proto.RegisterExtension(E_Message_Extensionrepeatedgroup)
}

var fileDescriptor0 = []byte{
	// 4468 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5c, 0x69, 0x70, 0x23, 0xc7,
	0x75, 0xe6, 0x00, 0x04, 0xb8, 0xec, 0x25, 0x48, 0x70, 0x76, 0x97, 0x9c, 0xa5, 0xa4, 0x08, 0x5e,
	0x3b, 0x0e, 0xa2, 0x68, 0xb9, 0xcb, 0x61, 0xb3, 0x57, 0x8b, 0xe8, 0xf0, 0x52, 0x5a, 0x19, 0x72,
	0x2c, 0xc8, 0x35, 0xaa, 0x4d, 0xa5, 0x52, 0xaa, 0x30, 0xe0, 0x12, 0xe4, 0x52, 0xc2, 0x41, 0x91,
	0x80, 0xa4, 0x8d, 0x9d, 0xd2, 0xc6, 0x39, 0x7f, 0xca, 0xf7, 0x05, 0xdb, 0xb2, 0x6e, 0x5b, 0xa7,
	0xef, 0x4b, 0x97, 0x8f, 0x24, 0xf2, 0x7d, 0xe5, 0x70, 0x2e, 0xe7, 0xbe, 0x9c, 0xfb, 0xbe, 0x8f,
	0xea, 0x7e, 0xdd, 0xd3, 0xdd, 0x33, 0x3d, 0x20, 0x7b, 0xe0, 0xd2, 0x0f, 0x55, 0x69, 0x1b, 0xaf,
	0xdf, 0xd7, 0xef, 0x43, 0xbf, 0xf7, 0x3e, 0xf6, 0x4c, 0x03, 0xbd, 0x74, 0x6b, 0xbb, 0xd3, 0xed,
	0xf8, 0x2b, 0xfe, 0xf1, 0x05, 0x72, 0x7c, 0x69, 0xe1, 0xe4, 0x4a, 0x1d, 0xd7, 0x57, 0x4f, 0x36,
	0xce, 0x2e, 0x1d, 0xeb, 0x36, 0x76, 0xba, 0xf3, 0xec, 0x53, 0xf7, 0x25, 0x1b, 0x9d, 0xce, 0x46,
	0xb3, 0x31, 0xbf, 0xd1, 0x69, 0xd6, 0xdb, 0x1b, 0xf3, 0x9d, 0xed, 0x8d, 0xf9, 0xc8, 0xb4, 0x23,
	0xaf, 0x43, 0x93, 0x37, 0x6f, 0xae, 0x36, 0x37, 0xdb, 0x1b, 0x37, 0x36, 0x76, 0x76, 0xea, 0x1b,
	0x0d, 0x77, 0x12, 0x65, 0xd6, 0x17, 0x3c, 0xa7, 0xe4, 0x94, 0xc7, 0x83, 0xcc, 0xfa, 0x02, 0xfb,
	0xb7, 0xef, 0x65, 0x4a, 0x19, 0xf6, 0x6f, 0x9f, 0xfd, 0x7b, 0xd1, 0xcb, 0x96, 0xb2, 0xec, 0xdf,
	0x8b, 0x6e, 0x05, 0x65, 0xd6, 0xb1, 0x37, 0x5a, 0x72, 0xca, 0xfb, 0xfd, 0xcb, 0xe6, 0x77, 0x45,
	0x9c, 0xe7, 0x38, 0x41, 0x66, 0x1d, 0x1f, 0xf9, 0xce, 0xa3, 0x0e, 0x1a, 0x13, 0xc0, 0x67, 0x10,
	0x6a, 0xd7, 0x5b, 0x8d, 0xb5, 0x8d, 0xed, 0x4e, 0x6f, 0x8b, 0x2d, 0x00, 0xf9, 0x4b, 0x7b, 0x77,
	0x38, 0x5f, 0xa3, 0x93, 0x5f, 0x49, 0x27, 0x07, 0x8a, 0x23, 0xf7, 0xa5, 0xa8, 0xd0, 0xd9, 0xea,
	0x6e, 0x76, 0xda, 0xf5, 0xe6, 0xca, 0x6a, 0xa7, 0xd3, 0xf4, 0xd6, 0x4a, 0x4e, 0x79, 0x5f, 0x30,
	0x21, 0x06, 0x97, 0x3b, 0x9d, 0xa6, 0xfb, 0xfd, 0x68, 0x32, 0x34, 0xda, 0x6c, 0x77, 0x17, 0x7d,
	0xaf, 0x51, 0x72, 0xca, 0xb9, 0x20, 0x9c, 0x7a, 0x03, 0x1d, 0x74, 0x7f, 0x00, 0x4d, 0x85, 0x66,
	0x3b, 0x60, 0xb7, 0x5e, 0x72, 0xca, 0xd3, 0x41, 0x38, 0xfb, 0xe6, 0xcd, 0x98, 0x61, 0x0f, 0x0c,
	0x37, 0x4a, 0x4e, 0xb9, 0x20, 0x0d, 0xcf, 0x80, 0x61, 0x04, 0x98, 0x60, 0xef, 0x5c, 0xc9, 0x29,
	0x67, 0x35, 0x60, 0x82, 0x63, 0xc0, 0x04, 0x7b, 0x9b, 0x25, 0xa7, 0xec, 0xea, 0xc0, 0x11, 0xc3,
	0x1e, 0x18, 0xde, 0x5a, 0x72, 0xca, 0xa3, 0x3a, 0x30, 0xc1, 0xee, 0x0f, 0xa2, 0x62, 0x68, 0xb8,
	0xbe, 0x79, 0x57, 0x63, 0x6d, 0xd1, 0xf7, 0x6e, 0x2b, 0x39, 0xe5, 0xb1, 0x20, 0x74, 0x70, 0x3d,
	0x0c, 0xbb, 0x3f, 0x84, 0xa6, 0x25, 0xb8, 0xb0, 0x6d, 0x96, 0x9c, 0xf2, 0x54, 0x10, 0xfa, 0xb8,
	0x99, 0x8f, 0x6b, 0x01, 0xad, 0x37, 0x3b, 0xf5, 0xae, 0xd7, 0x2a, 0x39, 0xe5, 0x8c, 0x0c, 0xe8,
	0x7a, 0x3a, 0x18, 0x87, 0x27, 0xd8, 0x6b, 0x97, 0x9c, 0x72, 0x3e, 0x02, 0x4f, 0xb0, 0x01, 0x9e,
	0x60, 0xaf, 0x53, 0x72, 0xca, 0xc5, 0x28, 0x7c, 0x24, 0xfe, 0xb5, 0x4e, 0x6f, 0xb5, 0xd9, 0xf0,
	0xb6, 0x4a, 0x4e, 0xd9, 0x91, 0xf1, 0x5f, 0xc7, 0x46, 0x75, 0x46, 0xbb, 0xdb, 0x9b, 0xed, 0x0d,
	0xef, 0x76, 0xb6, 0xe7, 0x25, 0xa3, 0x6c, 0x54, 0x0b, 0x68, 0xf5, 0x7c, 0xb7, 0xb1, 0xe3, 0x6d,
	0x97, 0x9c, 0xf2, 0x84, 0x0c, 0x68, 0x99, 0x0e, 0xba, 0x6b, 0xe8, 0x40, 0x68, 0x76, 0xf6, 0xdc,
	0x66, 0x73, 0x6d, 0xa5, 0xd1, 0xee, 0xb5, 0xbc, 0x9d, 0x92, 0x53, 0x9e, 0xf4, 0xb1, 0xc5, 0x36,
	0xbe, 0x96, 0x4e, 0x3e, 0xdd, 0xee, 0xb5, 0x82, 0x30, 0xec, 0x70, 0xc8, 0x6d, 0xa1, 0x99, 0x08,
	0x4a, 0x0b, 0xa6, 0x79, 0x5d, 0x96, 0x80, 0x27, 0x6c, 0x81, 0x44, 0x36, 0x1e, 0xd4, 0xb0, 0x44,
	0x4a, 0x6e, 0xa0, 0x70, 0x7c, 0x85, 0xa5, 0xd4, 0x0a, 0x24, 0x67, 0x8f, 0x81, 0xa5, 0x4c, 0x4e,
	0x57, 0xb8, 0x94, 0x63, 0xee, 0x2a, 0x3a, 0xa4, 0xec, 0x6f, 0x56, 0x8f, 0x80, 0xbf, 0x3b, 0x18,
	0x7f, 0xf3, 0x7b, 0x40, 0xe2, 0x65, 0x8c, 0x31, 0x77, 0x40, 0x66, 0x45, 0x38, 0xe8, 0xde, 0x86,
	0xbc, 0x18, 0x86, 0x60, 0xef, 0x4e, 0x16, 0xd0, 0xc2, 0xde, 0x61, 0x04, 0x6f, 0x33, 0x11, 0x24,
	0xc1, 0xdc, 0x4f, 0xc8, 0xaa, 0x03, 0x94, 0xdd, 0xc5, 0xea, 0xd9, 0x15, 0x16, 0x94, 0xdd, 0xc4,
	0xe7, 0x03, 0x6b, 0xba, 0x3b, 0xf7, 0x72, 0x34, 0xb9, 0xd6, 0x58, 0xaf, 0xf7, 0x9a, 0xdd, 0xc6,
	0x1a, 0x94, 0xb5, 0x17, 0x68, 0xc5, 0xdc, 0x57, 0x19, 0xed, 0x6e, 0xf7, 0x1a, 0x41, 0x21, 0xfc,
	0x90, 0x95, 0xb7, 0xe3, 0x68, 0x4a, 0x5a, 0x43, 0x39, 0xfa, 0x02, 0x35, 0xcf, 0x55, 0xf2, 0x47,
	0x17, 0xfc, 0x45, 0xbc, 0x14, 0x48, 0x6f, 0x50, 0xe9, 0x16, 0x50, 0x51, 0xce, 0xe0, 0xa5, 0xee,
	0x8b, 0x74, 0xca, 0x74, 0x25, 0x77, 0x74, 0xd1, 0x3f, 0x7e, 0x3c, 0x90, 0x1e, 0x79, 0xcd, 0x3b,
	0xae, 0x4e, 0xe1, 0x45, 0xef, 0x4b, 0x74, 0x4a, 0xa1, 0x32, 0x1a, 0x99, 0xc1, 0x8b, 0x1f, 0x8e,
	0x2c, 0x8b, 0x60, 0xef, 0xcb, 0x74, 0x42, 0xb6, 0x82, 0x60, 0x59, 0xe4, 0xc4, 0x15, 0x27, 0xf5,
	0xa5, 0x11, 0x1c, 0x5f, 0x1a, 0xc1, 0xde, 0x57, 0xe8, 0x34, 0xb7, 0x92, 0x3b, 0x4a, 0x70, 0x6c,
	0x69, 0x04, 0xc7, 0x97, 0x46, 0xb0, 0xf7, 0x55, 0x3a, 0x65, 0xb4, 0x32, 0x1a, 0x99, 0xc1, 0xcb,
	0x23, 0x46, 0xd3, 0x72, 0x86, 0xa8, 0x79, 0x5f, 0xa3, 0x53, 0xc6, 0x2a, 0x79, 0x1a, 0xcd, 0xf1,
	0xe3, 0x81, 0xf4, 0x29, 0x2a, 0xe5, 0x09, 0xe4, 0x2a, 0x4b, 0x13, 0xd3, 0xbe, 0x4e, 0xa7, 0x4d,
	0x55, 0xc6, 0x8e, 0xf2, 0x79, 0xd2, 0x73, 0x58, 0x35, 0x17, 0x54, 0x26, 0xa0, 0x6c, 0x7e, 0x83,
	0xce, 0xca, 0x54, 0xc6, 0x16, 0xe7, 0x17, 0xf0, 0xc2, 0x92, 0x4a, 0x03, 0x54, 0xd0, 0xf8, 0x0a,
	0x09, 0xf6, 0xbe, 0x49, 0x27, 0xe5, 0x2b, 0x79, 0x1a, 0x54, 0x7c, 0x85, 0x04, 0x9b, 0x56, 0x48,
	0xb0, 0xf7, 0x2d, 0x3a, 0xad, 0x58, 0x19, 0x3b, 0xca, 0xe7, 0x45, 0x57, 0x48, 0xb0, 0x7b, 0x52,
	0xa5, 0x90, 0x57, 0xd6, 0x5f, 0xa3, 0xd3, 0x9c, 0x4a, 0x81, 0x2f, 0xd1, 0x27, 0x4b, 0x8b, 0x4b,
	0x27, 0x15, 0x2e, 0x79, 0xa9, 0xbd, 0x52, 0xfb, 0xc2, 0xa0, 0xd6, 0xfe, 0x3a, 0x13, 0x18, 0x95,
	0xe2, 0xb9, 0x46, 0xb3, 0xd9, 0xb9, 0xbc, 0x74, 0xe4, 0xce, 0xce, 0x76, 0x73, 0xed, 0x25, 0x47,
	0x90, 0xfa, 0xdd, 0x41, 0xfd, 0x5d, 0x56, 0xa9, 0x81, 0x02, 0xfc, 0x1b, 0x74, 0xf2, 0x44, 0xc5,
	0x5b, 0x6b, 0xd4, 0xd7, 0x6e, 0x59, 0x5c, 0x24, 0xb7, 0xf8, 0x4b, 0x4b, 0xb7, 0xf8, 0x27, 0xc8,
	0x2d, 0x8b, 0x4b, 0x27, 0x56, 0x1b, 0x8d, 0x75, 0x85, 0x2b, 0x28, 0xce, 0x6d, 0x74, 0x50, 0xfa,
	0x50, 0xaa, 0xf3, 0x6f, 0x3a, 0xe9, 0xcb, 0x73, 0x25, 0x77, 0xea, 0xd5, 0xaf, 0xa9, 0x9e, 0x0a,
	0x24, 0x9f, 0xb2, 0x4c, 0x37, 0xd1, 0x8c, 0xba, 0x45, 0x95, 0x7a, 0xf6, 0x6d, 0x27, 0x4d, 0x41,
	0x13, 0x58, 0x07, 0x95, 0x8d, 0x2d, 0x0b, 0xdb, 0xcb, 0x50, 0x61, 0xbb, 0x71, 0x7b, 0x6f, 0x73,
	0x5b, 0x94, 0x82, 0xc7, 0xa8, 0x5a, 0xdb, 0x17, 0x4c, 0x88, 0x51, 0x56, 0x03, 0x5e, 0x8e, 0x26,
	0x43, 0x2b, 0x48, 0xce, 0xc7, 0xa9, 0x59, 0x2e, 0x08, 0x27, 0x43, 0xe6, 0x97, 0xd1, 0x54, 0x68,
	0xc7, 0x13, 0xff, 0x09, 0x6a, 0x38, 0x1d, 0x84, 0xf3, 0x79, 0xc2, 0xab, 0x96, 0x3c, 0xdf, 0x9f,
	0xa4, 0x96, 0x05, 0x69, 0xc9, 0x13, 0x3d, 0x82, 0x4d, 0xb0, 0xf7, 0x14, 0x35, 0xcc, 0x6a, 0xd8,
	0x04, 0xc7, 0xb0, 0x09, 0xf6, 0x3e, 0x48, 0x0d, 0x5d, 0x1d, 0x3b, 0x62, 0xc9, 0x13, 0xfa, 0x43,
	0xd4, 0x72, 0x54, 0xc7, 0x26, 0xd8, 0xbd, 0x0c, 0x15, 0x43, 0x4b, 0x91, 0x91, 0x1f, 0xa6, 0xa6,
	0x63, 0x41, 0xe8, 0x42, 0xe4, 0xef, 0xe5, 0x68, 0x5a, 0xe2, 0x0b, 0xe3, 0x8f, 0x50, 0xe3, 0xa9,
	0x20, 0xf4, 0x12, 0x26, 0xad, 0x1a, 0x15, 0xe4, 0xec, 0x47, 0xa9, 0x69, 0x46, 0x46, 0x05, 0x99,
	0x1a, 0x5b, 0x01, 0xc1, 0xde, 0xc7, 0xa8, 0x65, 0x3e, 0xb2, 0x02, 0x82, 0x0d, 0x2b, 0x20, 0xd8,
	0xfb, 0x38, 0x35, 0x2e, 0x46, 0x57, 0x10, 0x61, 0x81, 0xe7, 0xe4, 0x27, 0xa8, 0xad, 0x23, 0x59,
	0xe0, 0x39, 0xa8, 0x31, 0x0b, 0x29, 0xf8, 0x49, 0xd0, 0xf4, 0x92, 0x59, 0xc8, 0x37, 0x35, 0x2a,
	0x48, 0xb7, 0x4f, 0x51, 0xc3, 0x09, 0x19, 0x15, 0xe4, 0x54, 0x03, 0x1d, 0x08, 0xed, 0x94, 0x94,
	0xfa, 0x34, 0x35, 0x4e, 0xad, 0x78, 0x84, 0x47, 0x99, 0x4a, 0x6d, 0x34, 0x13, 0x81, 0x11, 0x3d,
	0xfb, 0x69, 0x8a, 0x34, 0x8c, 0xe4, 0xd1, 0xc0, 0x44, 0xe3, 0x3e, 0x87, 0xc2, 0x71, 0x4d, 0xf2,
	0x3c, 0x03, 0x68, 0x69, 0x35, 0x8f, 0xf0, 0xa9, 0x68, 0x9e, 0xb3, 0xe8, 0x90, 0xb2, 0xd9, 0x95,
	0x1a, 0xf1, 0x2c, 0x50, 0x68, 0x2d, 0x7a, 0x64, 0x8a, 0xc8, 0xda, 0xd0, 0x44, 0x5e, 0x0c, 0x44,
	0x10, 0xf8, 0x1c, 0x84, 0x94, 0x46, 0xf5, 0x44, 0xa0, 0x04, 0x79, 0x2b, 0xb2, 0x12, 0x01, 0x6b,
	0xcf, 0x53, 0x08, 0x3b, 0xd9, 0x13, 0x70, 0x07, 0x5c, 0xf6, 0x68, 0xfe, 0xdc, 0xab, 0xd0, 0xac,
	0xdc, 0xf0, 0xba, 0xfe, 0xb9, 0x27, 0x4b, 0x8b, 0x1e, 0xd7, 0x3f, 0x21, 0xb3, 0xd7, 0x69, 0x3a,
	0xe8, 0x94, 0xc2, 0x46, 0x54, 0x10, 0xbd, 0x81, 0xce, 0x97, 0x82, 0x68, 0x26, 0xe6, 0x01, 0xca,
	0xe3, 0x32, 0x3a, 0x6c, 0x70, 0xc1, 0x0b, 0xe5, 0x1b, 0xa9, 0x8f, 0x50, 0x21, 0xcd, 0xc6, 0x5c,
	0xf0, 0xc2, 0x79, 0xca, 0xe8, 0x83, 0x97, 0xd0, 0x37, 0x51, 0x1f, 0x42, 0x32, 0xc5, 0x5d, 0xf0,
	0x8a, 0x7a, 0x3a, 0x29, 0x12, 0x82, 0xbd, 0x37, 0x53, 0x0f, 0xba, 0x86, 0x32, 0x46, 0x43, 0xf0,
	0x80, 0x68, 0x08, 0xf6, 0xde, 0x42, 0xfd, 0x84, 0xa2, 0xca, 0x1c, 0x0d, 0xc1, 0x03, 0xa2, 0x21,
	0xd8, 0x7b, 0x2b, 0xf5, 0x21, 0x54, 0x96, 0x39, 0x1a, 0x82, 0xdd, 0xd3, 0x68, 0xce, 0xe0, 0x42,
	0x14, 0xe0, 0xb7, 0x51, 0x1f, 0x52, 0x76, 0x79, 0x31, 0x2f, 0xa2, 0x7c, 0x57, 0xd1, 0x45, 0xa6,
	0x68, 0x84, 0x9f, 0xb7, 0x53, 0x3f, 0x8a, 0x0e, 0x3b, 0x1c, 0x8f, 0x48, 0x94, 0xf6, 0x65, 0x23,
	0xbd, 0x50, 0xe4, 0xdf, 0x41, 0xdd, 0x28, 0xc2, 0x2c, 0xce, 0x2d, 0x94, 0xfd, 0x01, 0x41, 0x11,
	0xec, 0xbd, 0x93, 0x7a, 0x91, 0x4a, 0x2d, 0x21, 0x28, 0x82, 0x07, 0x06, 0x45, 0xb0, 0xf7, 0x2e,
	0xea, 0x47, 0x91, 0x6e, 0x49, 0x41, 0x11, 0xec, 0xbe, 0xca, 0xf8, 0x45, 0xf1, 0xbe, 0xd1, 0xa7,
	0x7e, 0x62, 0x5a, 0x2e, 0xfe, 0x8d, 0xf1, 0x7e, 0x72, 0xa3, 0x79, 0xe3, 0x40, 0x67, 0x79, 0x37,
	0xf5, 0x65, 0x12, 0x77, 0x86, 0x3d, 0x04, 0x4d, 0xe7, 0x66, 0x23, 0xdf, 0xd0, 0x7e, 0xde, 0x43,
	0xbd, 0x0d, 0x52, 0x7b, 0xf1, 0x2f, 0x00, 0x3a, 0xd4, 0xdd, 0xe8, 0x12, 0x83, 0x53, 0xa5, 0x57,
	0xbd, 0x37, 0x9b, 0xbe, 0x57, 0x09, 0x49, 0x36, 0x17, 0x03, 0x97, 0xbd, 0xeb, 0xa7, 0xd1, 0xa5,
	0xc6, 0xec, 0x52, 0x6a, 0xfd, 0xbd, 0xd9, 0x34, 0xb5, 0x5e, 0x80, 0x5f, 0x6c, 0xc8, 0xc9, 0x88,
	0x2e, 0xdc, 0x6a, 0xd4, 0xc3, 0x12, 0xf9, 0xcf, 0xd9, 0x52, 0x16, 0x74, 0x21, 0x8c, 0x4a, 0x5d,
	0xc8, 0xad, 0xa0, 0x02, 0xfd, 0x0b, 0x35, 0x63, 0xba, 0x10, 0x86, 0x15, 0x5d, 0xc8, 0xed, 0x78,
	0xb9, 0xfb, 0x57, 0x6a, 0xc8, 0x74, 0x21, 0x8c, 0xab, 0xba, 0x90, 0x5b, 0xf2, 0xa2, 0xf6, 0x6f,
	0xd4, 0xb2, 0x20, 0x2d, 0x55, 0x5d, 0x28, 0xb1, 0x09, 0xf6, 0xfe, 0x9d, 0x1a, 0x66, 0x35, 0x6c,
	0xa1, 0x73, 0x14, 0x6c, 0x82, 0xbd, 0xff, 0xa0, 0x86, 0xae, 0x8e, 0x1d, 0xb1, 0xe4, 0x25, 0xe8,
	0x3f, 0xa9, 0xe5, 0xa8, 0x8e, 0x2d, 0x74, 0x21, 0xb7, 0x14, 0x15, 0xe2, 0xbf, 0xa8, 0x29, 0xd3,
	0x85, 0xf0, 0x81, 0xa6, 0x0b, 0x05, 0xbe, 0x30, 0xfe, 0x6f, 0x6a, 0xcc, 0x74, 0x21, 0x5f, 0x81,
	0xa6, 0x0b, 0x85, 0x67, 0x56, 0x32, 0xfe, 0x87, 0x9a, 0x66, 0x64, 0x54, 0x8a, 0x2e, 0x54, 0x57,
	0x40, 0xb0, 0xf7, 0xbf, 0xd4, 0x32, 0x1f, 0x59, 0x81, 0xd0, 0x85, 0xda, 0x0a, 0x08, 0xf6, 0xfe,
	0x8f, 0x1a, 0x17, 0xa3, 0x2b, 0x88, 0xb0, 0xc0, 0xf3, 0xfb, 0xc2, 0x68, 0x29, 0x0b, 0xba, 0x10,
	0xc6, 0x55, 0x5d, 0x28, 0xfc, 0x42, 0xf6, 0xfe, 0xcc, 0x28, 0x3b, 0xdb, 0x95, 0xcc, 0x2a, 0xba,
	0x50, 0xec, 0x26, 0x96, 0x98, 0xaf, 0xa7, 0x86, 0x13, 0x32, 0x2a, 0x45, 0x17, 0x72, 0x3b, 0x25,
	0xd7, 0x7e, 0x96, 0x1a, 0x0f, 0xa1, 0x0b, 0xc1, 0x63, 0x44, 0x17, 0x6a, 0x30, 0x42, 0xd6, 0xfc,
	0x1c, 0x45, 0x1a, 0x4e, 0x17, 0x2a, 0x60, 0x9a, 0x2e, 0xe4, 0x78, 0xaa, 0x2e, 0xfc, 0x79, 0x40,
	0x4b, 0xaf, 0x0b, 0xc1, 0x67, 0x54, 0x17, 0x86, 0x9b, 0x5d, 0xa9, 0x15, 0xbf, 0x00, 0x14, 0xa6,
	0xd0, 0x85, 0x22, 0x45, 0x22, 0xba, 0x30, 0x02, 0x22, 0x08, 0xfc, 0x45, 0x08, 0x29, 0x9d, 0x2e,
	0xd4, 0xa0, 0x34, 0x5d, 0x08, 0x9f, 0x00, 0x6b, 0xbf, 0x44, 0x21, 0x6c, 0x75, 0x21, 0x38, 0x08,
	0x75, 0xa1, 0xe2, 0xcf, 0xfd, 0x49, 0x54, 0x68, 0xd5, 0xb7, 0x58, 0x95, 0x83, 0x52, 0xf7, 0x6d,
	0x88, 0xe1, 0x87, 0x2d, 0x00, 0x6e, 0xac, 0x6f, 0xd1, 0x82, 0x48, 0xff, 0x3b, 0xdd, 0xee, 0x6e,
	0x9f, 0x0f, 0xf6, 0xb7, 0xe4, 0x88, 0x7b, 0x16, 0x4d, 0x86, 0x08, 0x50, 0xd3, 0x7e, 0x0b, 0x20,
	0xae, 0xb4, 0x87, 0x60, 0x05, 0x15, 0x30, 0x26, 0x5a, 0xca, 0x90, 0xbb, 0x8e, 0xa6, 0x42, 0x10,
	0x5e, 0x63, 0x7f, 0x1b, 0x50, 0xae, 0xb2, 0x47, 0x81, 0x6a, 0x0c, 0x30, 0x85, 0x96, 0x3a, 0xa6,
	0xe1, 0xf0, 0x0a, 0xfd, 0x3b, 0xa9, 0x71, 0xce, 0x18, 0x70, 0x78, 0x7d, 0x8f, 0x90, 0x46, 0xb0,
	0xf7, 0xbb, 0xc3, 0x90, 0x46, 0x70, 0x8c, 0x34, 0x82, 0x63, 0xa4, 0x11, 0xec, 0xfd, 0xde, 0x50,
	0xa4, 0x09, 0x18, 0x95, 0xb4, 0x08, 0x0e, 0x6f, 0x2d, 0xdf, 0x19, 0x8a, 0xb4, 0x28, 0x0e, 0x6f,
	0x4c, 0x9b, 0xa8, 0x18, 0xe2, 0x88, 0x5e, 0xf3, 0xfb, 0x00, 0x74, 0xb5, 0x3d, 0x10, 0x6f, 0x61,
	0x80, 0x34, 0xd9, 0xd2, 0x06, 0xdd, 0x26, 0x9a, 0x96, 0xd4, 0x09, 0xac, 0x3f, 0x00, 0xac, 0x6b,
	0x52, 0x90, 0xb7, 0xae, 0x82, 0x4d, 0xb5, 0xf4, 0x51, 0x6d, 0x37, 0x40, 0x5f, 0xfc, 0xc3, 0xd4,
	0xbb, 0x81, 0x75, 0x50, 0x7d, 0x37, 0x40, 0x53, 0x8d, 0xb1, 0x47, 0xb0, 0xf7, 0x47, 0xc3, 0xb1,
	0x27, 0xbe, 0x27, 0x8d, 0x3d, 0x82, 0x0d, 0xec, 0x11, 0xec, 0xfd, 0xf1, 0x90, 0xec, 0x09, 0x30,
	0x9d, 0xbd, 0xc8, 0xf6, 0xe3, 0x3d, 0xfd, 0x4f, 0x52, 0x6f, 0x3f, 0xe8, 0xfe, 0xfa, 0xf6, 0xe3,
	0x8a, 0x40, 0x4b, 0x27, 0x50, 0x04, 0x7f, 0x9a, 0x3e, 0x9d, 0x98, 0x83, 0x48, 0x3a, 0x81, 0x9e,
	0x50, 0x77, 0x03, 0xe8, 0x89, 0x3f, 0x4b, 0xbd, 0x1b, 0x98, 0xf2, 0xd0, 0x77, 0x03, 0x88, 0x91,
	0x2d, 0x74, 0x20, 0x04, 0x51, 0xc4, 0xc8, 0x9f, 0x03, 0xd2, 0x2b, 0xec, 0x91, 0x42, 0x01, 0x02,
	0x68, 0xc5, 0x56, 0x64, 0xd8, 0x3d, 0x8f, 0x66, 0x22, 0x88, 0xa2, 0xad, 0xfe, 0x05, 0x80, 0x5e,
	0x9b, 0x12, 0x94, 0x8f, 0x01, 0xee, 0x81, 0x56, 0xfc, 0x13, 0x77, 0x07, 0x1d, 0x0c, 0xa1, 0x55,
	0x89, 0xf2, 0x97, 0x00, 0x7c, 0xca, 0x1e, 0x58, 0xaa, 0x12, 0x80, 0x9d, 0x6e, 0x45, 0xc7, 0xdd,
	0x3b, 0xd0, 0x21, 0xa5, 0xfa, 0x2a, 0x6a, 0xe5, 0xbb, 0x80, 0xba, 0x9c, 0xa6, 0x06, 0x87, 0x3a,
	0x05, 0x60, 0xdd, 0x56, 0xec, 0x03, 0xf7, 0x6e, 0xe4, 0xc5, 0x70, 0x05, 0xd3, 0x7f, 0x05, 0xd0,
	0xa7, 0x53, 0x43, 0x6b, 0x5c, 0x1f, 0x6a, 0x99, 0x3e, 0x13, 0xfb, 0x97, 0x35, 0x3a, 0xd0, 0x1c,
	0x7f, 0x9d, 0x6a, 0xff, 0xb2, 0xce, 0x2f, 0x45, 0x07, 0xdd, 0xbf, 0xe1, 0x90, 0x48, 0xc6, 0x1d,
	0x05, 0xe5, 0x6f, 0x52, 0x25, 0x23, 0x34, 0x7e, 0x09, 0x43, 0x93, 0x51, 0x8e, 0x09, 0x9c, 0x9e,
	0x82, 0xf3, 0xb7, 0xa9, 0x70, 0xce, 0x18, 0x70, 0xe4, 0x98, 0x42, 0x1a, 0xc1, 0x00, 0xf3, 0x77,
	0x69, 0x49, 0x23, 0x38, 0x46, 0x1a, 0x0c, 0xa9, 0xa4, 0x09, 0x94, 0xbf, 0x4f, 0x4d, 0x9a, 0x0a,
	0x23, 0x48, 0xd3, 0x71, 0x7a, 0x0a, 0xce, 0x3f, 0xa4, 0x26, 0x2d, 0x8a, 0x23, 0xc7, 0x44, 0x4b,
	0xe3, 0x6d, 0x14, 0x80, 0xfe, 0x31, 0x55, 0x4b, 0xe3, 0x7d, 0x5f, 0x22, 0xd1, 0x6f, 0x43, 0x19,
	0x0c, 0xa9, 0x63, 0x25, 0x1a, 0x90, 0xfe, 0x29, 0x1d, 0x75, 0xcc, 0x43, 0x84, 0xba, 0x70, 0xcc,
	0x2d, 0x21, 0xd4, 0x69, 0x37, 0x3a, 0xeb, 0x00, 0xf1, 0x74, 0xae, 0xe4, 0x94, 0xf7, 0x55, 0x47,
	0x82, 0x71, 0x36, 0xc8, 0x2c, 0x8e, 0xa0, 0xfd, 0x60, 0x01, 0xf2, 0xf4, 0x19, 0x6a, 0x92, 0xab,
	0x8e, 0x04, 0x30, 0x0f, 0xe4, 0xf2, 0xcb, 0xd0, 0x04, 0xd8, 0x70, 0xad, 0xfc, 0x2c, 0x35, 0x9a,
	0xae, 0x8e, 0x04, 0x30, 0x95, 0x8b, 0xdd, 0xd0, 0x8a, 0x2b, 0xdd, 0xe7, 0xa8, 0x55, 0x21, 0xb4,
	0xe2, 0x52, 0x55, 0xc5, 0x23, 0xd8, 0x7b, 0x9e, 0x1a, 0x65, 0x55, 0x3c, 0x82, 0x75, 0x3c, 0x82,
	0xbd, 0xcf, 0x50, 0x23, 0x57, 0xc3, 0x53, 0xad, 0xb8, 0x48, 0xfc, 0x2c, 0xb5, 0x1a, 0xd5, 0xf0,
	0x08, 0x76, 0x5f, 0x8e, 0x0a, 0x60, 0x25, 0x64, 0xd7, 0xe7, 0xa8, 0xd9, 0x58, 0x75, 0x24, 0x80,
	0xd9, 0x42, 0xa2, 0x95, 0xd1, 0x24, 0xc7, 0x14, 0x86, 0x9f, 0xa7, 0x86, 0x53, 0xd5, 0x91, 0x00,
	0x1c, 0x84, 0xf2, 0x2a, 0x8c, 0x00, 0xb4, 0xd5, 0x2f, 0x53, 0xb3, 0x4c, 0x18, 0x01, 0xa8, 0x23,
	0x1d, 0x95, 0x60, 0xef, 0x57, 0xa8, 0x55, 0x5e, 0x47, 0x65, 0x07, 0x08, 0x1a, 0x2a, 0xc1, 0xde,
	0xaf, 0x52, 0xc3, 0x62, 0x04, 0x55, 0x8d, 0x96, 0x6b, 0x92, 0x17, 0xa8, 0x9d, 0x13, 0x46, 0xcb,
	0x45, 0x85, 0x64, 0x0e, 0x14, 0xc5, 0x17, 0xa8, 0xd5, 0xb8, 0x64, 0x0e, 0x24, 0x41, 0x18, 0x01,
	0xe8, 0x81, 0x2f, 0x52, 0xa3, 0x89, 0x30, 0x02, 0xe8, 0xe8, 0x75, 0x54, 0x04, 0x1b, 0xa5, 0x9d,
	0x7f, 0x29, 0x97, 0xfe, 0x31, 0x6e, 0x75, 0x24, 0x80, 0x50, 0x65, 0x0b, 0xbf, 0x15, 0x1d, 0x50,
	0x21, 0x44, 0x57, 0xf9, 0x72, 0x6e, 0xa8, 0x57, 0x6c, 0xaa, 0x23, 0xc1, 0xb4, 0x04, 0x12, 0x5d,
	0x64, 0x0d, 0xc1, 0xa0, 0xd6, 0xb0, 0xbf, 0x92, 0x1b, 0xe2, 0xfd, 0x9a, 0xea, 0x48, 0x30, 0xc5,
	0x5c, 0x2a, 0x4d, 0x7a, 0x05, 0xb9, 0x62, 0xe3, 0x2a, 0x1d, 0xfa, 0xab, 0xb9, 0x34, 0xcf, 0xa2,
	0xab, 0x23, 0x41, 0x91, 0x6f, 0x77, 0xd9, 0x8d, 0xcf, 0xa1, 0x43, 0x3a, 0x80, 0x20, 0xed, 0x6b,
	0xb9, 0x94, 0x6f, 0xd6, 0x54, 0x47, 0x82, 0x03, 0x2a, 0x8c, 0x20, 0xec, 0xc7, 0x78, 0xe5, 0x00,
	0xa6, 0xbe, 0x9e, 0xb3, 0x7e, 0x4d, 0xf0, 0x26, 0x3a, 0x5b, 0x30, 0xa5, 0xf8, 0x92, 0xb9, 0x01,
	0x7b, 0x74, 0xc1, 0xfb, 0x86, 0xd8, 0xa4, 0x13, 0xca, 0x26, 0x5d, 0x88, 0xda, 0xf9, 0xde, 0x37,
	0x4d, 0x76, 0x7e, 0xd4, 0x6e, 0xd1, 0xfb, 0x96, 0xc9, 0x6e, 0xd1, 0x3d, 0x89, 0x0e, 0xf2, 0x0c,
	0xd2, 0x1f, 0x68, 0xdd, 0x9b, 0x97, 0x2f, 0xf4, 0x54, 0x9d, 0x00, 0xbe, 0x41, 0xfd, 0x79, 0xd6,
	0x55, 0x82, 0xf6, 0xe8, 0xc3, 0xac, 0xf7, 0xe5, 0xd5, 0xb7, 0x7b, 0xaa, 0x0e, 0xe7, 0x32, 0xf2,
	0x2c, 0xeb, 0x6a, 0x34, 0x13, 0x9d, 0xce, 0x2b, 0xe9, 0x7d, 0x79, 0xe5, 0x55, 0x9f, 0xaa, 0x13,
	0x1c, 0xd4, 0xa7, 0xf3, 0xca, 0x7a, 0x55, 0x7c, 0x3e, 0xaf, 0xb1, 0xf7, 0xe7, 0xe5, 0x7b, 0x3f,
	0xf1, 0xe9, 0x67, 0xc4, 0x63, 0x30, 0xd3, 0xea, 0x09, 0xf6, 0x1e, 0xc8, 0x47, 0x5f, 0x02, 0x32,
	0x46, 0x40, 0x70, 0x52, 0x04, 0x04, 0x7b, 0x0f, 0xe6, 0x95, 0x37, 0x82, 0xcc, 0x11, 0x10, 0x9c,
	0x14, 0x01, 0xc1, 0xde, 0x43, 0x79, 0xf9, 0x7a, 0x90, 0x39, 0x02, 0xf6, 0xe8, 0x6b, 0x36, 0x3a,
	0x5d, 0x54, 0xe9, 0x87, 0xf3, 0xea, 0xbb, 0x42, 0x55, 0x27, 0x38, 0xa4, 0x7b, 0x10, 0xf5, 0xfd,
	0x3a, 0xe4, 0xc5, 0x22, 0x10, 0x3e, 0x1e, 0xc9, 0x6b, 0x2f, 0x0e, 0x55, 0x9d, 0x60, 0x26, 0x12,
	0x85, 0xa8, 0xfd, 0x57, 0xc7, 0xa9, 0x84, 0x2e, 0xf0, 0xfe, 0xbc, 0xf6, 0x16, 0x51, 0x9c, 0x47,
	0xe8, 0x0b, 0x49, 0x81, 0x10, 0xec, 0x7d, 0x20, 0xaf, 0xbe, 0x52, 0x94, 0x10, 0x08, 0xc1, 0xc9,
	0x81, 0x10, 0xec, 0x3d, 0x9a, 0xd7, 0xde, 0x2f, 0x4a, 0x0a, 0x84, 0x60, 0xf7, 0xfa, 0xf8, 0x17,
	0xc2, 0x1b, 0xcb, 0x63, 0x79, 0xc3, 0xcb, 0x46, 0xf1, 0x6f, 0x86, 0x37, 0x9c, 0x1b, 0x0c, 0x1b,
	0x03, 0x5a, 0xcf, 0xe3, 0x79, 0xf3, 0x9b, 0x47, 0x86, 0x3d, 0x02, 0x5d, 0xe9, 0xa6, 0x38, 0xb7,
	0xd0, 0x9f, 0x9e, 0xc8, 0x0f, 0x7e, 0x0d, 0x29, 0x4e, 0x36, 0xb4, 0xb0, 0xd7, 0xa2, 0xb9, 0xa8,
	0x43, 0xa5, 0x99, 0x3d, 0x99, 0x1f, 0xfa, 0x9d, 0xa4, 0xaa, 0x13, 0xcc, 0xea, 0xc0, 0xea, 0xdf,
	0xa7, 0x17, 0xc7, 0x33, 0x46, 0x69, 0x0a, 0x4f, 0xe5, 0x87, 0x78, 0x41, 0xa9, 0xea, 0x04, 0x87,
	0xa3, 0x79, 0x16, 0xda, 0xcc, 0xfd, 0x14, 0x9a, 0xd0, 0x7a, 0xdf, 0x8b, 0xf8, 0xa6, 0xf9, 0xdc,
	0x5d, 0x08, 0x29, 0xfd, 0xf0, 0xc5, 0x44, 0xbe, 0x06, 0x15, 0xb4, 0x37, 0x39, 0x6d, 0xc1, 0xa9,
	0x03, 0xed, 0x9d, 0x88, 0x74, 0x0e, 0x94, 0xc3, 0x73, 0x6b, 0x07, 0x57, 0xa3, 0x62, 0xf4, 0x70,
	0xdc, 0x2d, 0xa2, 0xec, 0x6d, 0x8d, 0xf3, 0xcc, 0xc9, 0xbe, 0x80, 0xfe, 0xaf, 0x7b, 0x10, 0xe5,
	0xee, 0xa8, 0x37, 0x7b, 0x0d, 0x2f, 0xc3, 0xc6, 0xe0, 0x1f, 0x95, 0xcc, 0x15, 0xce, 0xdc, 0x35,
	0x68, 0x3a, 0x76, 0xf2, 0xbd, 0x9b, 0x83, 0x9c, 0xea, 0xe0, 0x15, 0xc8, 0x8d, 0x1f, 0x6a, 0xef,
	0xe6, 0x61, 0xda, 0xec, 0xe1, 0xcc, 0xde, 0x3d, 0x14, 0x12, 0x83, 0xe0, 0xa7, 0x74, 0xbb, 0x39,
	0xc8, 0x26, 0x07, 0xb1, 0x47, 0x0f, 0x6e, 0x72, 0x10, 0x7b, 0xf4, 0x30, 0xaa, 0x7a, 0x38, 0x85,
	0x0e, 0x18, 0xce, 0x85, 0x77, 0x73, 0x31, 0xa6, 0xba, 0x58, 0x46, 0x07, 0x4d, 0xc7, 0xbd, 0xbb,
	0xf9, 0x98, 0x32, 0x73, 0x29, 0xcf, 0x71, 0x77, 0x73, 0x90, 0x19, 0x10, 0xc7, 0x1e, 0xa9, 0xc8,
	0x0f, 0x8a, 0x63, 0x8f, 0x3e, 0x8a, 0xe6, 0x2f, 0x44, 0x39, 0x50, 0xdd, 0xcd, 0x83, 0x93, 0xb0,
	0x29, 0xe4, 0x51, 0xe9, 0x6e, 0x1e, 0xc6, 0xcd, 0x5c, 0xca, 0x53, 0xd0, 0xdd, 0x1c, 0x4c, 0xa8,
	0x0e, 0xce, 0xa3, 0x43, 0xc6, 0xc3, 0x4d, 0x83, 0x93, 0x57, 0xa9, 0x4e, 0xd2, 0x3e, 0xcc, 0x55,
	0xa0, 0xef, 0x46, 0x5e, 0xd2, 0x11, 0xa7, 0x01, 0xfd, 0x46, 0x15, 0x7d, 0x88, 0x07, 0xbc, 0xca,
	0x02, 0x5e, 0x8b, 0x66, 0xcc, 0x47, 0x9d, 0x06, 0xf8, 0x1f, 0xd1, 0xe1, 0x53, 0x3e, 0xf1, 0x55,
	0xc0, 0x7b, 0x68, 0x36, 0xe1, 0xc4, 0xd3, 0x80, 0x7e, 0x9d, 0x4e, 0xbd, 0xed, 0x43, 0x60, 0x2d,
	0xe6, 0xb9, 0xe4, 0xd3, 0x4e, 0x03, 0xf2, 0x2b, 0xf5, 0xb8, 0x53, 0x3c, 0x16, 0x8e, 0xed, 0x56,
	0xfd, 0xcc, 0x53, 0xc5, 0xcc, 0xed, 0xd6, 0x4b, 0x20, 0x61, 0x22, 0xc7, 0x99, 0xaa, 0x87, 0xe9,
	0xbd, 0x79, 0x38, 0x93, 0xec, 0xa1, 0xb0, 0xb7, 0x7e, 0xa6, 0x9f, 0x41, 0xaa, 0x0e, 0xb2, 0x7b,
	0x0f, 0x22, 0xc1, 0x83, 0xbb, 0xf7, 0x20, 0x12, 0x3c, 0x8c, 0xee, 0xe6, 0x01, 0x4a, 0x68, 0xf4,
	0x44, 0x50, 0x75, 0x31, 0xb6, 0xc7, 0x30, 0xf4, 0xa3, 0x3e, 0xd5, 0xc3, 0xf8, 0x6e, 0x1e, 0xae,
	0x44, 0x48, 0xfe, 0x3d, 0x6e, 0xad, 0x4b, 0xaa, 0x68, 0xe6, 0xf4, 0x5d, 0xdd, 0x46, 0x7b, 0x67,
	0xb3, 0xd3, 0x1e, 0x4e, 0x63, 0xa9, 0x9e, 0x86, 0xd2, 0x4a, 0x47, 0xe6, 0xd1, 0xb8, 0x14, 0xdb,
	0xe3, 0x08, 0x74, 0x71, 0x71, 0x84, 0xfe, 0xef, 0x72, 0x70, 0xea, 0x47, 0x6f, 0x2a, 0x3a, 0xee,
	0x7e, 0x34, 0x76, 0x6d, 0xf5, 0x54, 0xf0, 0xea, 0x1b, 0x4e, 0x17, 0x33, 0x97, 0x8d, 0xef, 0xbb,
	0xa7, 0x56, 0xbc, 0x70, 0xe1, 0xc2, 0x85, 0x8c, 0x7f, 0x16, 0xcd, 0x36, 0xc4, 0x22, 0x56, 0xb4,
	0x3b, 0x8b, 0xae, 0x85, 0xe8, 0xf4, 0xee, 0xa9, 0x31, 0x96, 0x0f, 0x35, 0xa2, 0xd4, 0xd0, 0xaf,
	0xc8, 0x6f, 0x20, 0xcf, 0x00, 0x02, 0x7f, 0x90, 0xdb, 0xa0, 0xbc, 0xa1, 0xc6, 0xb2, 0x75, 0x26,
	0x86, 0xc2, 0x72, 0xdb, 0xdf, 0x40, 0x87, 0x0d, 0x30, 0x3b, 0xf6, 0x38, 0x6f, 0xac, 0xb1, 0x9c,
	0x9e, 0x8d, 0xe1, 0x40, 0x09, 0x48, 0x00, 0xea, 0xd9, 0x03, 0xbd, 0xa9, 0xc6, 0x52, 0x3f, 0x0e,
	0x04, 0x95, 0x22, 0x99, 0x38, 0x82, 0xad, 0x70, 0xde, 0x5c, 0x63, 0x15, 0xc2, 0x48, 0x1c, 0xc1,
	0x03, 0x88, 0xb3, 0xc4, 0x79, 0x4b, 0x8d, 0xd5, 0x11, 0x33, 0x71, 0x89, 0x40, 0x3d, 0x7b, 0xa0,
	0xb7, 0xd6, 0x58, 0xb9, 0x31, 0x13, 0x47, 0xb0, 0xbf, 0x89, 0xe6, 0x0c, 0x40, 0xe2, 0xe4, 0xc2,
	0x06, 0xe9, 0x6d, 0x35, 0x56, 0x95, 0xbc, 0x18, 0x12, 0xaf, 0x62, 0xfe, 0x6d, 0xe8, 0x22, 0x13,
	0x79, 0x69, 0xb0, 0xde, 0x5e, 0x63, 0xa2, 0xf5, 0x70, 0x9c, 0x3e, 0xee, 0x2d, 0x61, 0x43, 0xac,
	0xc3, 0xab, 0x7d, 0x16, 0x48, 0xef, 0xa8, 0x31, 0x75, 0x1b, 0xdf, 0x10, 0x4c, 0x1b, 0x0f, 0xa2,
	0xcf, 0xf2, 0x8b, 0x7a, 0x67, 0x8d, 0x69, 0xe0, 0x04, 0xfa, 0x08, 0x1e, 0x48, 0x9f, 0x25, 0xd6,
	0xbb, 0x6a, 0x4c, 0x2b, 0x27, 0xd1, 0x97, 0xb8, 0xff, 0xe0, 0xb0, 0xc7, 0x0a, 0xaa, 0x5f, 0x63,
	0xa2, 0x3a, 0xbe, 0xff, 0x40, 0x93, 0x27, 0x65, 0x14, 0x1c, 0xee, 0xd8, 0x00, 0xbd, 0xbb, 0xc6,
	0xba, 0x80, 0x21, 0xa3, 0xe0, 0xc4, 0xd7, 0xbc, 0x21, 0xd8, 0x59, 0x91, 0x15, 0xce, 0x7b, 0x6a,
	0x4c, 0xa2, 0xc7, 0x37, 0x04, 0x13, 0xf8, 0xfe, 0x03, 0x0e, 0xba, 0xc4, 0x80, 0x23, 0x8f, 0x90,
	0xac, 0xc0, 0xde, 0x5b, 0x1b, 0x42, 0xca, 0xcf, 0xc5, 0x96, 0x18, 0x7e, 0xe6, 0x3f, 0xee, 0xa0,
	0x52, 0xe2, 0x32, 0xf9, 0xe3, 0x01, 0xab, 0x95, 0xde, 0x5b, 0x1b, 0x4e, 0xf6, 0x5f, 0x62, 0x5e,
	0x2c, 0xff, 0xd8, 0x7f, 0xd8, 0x41, 0xdf, 0x67, 0x58, 0xaf, 0xf2, 0x5c, 0xc6, 0x6a, 0xb5, 0xef,
	0xab, 0x0d, 0xf3, 0x57, 0xc2, 0x45, 0xb1, 0xb5, 0xca, 0x0f, 0xfd, 0xfb, 0x1c, 0x74, 0xa9, 0xb1,
	0x47, 0xc8, 0x63, 0x3c, 0xab, 0xa5, 0xde, 0x57, 0x4b, 0xf5, 0x27, 0xc5, 0xc5, 0x86, 0xce, 0x12,
	0x7e, 0xea, 0x3f, 0xea, 0xa0, 0x23, 0x03, 0x16, 0x99, 0x66, 0x03, 0xdc, 0x5f, 0x4b, 0xfb, 0x07,
	0xc8, 0xa5, 0x49, 0x4b, 0x15, 0x5f, 0xfe, 0x43, 0x0e, 0x92, 0xe9, 0xa6, 0xdf, 0xb4, 0xb6, 0x59,
	0xe1, 0x03, 0x35, 0xf6, 0x38, 0xca, 0xe6, 0x4d, 0x1b, 0xb3, 0x80, 0x0d, 0x12, 0x56, 0xe3, 0x37,
	0xd5, 0x1a, 0xa3, 0x3f, 0x30, 0xb2, 0x4b, 0xa6, 0xbe, 0x7a, 0x5d, 0x5c, 0xa2, 0x69, 0xcf, 0x97,
	0xfc, 0x2d, 0xb5, 0x74, 0x46, 0x9e, 0x31, 0xd9, 0x65, 0x43, 0x5f, 0xbf, 0x6e, 0x3e, 0x1b, 0x07,
	0x04, 0xdd, 0x78, 0xbb, 0xda, 0xed, 0xa2, 0x8f, 0xa5, 0xec, 0x76, 0x75, 0x5f, 0xbb, 0xae, 0xee,
	0xc5, 0x11, 0xb9, 0x82, 0xdc, 0x32, 0x43, 0xa6, 0x90, 0x90, 0xf7, 0xf7, 0xd5, 0xeb, 0xee, 0x06,
	0x44, 0x2e, 0x25, 0xbb, 0x89, 0xb4, 0x5a, 0x76, 0xd9, 0x07, 0xfa, 0xf1, 0xeb, 0xf2, 0x66, 0x6a,
	0x09, 0x1e, 0x44, 0xad, 0x25, 0xec, 0x83, 0x7d, 0xed, 0xba, 0x7d, 0x02, 0xb5, 0x04, 0x0f, 0xa2,
	0xd6, 0x12, 0xf2, 0xa1, 0xbe, 0x7a, 0x5d, 0x3f, 0x81, 0x5a, 0x82, 0xfd, 0xae, 0x2a, 0x61, 0x62,
	0x4f, 0xe5, 0xac, 0x20, 0x1f, 0xee, 0xeb, 0xd7, 0xfd, 0x0f, 0xc7, 0x41, 0x85, 0xee, 0xbc, 0x13,
	0x5d, 0x6c, 0xa4, 0x36, 0x0d, 0xec, 0x23, 0xfd, 0xc8, 0xcf, 0x05, 0xcc, 0x19, 0xe8, 0x15, 0x1a,
	0xf4, 0x76, 0xf3, 0x4e, 0xb2, 0x17, 0xa1, 0xef, 0xef, 0x47, 0x7e, 0x6e, 0xc0, 0xb0, 0x8d, 0x40,
	0x8f, 0x0e, 0x62, 0xd8, 0xf2, 0x4b, 0xfd, 0x40, 0x5f, 0xff, 0xb9, 0x82, 0x24, 0x86, 0x09, 0x1e,
	0xcc, 0xb0, 0x25, 0xec, 0xa3, 0xfd, 0xc8, 0xcf, 0x1d, 0x24, 0x32, 0x4c, 0xb0, 0x7f, 0xde, 0xbc,
	0x85, 0x53, 0xe8, 0xd4, 0xc7, 0xfa, 0xc6, 0x9f, 0x4b, 0x30, 0xec, 0x65, 0x2e, 0x5c, 0x5f, 0x97,
	0x90, 0xb0, 0xf6, 0xca, 0xf5, 0xf1, 0x7e, 0xd2, 0xcf, 0x2d, 0x98, 0x72, 0x17, 0xd4, 0xec, 0xeb,
	0x1d, 0xf3, 0xde, 0xb2, 0xd7, 0xb3, 0x4f, 0xf4, 0x77, 0xfb, 0xbd, 0x06, 0xc3, 0x66, 0x03, 0xad,
	0xfb, 0x84, 0x26, 0xca, 0x4c, 0xcf, 0x4b, 0xad, 0x56, 0xf2, 0x64, 0xff, 0x7b, 0xf0, 0x83, 0x0f,
	0x17, 0xc5, 0x17, 0x2b, 0x55, 0xef, 0x63, 0x9a, 0xea, 0x35, 0x3f, 0x63, 0xb5, 0x5a, 0xf2, 0x53,
	0xfd, 0xa1, 0x7e, 0x31, 0xe2, 0x12, 0x53, 0x6d, 0x96, 0x2a, 0x6d, 0x4d, 0x3d, 0x72, 0xd2, 0x2e,
	0x0b, 0xda, 0x2d, 0xf2, 0xbb, 0x0e, 0xbb, 0x59, 0x28, 0xcf, 0x9c, 0x02, 0xe5, 0x8a, 0xa1, 0xbf,
	0xae, 0x8a, 0x16, 0xfd, 0xb2, 0xa1, 0x15, 0xcc, 0x07, 0x19, 0x8c, 0x7a, 0xe8, 0x14, 0xa8, 0x57,
	0x14, 0xfd, 0x73, 0xea, 0x8e, 0x8d, 0x5c, 0x56, 0xb4, 0x02, 0xfa, 0x10, 0x03, 0x52, 0x4f, 0x9d,
	0x02, 0xed, 0x8a, 0x63, 0x02, 0x52, 0x0a, 0xc9, 0xf0, 0x61, 0x86, 0x54, 0x30, 0x20, 0x71, 0xad,
	0x90, 0xc8, 0x9d, 0x65, 0xd1, 0xfb, 0x08, 0x03, 0xca, 0x9a, 0xb9, 0x23, 0x78, 0x00, 0x77, 0x96,
	0x40, 0x1f, 0x65, 0x40, 0x6e, 0x02, 0x77, 0x89, 0x48, 0x29, 0x34, 0xc1, 0xc7, 0x18, 0xd2, 0x68,
	0x02, 0x77, 0x04, 0xfb, 0xb7, 0xaa, 0x05, 0x34, 0x7a, 0xd9, 0xd3, 0x0a, 0xea, 0xe3, 0x0c, 0x4a,
	0x3d, 0x7a, 0x0a, 0xf4, 0x2b, 0xa2, 0x7e, 0x53, 0x6d, 0x8b, 0xb1, 0xcb, 0xa2, 0x56, 0x60, 0x9f,
	0x60, 0x60, 0xea, 0xd9, 0x53, 0x10, 0xb9, 0x62, 0x9a, 0xb0, 0x2b, 0xec, 0xdb, 0xfe, 0x27, 0x19,
	0x54, 0xc6, 0xb0, 0x2b, 0xa0, 0xd9, 0x0f, 0x60, 0xd0, 0xf2, 0xcb, 0xfa, 0x14, 0x43, 0xca, 0x27,
	0x31, 0x48, 0xf0, 0x40, 0x06, 0x2d, 0xc1, 0x3e, 0xcd, 0xc0, 0x8a, 0x89, 0x0c, 0x26, 0xee, 0xc2,
	0x14, 0x6d, 0xfd, 0x69, 0x86, 0xe5, 0x18, 0x76, 0x21, 0x6f, 0xe3, 0x09, 0x99, 0x65, 0xdf, 0xc5,
	0x9f, 0x61, 0x48, 0xe3, 0xa6, 0xcc, 0x82, 0x96, 0x6d, 0xde, 0x15, 0xf6, 0x0d, 0xfb, 0x59, 0x06,
	0x34, 0x61, 0xd8, 0x15, 0xd0, 0x95, 0x1f, 0xd4, 0x4e, 0xa0, 0x0c, 0xb7, 0x7d, 0xad, 0xd0, 0x9e,
	0x63, 0x68, 0xc3, 0x1f, 0x41, 0x05, 0xd1, 0x3b, 0xc2, 0x54, 0x3d, 0x94, 0x12, 0xd7, 0x99, 0xe6,
	0x04, 0xe2, 0x79, 0xb6, 0xd4, 0xef, 0xc9, 0x19, 0x54, 0x60, 0xb8, 0x64, 0xec, 0x3f, 0xa2, 0xc9,
	0x1d, 0xd3, 0x7d, 0x63, 0xab, 0xe5, 0x7e, 0x86, 0x2f, 0x77, 0xe8, 0x43, 0xa8, 0x20, 0x76, 0x4b,
	0xd9, 0xbf, 0x5f, 0x3b, 0x84, 0x32, 0x5e, 0x58, 0xb6, 0x5a, 0xeb, 0x67, 0xf9, 0x2e, 0x48, 0x7f,
	0x0a, 0x15, 0xc4, 0xaf, 0x39, 0x53, 0x39, 0x76, 0x64, 0xc0, 0x2a, 0xd3, 0xec, 0x81, 0xcf, 0x71,
	0x52, 0x87, 0x3a, 0x86, 0x0a, 0x8c, 0xf7, 0xa4, 0xfd, 0x87, 0xd5, 0x63, 0x28, 0xfd, 0x86, 0xb3,
	0xcd, 0x12, 0x3f, 0xcf, 0x96, 0x98, 0xf2, 0x1c, 0x4a, 0xbf, 0x67, 0x9d, 0xb0, 0x9c, 0xe5, 0x82,
	0x78, 0x55, 0xbf, 0xd7, 0xde, 0xec, 0xb4, 0x97, 0x67, 0xe3, 0xef, 0x48, 0xb2, 0x0f, 0x2e, 0x5b,
	0x40, 0xfb, 0xd5, 0xf7, 0xc4, 0x4d, 0x0f, 0x44, 0x91, 0x3b, 0x21, 0x1f, 0x88, 0xbe, 0xe0, 0x2c,
	0xbf, 0xe6, 0xc7, 0x6b, 0xb1, 0x65, 0x1f, 0x63, 0xcb, 0x5e, 0xed, 0xad, 0x1f, 0xdb, 0x6c, 0x77,
	0x1b, 0xdb, 0xed, 0x7a, 0x93, 0xfd, 0xce, 0x2d, 0x1b, 0xdd, 0x39, 0xd6, 0x6c, 0x6c, 0xd4, 0xcf,
	0x9e, 0x3f, 0x96, 0xf4, 0x93, 0xb8, 0xff, 0x1f, 0x00, 0x00, 0xff, 0xff, 0x83, 0x33, 0xd4, 0x34,
	0x2d, 0x57, 0x00, 0x00,
}
