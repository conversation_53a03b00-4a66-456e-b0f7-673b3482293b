// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Test Protobuf definitions with proto2 syntax.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/textpbeditions/test2.proto

package textpbeditions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

type Enum int32

const (
	Enum_ONE Enum = 1
	Enum_TWO Enum = 2
	Enum_TEN Enum = 10
)

// Enum value maps for Enum.
var (
	Enum_name = map[int32]string{
		1:  "ONE",
		2:  "TWO",
		10: "TEN",
	}
	Enum_value = map[string]int32{
		"ONE": 1,
		"TWO": 2,
		"TEN": 10,
	}
)

func (x Enum) Enum() *Enum {
	p := new(Enum)
	*p = x
	return p
}

func (x Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_textpbeditions_test2_proto_enumTypes[0].Descriptor()
}

func (Enum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_textpbeditions_test2_proto_enumTypes[0]
}

func (x Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Enum.Descriptor instead.
func (Enum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{0}
}

type OpenEnum int32

const (
	OpenEnum_UNKNOWN OpenEnum = 0
	OpenEnum_EINS    OpenEnum = 1
	OpenEnum_ZWEI    OpenEnum = 2
	OpenEnum_ZEHN    OpenEnum = 10
)

// Enum value maps for OpenEnum.
var (
	OpenEnum_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "EINS",
		2:  "ZWEI",
		10: "ZEHN",
	}
	OpenEnum_value = map[string]int32{
		"UNKNOWN": 0,
		"EINS":    1,
		"ZWEI":    2,
		"ZEHN":    10,
	}
)

func (x OpenEnum) Enum() *OpenEnum {
	p := new(OpenEnum)
	*p = x
	return p
}

func (x OpenEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OpenEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_textpbeditions_test2_proto_enumTypes[1].Descriptor()
}

func (OpenEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_textpbeditions_test2_proto_enumTypes[1]
}

func (x OpenEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OpenEnum.Descriptor instead.
func (OpenEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{1}
}

type Enums_NestedEnum int32

const (
	Enums_UNO  Enums_NestedEnum = 1
	Enums_DOS  Enums_NestedEnum = 2
	Enums_DIEZ Enums_NestedEnum = 10
)

// Enum value maps for Enums_NestedEnum.
var (
	Enums_NestedEnum_name = map[int32]string{
		1:  "UNO",
		2:  "DOS",
		10: "DIEZ",
	}
	Enums_NestedEnum_value = map[string]int32{
		"UNO":  1,
		"DOS":  2,
		"DIEZ": 10,
	}
)

func (x Enums_NestedEnum) Enum() *Enums_NestedEnum {
	p := new(Enums_NestedEnum)
	*p = x
	return p
}

func (x Enums_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enums_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_textpbeditions_test2_proto_enumTypes[2].Descriptor()
}

func (Enums_NestedEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_textpbeditions_test2_proto_enumTypes[2]
}

func (x Enums_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Enums_NestedEnum.Descriptor instead.
func (Enums_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{4, 0}
}

type Enums_NestedOpenEnum int32

const (
	Enums_UNKNOWN Enums_NestedOpenEnum = 0
	Enums_EINS    Enums_NestedOpenEnum = 1
	Enums_ZWEI    Enums_NestedOpenEnum = 2
	Enums_ZEHN    Enums_NestedOpenEnum = 10
)

// Enum value maps for Enums_NestedOpenEnum.
var (
	Enums_NestedOpenEnum_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "EINS",
		2:  "ZWEI",
		10: "ZEHN",
	}
	Enums_NestedOpenEnum_value = map[string]int32{
		"UNKNOWN": 0,
		"EINS":    1,
		"ZWEI":    2,
		"ZEHN":    10,
	}
)

func (x Enums_NestedOpenEnum) Enum() *Enums_NestedOpenEnum {
	p := new(Enums_NestedOpenEnum)
	*p = x
	return p
}

func (x Enums_NestedOpenEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enums_NestedOpenEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_textpbeditions_test2_proto_enumTypes[3].Descriptor()
}

func (Enums_NestedOpenEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_textpbeditions_test2_proto_enumTypes[3]
}

func (x Enums_NestedOpenEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Enums_NestedOpenEnum.Descriptor instead.
func (Enums_NestedOpenEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{4, 1}
}

// Scalars contains scalar fields.
type Scalars struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptBool     *bool    `protobuf:"varint,1,opt,name=opt_bool,json=optBool" json:"opt_bool,omitempty"`
	OptInt32    *int32   `protobuf:"varint,2,opt,name=opt_int32,json=optInt32" json:"opt_int32,omitempty"`
	OptInt64    *int64   `protobuf:"varint,3,opt,name=opt_int64,json=optInt64" json:"opt_int64,omitempty"`
	OptUint32   *uint32  `protobuf:"varint,4,opt,name=opt_uint32,json=optUint32" json:"opt_uint32,omitempty"`
	OptUint64   *uint64  `protobuf:"varint,5,opt,name=opt_uint64,json=optUint64" json:"opt_uint64,omitempty"`
	OptSint32   *int32   `protobuf:"zigzag32,6,opt,name=opt_sint32,json=optSint32" json:"opt_sint32,omitempty"`
	OptSint64   *int64   `protobuf:"zigzag64,7,opt,name=opt_sint64,json=optSint64" json:"opt_sint64,omitempty"`
	OptFixed32  *uint32  `protobuf:"fixed32,8,opt,name=opt_fixed32,json=optFixed32" json:"opt_fixed32,omitempty"`
	OptFixed64  *uint64  `protobuf:"fixed64,9,opt,name=opt_fixed64,json=optFixed64" json:"opt_fixed64,omitempty"`
	OptSfixed32 *int32   `protobuf:"fixed32,10,opt,name=opt_sfixed32,json=optSfixed32" json:"opt_sfixed32,omitempty"`
	OptSfixed64 *int64   `protobuf:"fixed64,11,opt,name=opt_sfixed64,json=optSfixed64" json:"opt_sfixed64,omitempty"`
	OptFloat    *float32 `protobuf:"fixed32,20,opt,name=opt_float,json=optFloat" json:"opt_float,omitempty"`
	OptDouble   *float64 `protobuf:"fixed64,21,opt,name=opt_double,json=optDouble" json:"opt_double,omitempty"`
	OptBytes    []byte   `protobuf:"bytes,14,opt,name=opt_bytes,json=optBytes" json:"opt_bytes,omitempty"`
	OptString   *string  `protobuf:"bytes,13,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *Scalars) Reset() {
	*x = Scalars{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scalars) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scalars) ProtoMessage() {}

func (x *Scalars) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scalars.ProtoReflect.Descriptor instead.
func (*Scalars) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{0}
}

func (x *Scalars) GetOptBool() bool {
	if x != nil && x.OptBool != nil {
		return *x.OptBool
	}
	return false
}

func (x *Scalars) GetOptInt32() int32 {
	if x != nil && x.OptInt32 != nil {
		return *x.OptInt32
	}
	return 0
}

func (x *Scalars) GetOptInt64() int64 {
	if x != nil && x.OptInt64 != nil {
		return *x.OptInt64
	}
	return 0
}

func (x *Scalars) GetOptUint32() uint32 {
	if x != nil && x.OptUint32 != nil {
		return *x.OptUint32
	}
	return 0
}

func (x *Scalars) GetOptUint64() uint64 {
	if x != nil && x.OptUint64 != nil {
		return *x.OptUint64
	}
	return 0
}

func (x *Scalars) GetOptSint32() int32 {
	if x != nil && x.OptSint32 != nil {
		return *x.OptSint32
	}
	return 0
}

func (x *Scalars) GetOptSint64() int64 {
	if x != nil && x.OptSint64 != nil {
		return *x.OptSint64
	}
	return 0
}

func (x *Scalars) GetOptFixed32() uint32 {
	if x != nil && x.OptFixed32 != nil {
		return *x.OptFixed32
	}
	return 0
}

func (x *Scalars) GetOptFixed64() uint64 {
	if x != nil && x.OptFixed64 != nil {
		return *x.OptFixed64
	}
	return 0
}

func (x *Scalars) GetOptSfixed32() int32 {
	if x != nil && x.OptSfixed32 != nil {
		return *x.OptSfixed32
	}
	return 0
}

func (x *Scalars) GetOptSfixed64() int64 {
	if x != nil && x.OptSfixed64 != nil {
		return *x.OptSfixed64
	}
	return 0
}

func (x *Scalars) GetOptFloat() float32 {
	if x != nil && x.OptFloat != nil {
		return *x.OptFloat
	}
	return 0
}

func (x *Scalars) GetOptDouble() float64 {
	if x != nil && x.OptDouble != nil {
		return *x.OptDouble
	}
	return 0
}

func (x *Scalars) GetOptBytes() []byte {
	if x != nil {
		return x.OptBytes
	}
	return nil
}

func (x *Scalars) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

// ImplicitScalars contains scalar field types with implicit field_presence
type ImplicitScalars struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SBool     bool    `protobuf:"varint,1,opt,name=s_bool,json=sBool" json:"s_bool,omitempty"`
	SInt32    int32   `protobuf:"varint,2,opt,name=s_int32,json=sInt32" json:"s_int32,omitempty"`
	SInt64    int64   `protobuf:"varint,3,opt,name=s_int64,json=sInt64" json:"s_int64,omitempty"`
	SUint32   uint32  `protobuf:"varint,4,opt,name=s_uint32,json=sUint32" json:"s_uint32,omitempty"`
	SUint64   uint64  `protobuf:"varint,5,opt,name=s_uint64,json=sUint64" json:"s_uint64,omitempty"`
	SSint32   int32   `protobuf:"zigzag32,6,opt,name=s_sint32,json=sSint32" json:"s_sint32,omitempty"`
	SSint64   int64   `protobuf:"zigzag64,7,opt,name=s_sint64,json=sSint64" json:"s_sint64,omitempty"`
	SFixed32  uint32  `protobuf:"fixed32,8,opt,name=s_fixed32,json=sFixed32" json:"s_fixed32,omitempty"`
	SFixed64  uint64  `protobuf:"fixed64,9,opt,name=s_fixed64,json=sFixed64" json:"s_fixed64,omitempty"`
	SSfixed32 int32   `protobuf:"fixed32,10,opt,name=s_sfixed32,json=sSfixed32" json:"s_sfixed32,omitempty"`
	SSfixed64 int64   `protobuf:"fixed64,11,opt,name=s_sfixed64,json=sSfixed64" json:"s_sfixed64,omitempty"`
	SFloat    float32 `protobuf:"fixed32,20,opt,name=s_float,json=sFloat" json:"s_float,omitempty"`
	SDouble   float64 `protobuf:"fixed64,21,opt,name=s_double,json=sDouble" json:"s_double,omitempty"`
	SBytes    []byte  `protobuf:"bytes,14,opt,name=s_bytes,json=sBytes" json:"s_bytes,omitempty"`
	SString   string  `protobuf:"bytes,13,opt,name=s_string,json=sString" json:"s_string,omitempty"`
}

func (x *ImplicitScalars) Reset() {
	*x = ImplicitScalars{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImplicitScalars) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImplicitScalars) ProtoMessage() {}

func (x *ImplicitScalars) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImplicitScalars.ProtoReflect.Descriptor instead.
func (*ImplicitScalars) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{1}
}

func (x *ImplicitScalars) GetSBool() bool {
	if x != nil {
		return x.SBool
	}
	return false
}

func (x *ImplicitScalars) GetSInt32() int32 {
	if x != nil {
		return x.SInt32
	}
	return 0
}

func (x *ImplicitScalars) GetSInt64() int64 {
	if x != nil {
		return x.SInt64
	}
	return 0
}

func (x *ImplicitScalars) GetSUint32() uint32 {
	if x != nil {
		return x.SUint32
	}
	return 0
}

func (x *ImplicitScalars) GetSUint64() uint64 {
	if x != nil {
		return x.SUint64
	}
	return 0
}

func (x *ImplicitScalars) GetSSint32() int32 {
	if x != nil {
		return x.SSint32
	}
	return 0
}

func (x *ImplicitScalars) GetSSint64() int64 {
	if x != nil {
		return x.SSint64
	}
	return 0
}

func (x *ImplicitScalars) GetSFixed32() uint32 {
	if x != nil {
		return x.SFixed32
	}
	return 0
}

func (x *ImplicitScalars) GetSFixed64() uint64 {
	if x != nil {
		return x.SFixed64
	}
	return 0
}

func (x *ImplicitScalars) GetSSfixed32() int32 {
	if x != nil {
		return x.SSfixed32
	}
	return 0
}

func (x *ImplicitScalars) GetSSfixed64() int64 {
	if x != nil {
		return x.SSfixed64
	}
	return 0
}

func (x *ImplicitScalars) GetSFloat() float32 {
	if x != nil {
		return x.SFloat
	}
	return 0
}

func (x *ImplicitScalars) GetSDouble() float64 {
	if x != nil {
		return x.SDouble
	}
	return 0
}

func (x *ImplicitScalars) GetSBytes() []byte {
	if x != nil {
		return x.SBytes
	}
	return nil
}

func (x *ImplicitScalars) GetSString() string {
	if x != nil {
		return x.SString
	}
	return ""
}

type UTF8Validated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValidatedString string `protobuf:"bytes,1,opt,name=validated_string,json=validatedString" json:"validated_string,omitempty"`
}

func (x *UTF8Validated) Reset() {
	*x = UTF8Validated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UTF8Validated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UTF8Validated) ProtoMessage() {}

func (x *UTF8Validated) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UTF8Validated.ProtoReflect.Descriptor instead.
func (*UTF8Validated) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{2}
}

func (x *UTF8Validated) GetValidatedString() string {
	if x != nil {
		return x.ValidatedString
	}
	return ""
}

type NestsUTF8Validated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValidatedMessage *UTF8Validated `protobuf:"bytes,1,opt,name=validated_message,json=validatedMessage" json:"validated_message,omitempty"`
}

func (x *NestsUTF8Validated) Reset() {
	*x = NestsUTF8Validated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NestsUTF8Validated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NestsUTF8Validated) ProtoMessage() {}

func (x *NestsUTF8Validated) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NestsUTF8Validated.ProtoReflect.Descriptor instead.
func (*NestsUTF8Validated) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{3}
}

func (x *NestsUTF8Validated) GetValidatedMessage() *UTF8Validated {
	if x != nil {
		return x.ValidatedMessage
	}
	return nil
}

// Message contains enum fields.
type Enums struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptEnum            *Enum                `protobuf:"varint,1,opt,name=opt_enum,json=optEnum,enum=pbeditions.Enum" json:"opt_enum,omitempty"`
	RptEnum            []Enum               `protobuf:"varint,2,rep,packed,name=rpt_enum,json=rptEnum,enum=pbeditions.Enum" json:"rpt_enum,omitempty"`
	ImplicitEnum       OpenEnum             `protobuf:"varint,5,opt,name=implicit_enum,json=implicitEnum,enum=pbeditions.OpenEnum" json:"implicit_enum,omitempty"`
	OptNestedEnum      *Enums_NestedEnum    `protobuf:"varint,3,opt,name=opt_nested_enum,json=optNestedEnum,enum=pbeditions.Enums_NestedEnum" json:"opt_nested_enum,omitempty"`
	RptNestedEnum      []Enums_NestedEnum   `protobuf:"varint,4,rep,packed,name=rpt_nested_enum,json=rptNestedEnum,enum=pbeditions.Enums_NestedEnum" json:"rpt_nested_enum,omitempty"`
	ImplicitNestedEnum Enums_NestedOpenEnum `protobuf:"varint,6,opt,name=implicit_nested_enum,json=implicitNestedEnum,enum=pbeditions.Enums_NestedOpenEnum" json:"implicit_nested_enum,omitempty"`
}

func (x *Enums) Reset() {
	*x = Enums{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enums) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enums) ProtoMessage() {}

func (x *Enums) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enums.ProtoReflect.Descriptor instead.
func (*Enums) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{4}
}

func (x *Enums) GetOptEnum() Enum {
	if x != nil && x.OptEnum != nil {
		return *x.OptEnum
	}
	return Enum_ONE
}

func (x *Enums) GetRptEnum() []Enum {
	if x != nil {
		return x.RptEnum
	}
	return nil
}

func (x *Enums) GetImplicitEnum() OpenEnum {
	if x != nil {
		return x.ImplicitEnum
	}
	return OpenEnum_UNKNOWN
}

func (x *Enums) GetOptNestedEnum() Enums_NestedEnum {
	if x != nil && x.OptNestedEnum != nil {
		return *x.OptNestedEnum
	}
	return Enums_UNO
}

func (x *Enums) GetRptNestedEnum() []Enums_NestedEnum {
	if x != nil {
		return x.RptNestedEnum
	}
	return nil
}

func (x *Enums) GetImplicitNestedEnum() Enums_NestedOpenEnum {
	if x != nil {
		return x.ImplicitNestedEnum
	}
	return Enums_UNKNOWN
}

// Message contains repeated fields.
type Repeats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RptBool   []bool    `protobuf:"varint,1,rep,packed,name=rpt_bool,json=rptBool" json:"rpt_bool,omitempty"`
	RptInt32  []int32   `protobuf:"varint,2,rep,packed,name=rpt_int32,json=rptInt32" json:"rpt_int32,omitempty"`
	RptInt64  []int64   `protobuf:"varint,3,rep,packed,name=rpt_int64,json=rptInt64" json:"rpt_int64,omitempty"`
	RptUint32 []uint32  `protobuf:"varint,4,rep,packed,name=rpt_uint32,json=rptUint32" json:"rpt_uint32,omitempty"`
	RptUint64 []uint64  `protobuf:"varint,5,rep,packed,name=rpt_uint64,json=rptUint64" json:"rpt_uint64,omitempty"`
	RptFloat  []float32 `protobuf:"fixed32,6,rep,packed,name=rpt_float,json=rptFloat" json:"rpt_float,omitempty"`
	RptDouble []float64 `protobuf:"fixed64,7,rep,packed,name=rpt_double,json=rptDouble" json:"rpt_double,omitempty"`
	RptString []string  `protobuf:"bytes,8,rep,name=rpt_string,json=rptString" json:"rpt_string,omitempty"`
	RptBytes  [][]byte  `protobuf:"bytes,9,rep,name=rpt_bytes,json=rptBytes" json:"rpt_bytes,omitempty"`
}

func (x *Repeats) Reset() {
	*x = Repeats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Repeats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Repeats) ProtoMessage() {}

func (x *Repeats) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Repeats.ProtoReflect.Descriptor instead.
func (*Repeats) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{5}
}

func (x *Repeats) GetRptBool() []bool {
	if x != nil {
		return x.RptBool
	}
	return nil
}

func (x *Repeats) GetRptInt32() []int32 {
	if x != nil {
		return x.RptInt32
	}
	return nil
}

func (x *Repeats) GetRptInt64() []int64 {
	if x != nil {
		return x.RptInt64
	}
	return nil
}

func (x *Repeats) GetRptUint32() []uint32 {
	if x != nil {
		return x.RptUint32
	}
	return nil
}

func (x *Repeats) GetRptUint64() []uint64 {
	if x != nil {
		return x.RptUint64
	}
	return nil
}

func (x *Repeats) GetRptFloat() []float32 {
	if x != nil {
		return x.RptFloat
	}
	return nil
}

func (x *Repeats) GetRptDouble() []float64 {
	if x != nil {
		return x.RptDouble
	}
	return nil
}

func (x *Repeats) GetRptString() []string {
	if x != nil {
		return x.RptString
	}
	return nil
}

func (x *Repeats) GetRptBytes() [][]byte {
	if x != nil {
		return x.RptBytes
	}
	return nil
}

// Message contains map fields.
type Maps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Int32ToStr  map[int32]string   `protobuf:"bytes,1,rep,name=int32_to_str,json=int32ToStr" json:"int32_to_str,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	StrToNested map[string]*Nested `protobuf:"bytes,4,rep,name=str_to_nested,json=strToNested" json:"str_to_nested,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

func (x *Maps) Reset() {
	*x = Maps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Maps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Maps) ProtoMessage() {}

func (x *Maps) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Maps.ProtoReflect.Descriptor instead.
func (*Maps) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{6}
}

func (x *Maps) GetInt32ToStr() map[int32]string {
	if x != nil {
		return x.Int32ToStr
	}
	return nil
}

func (x *Maps) GetStrToNested() map[string]*Nested {
	if x != nil {
		return x.StrToNested
	}
	return nil
}

// Message type used as submessage.
type Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptNested *Nested `protobuf:"bytes,2,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
}

func (x *Nested) Reset() {
	*x = Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nested) ProtoMessage() {}

func (x *Nested) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nested.ProtoReflect.Descriptor instead.
func (*Nested) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{7}
}

func (x *Nested) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

func (x *Nested) GetOptNested() *Nested {
	if x != nil {
		return x.OptNested
	}
	return nil
}

// Message contains message and group fields.
type Nests struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptNested *Nested           `protobuf:"bytes,1,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
	Optgroup  *Nests_OptGroup   `protobuf:"group,2,opt,name=OptGroup,json=optgroup" json:"optgroup,omitempty"`
	RptNested []*Nested         `protobuf:"bytes,4,rep,name=rpt_nested,json=rptNested" json:"rpt_nested,omitempty"`
	Rptgroup  []*Nests_RptGroup `protobuf:"group,5,rep,name=RptGroup,json=rptgroup" json:"rptgroup,omitempty"`
}

func (x *Nests) Reset() {
	*x = Nests{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests) ProtoMessage() {}

func (x *Nests) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests.ProtoReflect.Descriptor instead.
func (*Nests) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{8}
}

func (x *Nests) GetOptNested() *Nested {
	if x != nil {
		return x.OptNested
	}
	return nil
}

func (x *Nests) GetOptgroup() *Nests_OptGroup {
	if x != nil {
		return x.Optgroup
	}
	return nil
}

func (x *Nests) GetRptNested() []*Nested {
	if x != nil {
		return x.RptNested
	}
	return nil
}

func (x *Nests) GetRptgroup() []*Nests_RptGroup {
	if x != nil {
		return x.Rptgroup
	}
	return nil
}

// Message contains required fields.
type Requireds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqBool     *bool    `protobuf:"varint,1,req,name=req_bool,json=reqBool" json:"req_bool,omitempty"`
	ReqSfixed64 *int64   `protobuf:"fixed64,2,req,name=req_sfixed64,json=reqSfixed64" json:"req_sfixed64,omitempty"`
	ReqDouble   *float64 `protobuf:"fixed64,3,req,name=req_double,json=reqDouble" json:"req_double,omitempty"`
	ReqString   *string  `protobuf:"bytes,4,req,name=req_string,json=reqString" json:"req_string,omitempty"`
	ReqEnum     *Enum    `protobuf:"varint,5,req,name=req_enum,json=reqEnum,enum=pbeditions.Enum" json:"req_enum,omitempty"`
	ReqNested   *Nested  `protobuf:"bytes,6,req,name=req_nested,json=reqNested" json:"req_nested,omitempty"`
}

func (x *Requireds) Reset() {
	*x = Requireds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Requireds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Requireds) ProtoMessage() {}

func (x *Requireds) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Requireds.ProtoReflect.Descriptor instead.
func (*Requireds) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{9}
}

func (x *Requireds) GetReqBool() bool {
	if x != nil && x.ReqBool != nil {
		return *x.ReqBool
	}
	return false
}

func (x *Requireds) GetReqSfixed64() int64 {
	if x != nil && x.ReqSfixed64 != nil {
		return *x.ReqSfixed64
	}
	return 0
}

func (x *Requireds) GetReqDouble() float64 {
	if x != nil && x.ReqDouble != nil {
		return *x.ReqDouble
	}
	return 0
}

func (x *Requireds) GetReqString() string {
	if x != nil && x.ReqString != nil {
		return *x.ReqString
	}
	return ""
}

func (x *Requireds) GetReqEnum() Enum {
	if x != nil && x.ReqEnum != nil {
		return *x.ReqEnum
	}
	return Enum_ONE
}

func (x *Requireds) GetReqNested() *Nested {
	if x != nil {
		return x.ReqNested
	}
	return nil
}

// Message contains both required and optional fields.
type PartialRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqString *string `protobuf:"bytes,1,req,name=req_string,json=reqString" json:"req_string,omitempty"`
	OptString *string `protobuf:"bytes,2,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *PartialRequired) Reset() {
	*x = PartialRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartialRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartialRequired) ProtoMessage() {}

func (x *PartialRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartialRequired.ProtoReflect.Descriptor instead.
func (*PartialRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{10}
}

func (x *PartialRequired) GetReqString() string {
	if x != nil && x.ReqString != nil {
		return *x.ReqString
	}
	return ""
}

func (x *PartialRequired) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

type NestedWithRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqString *string `protobuf:"bytes,1,req,name=req_string,json=reqString" json:"req_string,omitempty"`
}

func (x *NestedWithRequired) Reset() {
	*x = NestedWithRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NestedWithRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NestedWithRequired) ProtoMessage() {}

func (x *NestedWithRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NestedWithRequired.ProtoReflect.Descriptor instead.
func (*NestedWithRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{11}
}

func (x *NestedWithRequired) GetReqString() string {
	if x != nil && x.ReqString != nil {
		return *x.ReqString
	}
	return ""
}

type IndirectRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptNested   *NestedWithRequired            `protobuf:"bytes,1,req,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
	RptNested   []*NestedWithRequired          `protobuf:"bytes,2,rep,name=rpt_nested,json=rptNested" json:"rpt_nested,omitempty"`
	StrToNested map[string]*NestedWithRequired `protobuf:"bytes,3,rep,name=str_to_nested,json=strToNested" json:"str_to_nested,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Types that are assignable to Union:
	//
	//	*IndirectRequired_OneofNested
	Union isIndirectRequired_Union `protobuf_oneof:"union"`
}

func (x *IndirectRequired) Reset() {
	*x = IndirectRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndirectRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndirectRequired) ProtoMessage() {}

func (x *IndirectRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndirectRequired.ProtoReflect.Descriptor instead.
func (*IndirectRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{12}
}

func (x *IndirectRequired) GetOptNested() *NestedWithRequired {
	if x != nil {
		return x.OptNested
	}
	return nil
}

func (x *IndirectRequired) GetRptNested() []*NestedWithRequired {
	if x != nil {
		return x.RptNested
	}
	return nil
}

func (x *IndirectRequired) GetStrToNested() map[string]*NestedWithRequired {
	if x != nil {
		return x.StrToNested
	}
	return nil
}

func (m *IndirectRequired) GetUnion() isIndirectRequired_Union {
	if m != nil {
		return m.Union
	}
	return nil
}

func (x *IndirectRequired) GetOneofNested() *NestedWithRequired {
	if x, ok := x.GetUnion().(*IndirectRequired_OneofNested); ok {
		return x.OneofNested
	}
	return nil
}

type isIndirectRequired_Union interface {
	isIndirectRequired_Union()
}

type IndirectRequired_OneofNested struct {
	OneofNested *NestedWithRequired `protobuf:"bytes,4,opt,name=oneof_nested,json=oneofNested,oneof"`
}

func (*IndirectRequired_OneofNested) isIndirectRequired_Union() {}

type Extensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptBool   *bool   `protobuf:"varint,101,opt,name=opt_bool,json=optBool" json:"opt_bool,omitempty"`
	OptInt32  *int32  `protobuf:"varint,2,opt,name=opt_int32,json=optInt32" json:"opt_int32,omitempty"`
}

func (x *Extensions) Reset() {
	*x = Extensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Extensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Extensions) ProtoMessage() {}

func (x *Extensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Extensions.ProtoReflect.Descriptor instead.
func (*Extensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{13}
}

func (x *Extensions) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

func (x *Extensions) GetOptBool() bool {
	if x != nil && x.OptBool != nil {
		return *x.OptBool
	}
	return false
}

func (x *Extensions) GetOptInt32() int32 {
	if x != nil && x.OptInt32 != nil {
		return *x.OptInt32
	}
	return 0
}

type ExtensionsContainer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExtensionsContainer) Reset() {
	*x = ExtensionsContainer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtensionsContainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionsContainer) ProtoMessage() {}

func (x *ExtensionsContainer) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionsContainer.ProtoReflect.Descriptor instead.
func (*ExtensionsContainer) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{14}
}

type MessageSet struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *MessageSet) Reset() {
	*x = MessageSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageSet) ProtoMessage() {}

func (x *MessageSet) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageSet.ProtoReflect.Descriptor instead.
func (*MessageSet) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{15}
}

type MessageSetExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *MessageSetExtension) Reset() {
	*x = MessageSetExtension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageSetExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageSetExtension) ProtoMessage() {}

func (x *MessageSetExtension) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageSetExtension.ProtoReflect.Descriptor instead.
func (*MessageSetExtension) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{16}
}

func (x *MessageSetExtension) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

type FakeMessageSet struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *FakeMessageSet) Reset() {
	*x = FakeMessageSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FakeMessageSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FakeMessageSet) ProtoMessage() {}

func (x *FakeMessageSet) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FakeMessageSet.ProtoReflect.Descriptor instead.
func (*FakeMessageSet) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{17}
}

type FakeMessageSetExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *FakeMessageSetExtension) Reset() {
	*x = FakeMessageSetExtension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FakeMessageSetExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FakeMessageSetExtension) ProtoMessage() {}

func (x *FakeMessageSetExtension) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FakeMessageSetExtension.ProtoReflect.Descriptor instead.
func (*FakeMessageSetExtension) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{18}
}

func (x *FakeMessageSetExtension) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

// Message contains well-known type fields.
type KnownTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptBool      *wrapperspb.BoolValue   `protobuf:"bytes,1,opt,name=opt_bool,json=optBool" json:"opt_bool,omitempty"`
	OptInt32     *wrapperspb.Int32Value  `protobuf:"bytes,2,opt,name=opt_int32,json=optInt32" json:"opt_int32,omitempty"`
	OptInt64     *wrapperspb.Int64Value  `protobuf:"bytes,3,opt,name=opt_int64,json=optInt64" json:"opt_int64,omitempty"`
	OptUint32    *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=opt_uint32,json=optUint32" json:"opt_uint32,omitempty"`
	OptUint64    *wrapperspb.UInt64Value `protobuf:"bytes,5,opt,name=opt_uint64,json=optUint64" json:"opt_uint64,omitempty"`
	OptFloat     *wrapperspb.FloatValue  `protobuf:"bytes,6,opt,name=opt_float,json=optFloat" json:"opt_float,omitempty"`
	OptDouble    *wrapperspb.DoubleValue `protobuf:"bytes,7,opt,name=opt_double,json=optDouble" json:"opt_double,omitempty"`
	OptString    *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptBytes     *wrapperspb.BytesValue  `protobuf:"bytes,9,opt,name=opt_bytes,json=optBytes" json:"opt_bytes,omitempty"`
	OptDuration  *durationpb.Duration    `protobuf:"bytes,20,opt,name=opt_duration,json=optDuration" json:"opt_duration,omitempty"`
	OptTimestamp *timestamppb.Timestamp  `protobuf:"bytes,21,opt,name=opt_timestamp,json=optTimestamp" json:"opt_timestamp,omitempty"`
	OptStruct    *structpb.Struct        `protobuf:"bytes,25,opt,name=opt_struct,json=optStruct" json:"opt_struct,omitempty"`
	OptList      *structpb.ListValue     `protobuf:"bytes,26,opt,name=opt_list,json=optList" json:"opt_list,omitempty"`
	OptValue     *structpb.Value         `protobuf:"bytes,27,opt,name=opt_value,json=optValue" json:"opt_value,omitempty"`
	OptNull      *structpb.NullValue     `protobuf:"varint,28,opt,name=opt_null,json=optNull,enum=google.protobuf.NullValue" json:"opt_null,omitempty"`
	OptEmpty     *emptypb.Empty          `protobuf:"bytes,30,opt,name=opt_empty,json=optEmpty" json:"opt_empty,omitempty"`
	OptAny       *anypb.Any              `protobuf:"bytes,32,opt,name=opt_any,json=optAny" json:"opt_any,omitempty"`
	OptFieldmask *fieldmaskpb.FieldMask  `protobuf:"bytes,40,opt,name=opt_fieldmask,json=optFieldmask" json:"opt_fieldmask,omitempty"`
}

func (x *KnownTypes) Reset() {
	*x = KnownTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnownTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnownTypes) ProtoMessage() {}

func (x *KnownTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnownTypes.ProtoReflect.Descriptor instead.
func (*KnownTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{19}
}

func (x *KnownTypes) GetOptBool() *wrapperspb.BoolValue {
	if x != nil {
		return x.OptBool
	}
	return nil
}

func (x *KnownTypes) GetOptInt32() *wrapperspb.Int32Value {
	if x != nil {
		return x.OptInt32
	}
	return nil
}

func (x *KnownTypes) GetOptInt64() *wrapperspb.Int64Value {
	if x != nil {
		return x.OptInt64
	}
	return nil
}

func (x *KnownTypes) GetOptUint32() *wrapperspb.UInt32Value {
	if x != nil {
		return x.OptUint32
	}
	return nil
}

func (x *KnownTypes) GetOptUint64() *wrapperspb.UInt64Value {
	if x != nil {
		return x.OptUint64
	}
	return nil
}

func (x *KnownTypes) GetOptFloat() *wrapperspb.FloatValue {
	if x != nil {
		return x.OptFloat
	}
	return nil
}

func (x *KnownTypes) GetOptDouble() *wrapperspb.DoubleValue {
	if x != nil {
		return x.OptDouble
	}
	return nil
}

func (x *KnownTypes) GetOptString() *wrapperspb.StringValue {
	if x != nil {
		return x.OptString
	}
	return nil
}

func (x *KnownTypes) GetOptBytes() *wrapperspb.BytesValue {
	if x != nil {
		return x.OptBytes
	}
	return nil
}

func (x *KnownTypes) GetOptDuration() *durationpb.Duration {
	if x != nil {
		return x.OptDuration
	}
	return nil
}

func (x *KnownTypes) GetOptTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OptTimestamp
	}
	return nil
}

func (x *KnownTypes) GetOptStruct() *structpb.Struct {
	if x != nil {
		return x.OptStruct
	}
	return nil
}

func (x *KnownTypes) GetOptList() *structpb.ListValue {
	if x != nil {
		return x.OptList
	}
	return nil
}

func (x *KnownTypes) GetOptValue() *structpb.Value {
	if x != nil {
		return x.OptValue
	}
	return nil
}

func (x *KnownTypes) GetOptNull() structpb.NullValue {
	if x != nil && x.OptNull != nil {
		return *x.OptNull
	}
	return structpb.NullValue(0)
}

func (x *KnownTypes) GetOptEmpty() *emptypb.Empty {
	if x != nil {
		return x.OptEmpty
	}
	return nil
}

func (x *KnownTypes) GetOptAny() *anypb.Any {
	if x != nil {
		return x.OptAny
	}
	return nil
}

func (x *KnownTypes) GetOptFieldmask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.OptFieldmask
	}
	return nil
}

type Nests_OptGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString      *string                        `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptNested      *Nested                        `protobuf:"bytes,2,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
	Optnestedgroup *Nests_OptGroup_OptNestedGroup `protobuf:"group,3,opt,name=OptNestedGroup,json=optnestedgroup" json:"optnestedgroup,omitempty"`
}

func (x *Nests_OptGroup) Reset() {
	*x = Nests_OptGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests_OptGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests_OptGroup) ProtoMessage() {}

func (x *Nests_OptGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests_OptGroup.ProtoReflect.Descriptor instead.
func (*Nests_OptGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{8, 0}
}

func (x *Nests_OptGroup) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

func (x *Nests_OptGroup) GetOptNested() *Nested {
	if x != nil {
		return x.OptNested
	}
	return nil
}

func (x *Nests_OptGroup) GetOptnestedgroup() *Nests_OptGroup_OptNestedGroup {
	if x != nil {
		return x.Optnestedgroup
	}
	return nil
}

type Nests_RptGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RptString []string `protobuf:"bytes,1,rep,name=rpt_string,json=rptString" json:"rpt_string,omitempty"`
}

func (x *Nests_RptGroup) Reset() {
	*x = Nests_RptGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests_RptGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests_RptGroup) ProtoMessage() {}

func (x *Nests_RptGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests_RptGroup.ProtoReflect.Descriptor instead.
func (*Nests_RptGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{8, 1}
}

func (x *Nests_RptGroup) GetRptString() []string {
	if x != nil {
		return x.RptString
	}
	return nil
}

type Nests_OptGroup_OptNestedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptFixed32 *uint32 `protobuf:"fixed32,1,opt,name=opt_fixed32,json=optFixed32" json:"opt_fixed32,omitempty"`
}

func (x *Nests_OptGroup_OptNestedGroup) Reset() {
	*x = Nests_OptGroup_OptNestedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests_OptGroup_OptNestedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests_OptGroup_OptNestedGroup) ProtoMessage() {}

func (x *Nests_OptGroup_OptNestedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpbeditions_test2_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests_OptGroup_OptNestedGroup.ProtoReflect.Descriptor instead.
func (*Nests_OptGroup_OptNestedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP(), []int{8, 0, 0}
}

func (x *Nests_OptGroup_OptNestedGroup) GetOptFixed32() uint32 {
	if x != nil && x.OptFixed32 != nil {
		return *x.OptFixed32
	}
	return 0
}

var file_internal_testprotos_textpbeditions_test2_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         21,
		Name:          "pbeditions.opt_ext_bool",
		Tag:           "varint,21,opt,name=opt_ext_bool",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         22,
		Name:          "pbeditions.opt_ext_string",
		Tag:           "bytes,22,opt,name=opt_ext_string",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Enum)(nil),
		Field:         23,
		Name:          "pbeditions.opt_ext_enum",
		Tag:           "varint,23,opt,name=opt_ext_enum,enum=pbeditions.Enum",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Nested)(nil),
		Field:         24,
		Name:          "pbeditions.opt_ext_nested",
		Tag:           "bytes,24,opt,name=opt_ext_nested",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*PartialRequired)(nil),
		Field:         25,
		Name:          "pbeditions.opt_ext_partial",
		Tag:           "bytes,25,opt,name=opt_ext_partial",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         31,
		Name:          "pbeditions.rpt_ext_fixed32",
		Tag:           "fixed32,31,rep,name=rpt_ext_fixed32",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]Enum)(nil),
		Field:         32,
		Name:          "pbeditions.rpt_ext_enum",
		Tag:           "varint,32,rep,name=rpt_ext_enum,enum=pbeditions.Enum",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]*Nested)(nil),
		Field:         33,
		Name:          "pbeditions.rpt_ext_nested",
		Tag:           "bytes,33,rep,name=rpt_ext_nested",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*FakeMessageSetExtension)(nil),
		Field:         50,
		Name:          "pbeditions.message_set_extension",
		Tag:           "bytes,50,opt,name=message_set_extension",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         51,
		Name:          "pbeditions.ExtensionsContainer.opt_ext_bool",
		Tag:           "varint,51,opt,name=opt_ext_bool",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         52,
		Name:          "pbeditions.ExtensionsContainer.opt_ext_string",
		Tag:           "bytes,52,opt,name=opt_ext_string",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Enum)(nil),
		Field:         53,
		Name:          "pbeditions.ExtensionsContainer.opt_ext_enum",
		Tag:           "varint,53,opt,name=opt_ext_enum,enum=pbeditions.Enum",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Nested)(nil),
		Field:         54,
		Name:          "pbeditions.ExtensionsContainer.opt_ext_nested",
		Tag:           "bytes,54,opt,name=opt_ext_nested",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*PartialRequired)(nil),
		Field:         55,
		Name:          "pbeditions.ExtensionsContainer.opt_ext_partial",
		Tag:           "bytes,55,opt,name=opt_ext_partial",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         61,
		Name:          "pbeditions.ExtensionsContainer.rpt_ext_string",
		Tag:           "bytes,61,rep,name=rpt_ext_string",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]Enum)(nil),
		Field:         62,
		Name:          "pbeditions.ExtensionsContainer.rpt_ext_enum",
		Tag:           "varint,62,rep,name=rpt_ext_enum,enum=pbeditions.Enum",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]*Nested)(nil),
		Field:         63,
		Name:          "pbeditions.ExtensionsContainer.rpt_ext_nested",
		Tag:           "bytes,63,rep,name=rpt_ext_nested",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*MessageSetExtension)(nil),
		Field:         10,
		Name:          "pbeditions.MessageSetExtension.message_set_extension",
		Tag:           "bytes,10,opt,name=message_set_extension",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*MessageSetExtension)(nil),
		Field:         20,
		Name:          "pbeditions.MessageSetExtension.not_message_set_extension",
		Tag:           "bytes,20,opt,name=not_message_set_extension",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*Nested)(nil),
		Field:         30,
		Name:          "pbeditions.MessageSetExtension.ext_nested",
		Tag:           "bytes,30,opt,name=ext_nested",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
	{
		ExtendedType:  (*FakeMessageSet)(nil),
		ExtensionType: (*FakeMessageSetExtension)(nil),
		Field:         10,
		Name:          "pbeditions.FakeMessageSetExtension.message_set_extension",
		Tag:           "bytes,10,opt,name=message_set_extension",
		Filename:      "internal/testprotos/textpbeditions/test2.proto",
	},
}

// Extension fields to Extensions.
var (
	// optional bool opt_ext_bool = 21;
	E_OptExtBool = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[0]
	// optional string opt_ext_string = 22;
	E_OptExtString = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[1]
	// optional pbeditions.Enum opt_ext_enum = 23;
	E_OptExtEnum = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[2]
	// optional pbeditions.Nested opt_ext_nested = 24;
	E_OptExtNested = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[3]
	// optional pbeditions.PartialRequired opt_ext_partial = 25;
	E_OptExtPartial = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[4]
	// repeated fixed32 rpt_ext_fixed32 = 31;
	E_RptExtFixed32 = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[5]
	// repeated pbeditions.Enum rpt_ext_enum = 32;
	E_RptExtEnum = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[6]
	// repeated pbeditions.Nested rpt_ext_nested = 33;
	E_RptExtNested = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[7]
	// optional bool opt_ext_bool = 51;
	E_ExtensionsContainer_OptExtBool = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[9]
	// optional string opt_ext_string = 52;
	E_ExtensionsContainer_OptExtString = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[10]
	// optional pbeditions.Enum opt_ext_enum = 53;
	E_ExtensionsContainer_OptExtEnum = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[11]
	// optional pbeditions.Nested opt_ext_nested = 54;
	E_ExtensionsContainer_OptExtNested = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[12]
	// optional pbeditions.PartialRequired opt_ext_partial = 55;
	E_ExtensionsContainer_OptExtPartial = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[13]
	// repeated string rpt_ext_string = 61;
	E_ExtensionsContainer_RptExtString = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[14]
	// repeated pbeditions.Enum rpt_ext_enum = 62;
	E_ExtensionsContainer_RptExtEnum = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[15]
	// repeated pbeditions.Nested rpt_ext_nested = 63;
	E_ExtensionsContainer_RptExtNested = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[16]
)

// Extension fields to MessageSet.
var (
	// optional pbeditions.FakeMessageSetExtension message_set_extension = 50;
	E_MessageSetExtension = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[8]
	// optional pbeditions.MessageSetExtension message_set_extension = 10;
	E_MessageSetExtension_MessageSetExtension = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[17]
	// optional pbeditions.MessageSetExtension not_message_set_extension = 20;
	E_MessageSetExtension_NotMessageSetExtension = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[18]
	// optional pbeditions.Nested ext_nested = 30;
	E_MessageSetExtension_ExtNested = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[19]
)

// Extension fields to FakeMessageSet.
var (
	// optional pbeditions.FakeMessageSetExtension message_set_extension = 10;
	E_FakeMessageSetExtension_MessageSetExtension = &file_internal_testprotos_textpbeditions_test2_proto_extTypes[20]
)

var File_internal_testprotos_textpbeditions_test2_proto protoreflect.FileDescriptor

var file_internal_testprotos_textpbeditions_test2_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x19, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x03, 0x0a, 0x07, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x6f, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x74,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x70,
	0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x11, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x07, 0x20, 0x01, 0x28, 0x12, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x18, 0x09, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x0b, 0x6f, 0x70, 0x74, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x10, 0x52, 0x0b, 0x6f,
	0x70, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70,
	0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6f,
	0x70, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6f, 0x70, 0x74,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x22, 0x8f, 0x04, 0x0a, 0x0f, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x53,
	0x63, 0x61, 0x6c, 0x61, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x06, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x05, 0x73,
	0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x1e, 0x0a, 0x07, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x06, 0x73, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x1e, 0x0a, 0x07, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x06, 0x73, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x07, 0x73,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52,
	0x07, 0x73, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x11, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08,
	0x02, 0x52, 0x07, 0x73, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x5f,
	0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x07, 0x20, 0x01, 0x28, 0x12, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x08, 0x02, 0x52, 0x07, 0x73, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x22, 0x0a, 0x09,
	0x73, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x07, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x08, 0x73, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x12, 0x22, 0x0a, 0x09, 0x73, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x08, 0x73, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x12, 0x24, 0x0a, 0x0a, 0x73, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52,
	0x09, 0x73, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x24, 0x0a, 0x0a, 0x73, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x10, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x09, 0x73, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x12, 0x1e, 0x0a, 0x07, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x06, 0x73, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x20, 0x0a, 0x08, 0x73, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x07, 0x73, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x07, 0x73, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0c, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x06, 0x73, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x07, 0x73, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x22, 0x43, 0x0a, 0x0d, 0x55, 0x54, 0x46, 0x38, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xaa, 0x01, 0x04, 0x08, 0x02, 0x20, 0x02, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x5c, 0x0a, 0x12, 0x4e, 0x65, 0x73,
	0x74, 0x73, 0x55, 0x54, 0x46, 0x38, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x46, 0x0a, 0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x62, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x55, 0x54, 0x46, 0x38, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x52, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xf7, 0x03, 0x0a, 0x05, 0x45, 0x6e, 0x75, 0x6d,
	0x73, 0x12, 0x2b, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x2b,
	0x0a, 0x08, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x10, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x07, 0x72, 0x70, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x40, 0x0a, 0x0d, 0x69,
	0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x4f, 0x70, 0x65, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52,
	0x0c, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x44, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x44, 0x0a, 0x0f, 0x72, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70,
	0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x72, 0x70, 0x74, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x59, 0x0a, 0x14, 0x69, 0x6d, 0x70,
	0x6c, 0x69, 0x63, 0x69, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02,
	0x52, 0x12, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x22, 0x28, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x4e, 0x4f, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x44,
	0x4f, 0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x49, 0x45, 0x5a, 0x10, 0x0a, 0x22, 0x41,
	0x0a, 0x0e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x45, 0x49, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x57, 0x45, 0x49, 0x10,
	0x02, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x45, 0x48, 0x4e, 0x10, 0x0a, 0x1a, 0x04, 0x3a, 0x02, 0x10,
	0x01, 0x22, 0x94, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x72, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x08, 0x52,
	0x07, 0x72, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x70, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x72, 0x70, 0x74,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x72, 0x70, 0x74, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x70, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x09, 0x72, 0x70, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x70, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x08, 0x72, 0x70, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x70, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x01, 0x52, 0x09, 0x72, 0x70, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x70, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x08,
	0x72, 0x70, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x04, 0x4d, 0x61, 0x70,
	0x73, 0x12, 0x42, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74,
	0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x54,
	0x6f, 0x53, 0x74, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x54, 0x6f, 0x53, 0x74, 0x72, 0x12, 0x45, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x74, 0x6f, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70,
	0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x73, 0x2e, 0x53,
	0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0b, 0x73, 0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x1a, 0x3d, 0x0a, 0x0f,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x54, 0x6f, 0x53, 0x74, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x52, 0x0a, 0x10, 0x53,
	0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x5a, 0x0a, 0x06, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f,
	0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x52, 0x09, 0x6f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x22, 0x94, 0x04, 0x0a, 0x05,
	0x4e, 0x65, 0x73, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x6f,
	0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x62, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x73, 0x2e, 0x4f, 0x70,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x08, 0x6f,
	0x70, 0x74, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52,
	0x09, 0x72, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x08, 0x72, 0x70,
	0x74, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70,
	0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x73, 0x2e,
	0x52, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x07, 0xaa, 0x01, 0x04, 0x18, 0x02, 0x28,
	0x02, 0x52, 0x08, 0x72, 0x70, 0x74, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0xe9, 0x01, 0x0a, 0x08,
	0x4f, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70,
	0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52,
	0x09, 0x6f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x58, 0x0a, 0x0e, 0x6f, 0x70,
	0x74, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x4e, 0x65, 0x73, 0x74, 0x73, 0x2e, 0x4f, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x4f,
	0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x28, 0x02, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x1a, 0x31, 0x0a, 0x0e, 0x4f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0a, 0x6f, 0x70, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x1a, 0x29, 0x0a, 0x08, 0x52, 0x70, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x22, 0x91, 0x02, 0x0a, 0x09, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x73,
	0x12, 0x20, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x07, 0x72, 0x65, 0x71, 0x42, 0x6f,
	0x6f, 0x6c, 0x12, 0x28, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x10, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52,
	0x0b, 0x72, 0x65, 0x71, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x24, 0x0a, 0x0a,
	0x72, 0x65, 0x71, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x62, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x07, 0x72, 0x65, 0x71, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x38, 0x0a, 0x0a,
	0x72, 0x65, 0x71, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x22, 0x56, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x08, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x3a,
	0x0a, 0x12, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x98, 0x03, 0x0a, 0x10, 0x49,
	0x6e, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x44, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x3d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x72, 0x70, 0x74, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x12, 0x51, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x74, 0x6f, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x62,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x54, 0x6f, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x54,
	0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52,
	0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x1a, 0x5e, 0x0a, 0x10,
	0x53, 0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x22, 0x69, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x65,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x6f, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x2a, 0x04, 0x08, 0x14, 0x10, 0x65,
	0x22, 0xe4, 0x04, 0x0a, 0x13, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x32, 0x38, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f,
	0x65, 0x78, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x33, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x42, 0x6f,
	0x6f, 0x6c, 0x32, 0x3c, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x34, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x32, 0x4a, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10,
	0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x0a, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x32, 0x50, 0x0a, 0x0e,
	0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x16,
	0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x36, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x52, 0x0c, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x32, 0x5b,
	0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61,
	0x6c, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x37, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x6f, 0x70,
	0x74, 0x45, 0x78, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x32, 0x3c, 0x0a, 0x0e, 0x72,
	0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x2e,
	0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x70, 0x74,
	0x45, 0x78, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0x4a, 0x0a, 0x0c, 0x72, 0x70, 0x74,
	0x5f, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x3e, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x72, 0x70, 0x74, 0x45, 0x78,
	0x74, 0x45, 0x6e, 0x75, 0x6d, 0x32, 0x50, 0x0a, 0x0e, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x3f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x0c, 0x72, 0x70, 0x74, 0x45, 0x78,
	0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x22, 0x1a, 0x0a, 0x0a, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x2a, 0x08, 0x08, 0x04, 0x10, 0xff, 0xff, 0xff, 0xff, 0x07, 0x3a,
	0x02, 0x08, 0x01, 0x22, 0xe0, 0x02, 0x0a, 0x13, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6f,
	0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0x6b, 0x0a, 0x15, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x72, 0x0a, 0x19, 0x6e, 0x6f, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x16, 0x6e, 0x6f, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x49, 0x0a, 0x0a, 0x65,
	0x78, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65,
	0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x65, 0x78, 0x74,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x22, 0x1a, 0x0a, 0x0e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x2a, 0x08, 0x08, 0x04, 0x10, 0x80, 0x80, 0x80,
	0x80, 0x02, 0x22, 0xad, 0x01, 0x0a, 0x17, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0x73, 0x0a,
	0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x9e, 0x08, 0x0a, 0x0a, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x35, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x07, 0x6f, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x3b, 0x0a, 0x0a,
	0x6f, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x6f, 0x70, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x3b, 0x0a, 0x0a, 0x6f, 0x70, 0x74,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6f, 0x70, 0x74,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x3b, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x3b, 0x0a,
	0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70,
	0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6f, 0x70, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x36, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x6f,
	0x70, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x33, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f,
	0x70, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x6e,
	0x75, 0x6c, 0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x4e, 0x75, 0x6c, 0x6c, 0x12, 0x33,
	0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x2d, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x5f, 0x61, 0x6e, 0x79, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x41,
	0x6e, 0x79, 0x12, 0x3f, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x6d,
	0x61, 0x73, 0x6b, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x6d,
	0x61, 0x73, 0x6b, 0x2a, 0x21, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x4f,
	0x4e, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x12, 0x07, 0x0a,
	0x03, 0x54, 0x45, 0x4e, 0x10, 0x0a, 0x2a, 0x3b, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x45, 0x49, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x57, 0x45,
	0x49, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x45, 0x48, 0x4e, 0x10, 0x0a, 0x1a, 0x04, 0x3a,
	0x02, 0x10, 0x01, 0x3a, 0x38, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x3c, 0x0a,
	0x0e, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x70, 0x74, 0x45, 0x78, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x4a, 0x0a, 0x0c, 0x6f,
	0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x2e, 0x70, 0x62,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x6f, 0x70, 0x74,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x50, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x5f, 0x65,
	0x78, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x0c, 0x6f, 0x70, 0x74,
	0x45, 0x78, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x3a, 0x5b, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x5f, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x16, 0x2e, 0x70,
	0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x62, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x3a, 0x3e, 0x0a, 0x0f, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78,
	0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0d, 0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x4a, 0x0a, 0x0c, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x20,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x45, 0x6e,
	0x75, 0x6d, 0x3a, 0x50, 0x0a, 0x0e, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x6e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x21, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x0c, 0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x3a, 0x6f, 0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e,
	0x70, 0x62, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x62,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x13, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x46, 0x5a, 0x3d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73,
	0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x70, 0x62, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x92, 0x03, 0x04, 0x10, 0x02, 0x20, 0x03, 0x62, 0x08, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_internal_testprotos_textpbeditions_test2_proto_rawDescOnce sync.Once
	file_internal_testprotos_textpbeditions_test2_proto_rawDescData = file_internal_testprotos_textpbeditions_test2_proto_rawDesc
)

func file_internal_testprotos_textpbeditions_test2_proto_rawDescGZIP() []byte {
	file_internal_testprotos_textpbeditions_test2_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_textpbeditions_test2_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_textpbeditions_test2_proto_rawDescData)
	})
	return file_internal_testprotos_textpbeditions_test2_proto_rawDescData
}

var file_internal_testprotos_textpbeditions_test2_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_internal_testprotos_textpbeditions_test2_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_internal_testprotos_textpbeditions_test2_proto_goTypes = []interface{}{
	(Enum)(0),                             // 0: pbeditions.Enum
	(OpenEnum)(0),                         // 1: pbeditions.OpenEnum
	(Enums_NestedEnum)(0),                 // 2: pbeditions.Enums.NestedEnum
	(Enums_NestedOpenEnum)(0),             // 3: pbeditions.Enums.NestedOpenEnum
	(*Scalars)(nil),                       // 4: pbeditions.Scalars
	(*ImplicitScalars)(nil),               // 5: pbeditions.ImplicitScalars
	(*UTF8Validated)(nil),                 // 6: pbeditions.UTF8Validated
	(*NestsUTF8Validated)(nil),            // 7: pbeditions.NestsUTF8Validated
	(*Enums)(nil),                         // 8: pbeditions.Enums
	(*Repeats)(nil),                       // 9: pbeditions.Repeats
	(*Maps)(nil),                          // 10: pbeditions.Maps
	(*Nested)(nil),                        // 11: pbeditions.Nested
	(*Nests)(nil),                         // 12: pbeditions.Nests
	(*Requireds)(nil),                     // 13: pbeditions.Requireds
	(*PartialRequired)(nil),               // 14: pbeditions.PartialRequired
	(*NestedWithRequired)(nil),            // 15: pbeditions.NestedWithRequired
	(*IndirectRequired)(nil),              // 16: pbeditions.IndirectRequired
	(*Extensions)(nil),                    // 17: pbeditions.Extensions
	(*ExtensionsContainer)(nil),           // 18: pbeditions.ExtensionsContainer
	(*MessageSet)(nil),                    // 19: pbeditions.MessageSet
	(*MessageSetExtension)(nil),           // 20: pbeditions.MessageSetExtension
	(*FakeMessageSet)(nil),                // 21: pbeditions.FakeMessageSet
	(*FakeMessageSetExtension)(nil),       // 22: pbeditions.FakeMessageSetExtension
	(*KnownTypes)(nil),                    // 23: pbeditions.KnownTypes
	nil,                                   // 24: pbeditions.Maps.Int32ToStrEntry
	nil,                                   // 25: pbeditions.Maps.StrToNestedEntry
	(*Nests_OptGroup)(nil),                // 26: pbeditions.Nests.OptGroup
	(*Nests_RptGroup)(nil),                // 27: pbeditions.Nests.RptGroup
	(*Nests_OptGroup_OptNestedGroup)(nil), // 28: pbeditions.Nests.OptGroup.OptNestedGroup
	nil,                                   // 29: pbeditions.IndirectRequired.StrToNestedEntry
	(*wrapperspb.BoolValue)(nil),          // 30: google.protobuf.BoolValue
	(*wrapperspb.Int32Value)(nil),         // 31: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),         // 32: google.protobuf.Int64Value
	(*wrapperspb.UInt32Value)(nil),        // 33: google.protobuf.UInt32Value
	(*wrapperspb.UInt64Value)(nil),        // 34: google.protobuf.UInt64Value
	(*wrapperspb.FloatValue)(nil),         // 35: google.protobuf.FloatValue
	(*wrapperspb.DoubleValue)(nil),        // 36: google.protobuf.DoubleValue
	(*wrapperspb.StringValue)(nil),        // 37: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),         // 38: google.protobuf.BytesValue
	(*durationpb.Duration)(nil),           // 39: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),         // 40: google.protobuf.Timestamp
	(*structpb.Struct)(nil),               // 41: google.protobuf.Struct
	(*structpb.ListValue)(nil),            // 42: google.protobuf.ListValue
	(*structpb.Value)(nil),                // 43: google.protobuf.Value
	(structpb.NullValue)(0),               // 44: google.protobuf.NullValue
	(*emptypb.Empty)(nil),                 // 45: google.protobuf.Empty
	(*anypb.Any)(nil),                     // 46: google.protobuf.Any
	(*fieldmaskpb.FieldMask)(nil),         // 47: google.protobuf.FieldMask
}
var file_internal_testprotos_textpbeditions_test2_proto_depIdxs = []int32{
	6,  // 0: pbeditions.NestsUTF8Validated.validated_message:type_name -> pbeditions.UTF8Validated
	0,  // 1: pbeditions.Enums.opt_enum:type_name -> pbeditions.Enum
	0,  // 2: pbeditions.Enums.rpt_enum:type_name -> pbeditions.Enum
	1,  // 3: pbeditions.Enums.implicit_enum:type_name -> pbeditions.OpenEnum
	2,  // 4: pbeditions.Enums.opt_nested_enum:type_name -> pbeditions.Enums.NestedEnum
	2,  // 5: pbeditions.Enums.rpt_nested_enum:type_name -> pbeditions.Enums.NestedEnum
	3,  // 6: pbeditions.Enums.implicit_nested_enum:type_name -> pbeditions.Enums.NestedOpenEnum
	24, // 7: pbeditions.Maps.int32_to_str:type_name -> pbeditions.Maps.Int32ToStrEntry
	25, // 8: pbeditions.Maps.str_to_nested:type_name -> pbeditions.Maps.StrToNestedEntry
	11, // 9: pbeditions.Nested.opt_nested:type_name -> pbeditions.Nested
	11, // 10: pbeditions.Nests.opt_nested:type_name -> pbeditions.Nested
	26, // 11: pbeditions.Nests.optgroup:type_name -> pbeditions.Nests.OptGroup
	11, // 12: pbeditions.Nests.rpt_nested:type_name -> pbeditions.Nested
	27, // 13: pbeditions.Nests.rptgroup:type_name -> pbeditions.Nests.RptGroup
	0,  // 14: pbeditions.Requireds.req_enum:type_name -> pbeditions.Enum
	11, // 15: pbeditions.Requireds.req_nested:type_name -> pbeditions.Nested
	15, // 16: pbeditions.IndirectRequired.opt_nested:type_name -> pbeditions.NestedWithRequired
	15, // 17: pbeditions.IndirectRequired.rpt_nested:type_name -> pbeditions.NestedWithRequired
	29, // 18: pbeditions.IndirectRequired.str_to_nested:type_name -> pbeditions.IndirectRequired.StrToNestedEntry
	15, // 19: pbeditions.IndirectRequired.oneof_nested:type_name -> pbeditions.NestedWithRequired
	30, // 20: pbeditions.KnownTypes.opt_bool:type_name -> google.protobuf.BoolValue
	31, // 21: pbeditions.KnownTypes.opt_int32:type_name -> google.protobuf.Int32Value
	32, // 22: pbeditions.KnownTypes.opt_int64:type_name -> google.protobuf.Int64Value
	33, // 23: pbeditions.KnownTypes.opt_uint32:type_name -> google.protobuf.UInt32Value
	34, // 24: pbeditions.KnownTypes.opt_uint64:type_name -> google.protobuf.UInt64Value
	35, // 25: pbeditions.KnownTypes.opt_float:type_name -> google.protobuf.FloatValue
	36, // 26: pbeditions.KnownTypes.opt_double:type_name -> google.protobuf.DoubleValue
	37, // 27: pbeditions.KnownTypes.opt_string:type_name -> google.protobuf.StringValue
	38, // 28: pbeditions.KnownTypes.opt_bytes:type_name -> google.protobuf.BytesValue
	39, // 29: pbeditions.KnownTypes.opt_duration:type_name -> google.protobuf.Duration
	40, // 30: pbeditions.KnownTypes.opt_timestamp:type_name -> google.protobuf.Timestamp
	41, // 31: pbeditions.KnownTypes.opt_struct:type_name -> google.protobuf.Struct
	42, // 32: pbeditions.KnownTypes.opt_list:type_name -> google.protobuf.ListValue
	43, // 33: pbeditions.KnownTypes.opt_value:type_name -> google.protobuf.Value
	44, // 34: pbeditions.KnownTypes.opt_null:type_name -> google.protobuf.NullValue
	45, // 35: pbeditions.KnownTypes.opt_empty:type_name -> google.protobuf.Empty
	46, // 36: pbeditions.KnownTypes.opt_any:type_name -> google.protobuf.Any
	47, // 37: pbeditions.KnownTypes.opt_fieldmask:type_name -> google.protobuf.FieldMask
	11, // 38: pbeditions.Maps.StrToNestedEntry.value:type_name -> pbeditions.Nested
	11, // 39: pbeditions.Nests.OptGroup.opt_nested:type_name -> pbeditions.Nested
	28, // 40: pbeditions.Nests.OptGroup.optnestedgroup:type_name -> pbeditions.Nests.OptGroup.OptNestedGroup
	15, // 41: pbeditions.IndirectRequired.StrToNestedEntry.value:type_name -> pbeditions.NestedWithRequired
	17, // 42: pbeditions.opt_ext_bool:extendee -> pbeditions.Extensions
	17, // 43: pbeditions.opt_ext_string:extendee -> pbeditions.Extensions
	17, // 44: pbeditions.opt_ext_enum:extendee -> pbeditions.Extensions
	17, // 45: pbeditions.opt_ext_nested:extendee -> pbeditions.Extensions
	17, // 46: pbeditions.opt_ext_partial:extendee -> pbeditions.Extensions
	17, // 47: pbeditions.rpt_ext_fixed32:extendee -> pbeditions.Extensions
	17, // 48: pbeditions.rpt_ext_enum:extendee -> pbeditions.Extensions
	17, // 49: pbeditions.rpt_ext_nested:extendee -> pbeditions.Extensions
	19, // 50: pbeditions.message_set_extension:extendee -> pbeditions.MessageSet
	17, // 51: pbeditions.ExtensionsContainer.opt_ext_bool:extendee -> pbeditions.Extensions
	17, // 52: pbeditions.ExtensionsContainer.opt_ext_string:extendee -> pbeditions.Extensions
	17, // 53: pbeditions.ExtensionsContainer.opt_ext_enum:extendee -> pbeditions.Extensions
	17, // 54: pbeditions.ExtensionsContainer.opt_ext_nested:extendee -> pbeditions.Extensions
	17, // 55: pbeditions.ExtensionsContainer.opt_ext_partial:extendee -> pbeditions.Extensions
	17, // 56: pbeditions.ExtensionsContainer.rpt_ext_string:extendee -> pbeditions.Extensions
	17, // 57: pbeditions.ExtensionsContainer.rpt_ext_enum:extendee -> pbeditions.Extensions
	17, // 58: pbeditions.ExtensionsContainer.rpt_ext_nested:extendee -> pbeditions.Extensions
	19, // 59: pbeditions.MessageSetExtension.message_set_extension:extendee -> pbeditions.MessageSet
	19, // 60: pbeditions.MessageSetExtension.not_message_set_extension:extendee -> pbeditions.MessageSet
	19, // 61: pbeditions.MessageSetExtension.ext_nested:extendee -> pbeditions.MessageSet
	21, // 62: pbeditions.FakeMessageSetExtension.message_set_extension:extendee -> pbeditions.FakeMessageSet
	0,  // 63: pbeditions.opt_ext_enum:type_name -> pbeditions.Enum
	11, // 64: pbeditions.opt_ext_nested:type_name -> pbeditions.Nested
	14, // 65: pbeditions.opt_ext_partial:type_name -> pbeditions.PartialRequired
	0,  // 66: pbeditions.rpt_ext_enum:type_name -> pbeditions.Enum
	11, // 67: pbeditions.rpt_ext_nested:type_name -> pbeditions.Nested
	22, // 68: pbeditions.message_set_extension:type_name -> pbeditions.FakeMessageSetExtension
	0,  // 69: pbeditions.ExtensionsContainer.opt_ext_enum:type_name -> pbeditions.Enum
	11, // 70: pbeditions.ExtensionsContainer.opt_ext_nested:type_name -> pbeditions.Nested
	14, // 71: pbeditions.ExtensionsContainer.opt_ext_partial:type_name -> pbeditions.PartialRequired
	0,  // 72: pbeditions.ExtensionsContainer.rpt_ext_enum:type_name -> pbeditions.Enum
	11, // 73: pbeditions.ExtensionsContainer.rpt_ext_nested:type_name -> pbeditions.Nested
	20, // 74: pbeditions.MessageSetExtension.message_set_extension:type_name -> pbeditions.MessageSetExtension
	20, // 75: pbeditions.MessageSetExtension.not_message_set_extension:type_name -> pbeditions.MessageSetExtension
	11, // 76: pbeditions.MessageSetExtension.ext_nested:type_name -> pbeditions.Nested
	22, // 77: pbeditions.FakeMessageSetExtension.message_set_extension:type_name -> pbeditions.FakeMessageSetExtension
	78, // [78:78] is the sub-list for method output_type
	78, // [78:78] is the sub-list for method input_type
	63, // [63:78] is the sub-list for extension type_name
	42, // [42:63] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_internal_testprotos_textpbeditions_test2_proto_init() }
func file_internal_testprotos_textpbeditions_test2_proto_init() {
	if File_internal_testprotos_textpbeditions_test2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scalars); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImplicitScalars); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UTF8Validated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NestsUTF8Validated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enums); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Repeats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Maps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Requireds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartialRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NestedWithRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndirectRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Extensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtensionsContainer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageSetExtension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FakeMessageSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FakeMessageSetExtension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnownTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests_OptGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests_RptGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpbeditions_test2_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests_OptGroup_OptNestedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_internal_testprotos_textpbeditions_test2_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*IndirectRequired_OneofNested)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_textpbeditions_test2_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   26,
			NumExtensions: 21,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_textpbeditions_test2_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_textpbeditions_test2_proto_depIdxs,
		EnumInfos:         file_internal_testprotos_textpbeditions_test2_proto_enumTypes,
		MessageInfos:      file_internal_testprotos_textpbeditions_test2_proto_msgTypes,
		ExtensionInfos:    file_internal_testprotos_textpbeditions_test2_proto_extTypes,
	}.Build()
	File_internal_testprotos_textpbeditions_test2_proto = out.File
	file_internal_testprotos_textpbeditions_test2_proto_rawDesc = nil
	file_internal_testprotos_textpbeditions_test2_proto_goTypes = nil
	file_internal_testprotos_textpbeditions_test2_proto_depIdxs = nil
}
