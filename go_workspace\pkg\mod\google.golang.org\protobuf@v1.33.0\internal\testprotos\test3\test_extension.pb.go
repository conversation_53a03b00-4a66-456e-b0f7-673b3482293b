// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/test3/test_extension.proto

package test3

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
)

var file_internal_testprotos_test3_test_extension_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1001,
		Name:          "goproto.proto.test3.optional_int32",
		Tag:           "varint,1001,opt,name=optional_int32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1002,
		Name:          "goproto.proto.test3.optional_int64",
		Tag:           "varint,1002,opt,name=optional_int64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         1003,
		Name:          "goproto.proto.test3.optional_uint32",
		Tag:           "varint,1003,opt,name=optional_uint32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         1004,
		Name:          "goproto.proto.test3.optional_uint64",
		Tag:           "varint,1004,opt,name=optional_uint64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1005,
		Name:          "goproto.proto.test3.optional_sint32",
		Tag:           "zigzag32,1005,opt,name=optional_sint32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1006,
		Name:          "goproto.proto.test3.optional_sint64",
		Tag:           "zigzag64,1006,opt,name=optional_sint64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         1007,
		Name:          "goproto.proto.test3.optional_fixed32",
		Tag:           "fixed32,1007,opt,name=optional_fixed32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         1008,
		Name:          "goproto.proto.test3.optional_fixed64",
		Tag:           "fixed64,1008,opt,name=optional_fixed64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1009,
		Name:          "goproto.proto.test3.optional_sfixed32",
		Tag:           "fixed32,1009,opt,name=optional_sfixed32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1010,
		Name:          "goproto.proto.test3.optional_sfixed64",
		Tag:           "fixed64,1010,opt,name=optional_sfixed64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         1011,
		Name:          "goproto.proto.test3.optional_float",
		Tag:           "fixed32,1011,opt,name=optional_float",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         1012,
		Name:          "goproto.proto.test3.optional_double",
		Tag:           "fixed64,1012,opt,name=optional_double",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1013,
		Name:          "goproto.proto.test3.optional_bool",
		Tag:           "varint,1013,opt,name=optional_bool",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         1014,
		Name:          "goproto.proto.test3.optional_string",
		Tag:           "bytes,1014,opt,name=optional_string",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         1015,
		Name:          "goproto.proto.test3.optional_bytes",
		Tag:           "bytes,1015,opt,name=optional_bytes",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignMessage)(nil),
		Field:         1016,
		Name:          "goproto.proto.test3.optional_foreign_message",
		Tag:           "bytes,1016,opt,name=optional_foreign_message",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignEnum)(nil),
		Field:         1017,
		Name:          "goproto.proto.test3.optional_foreign_enum",
		Tag:           "varint,1017,opt,name=optional_foreign_enum,enum=goproto.proto.test3.ForeignEnum",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         2001,
		Name:          "goproto.proto.test3.optional_optional_int32",
		Tag:           "varint,2001,opt,name=optional_optional_int32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2002,
		Name:          "goproto.proto.test3.optional_optional_int64",
		Tag:           "varint,2002,opt,name=optional_optional_int64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         2003,
		Name:          "goproto.proto.test3.optional_optional_uint32",
		Tag:           "varint,2003,opt,name=optional_optional_uint32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         2004,
		Name:          "goproto.proto.test3.optional_optional_uint64",
		Tag:           "varint,2004,opt,name=optional_optional_uint64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         2005,
		Name:          "goproto.proto.test3.optional_optional_sint32",
		Tag:           "zigzag32,2005,opt,name=optional_optional_sint32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2006,
		Name:          "goproto.proto.test3.optional_optional_sint64",
		Tag:           "zigzag64,2006,opt,name=optional_optional_sint64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         2007,
		Name:          "goproto.proto.test3.optional_optional_fixed32",
		Tag:           "fixed32,2007,opt,name=optional_optional_fixed32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         2008,
		Name:          "goproto.proto.test3.optional_optional_fixed64",
		Tag:           "fixed64,2008,opt,name=optional_optional_fixed64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         2009,
		Name:          "goproto.proto.test3.optional_optional_sfixed32",
		Tag:           "fixed32,2009,opt,name=optional_optional_sfixed32",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2010,
		Name:          "goproto.proto.test3.optional_optional_sfixed64",
		Tag:           "fixed64,2010,opt,name=optional_optional_sfixed64",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         2011,
		Name:          "goproto.proto.test3.optional_optional_float",
		Tag:           "fixed32,2011,opt,name=optional_optional_float",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         2012,
		Name:          "goproto.proto.test3.optional_optional_double",
		Tag:           "fixed64,2012,opt,name=optional_optional_double",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         2013,
		Name:          "goproto.proto.test3.optional_optional_bool",
		Tag:           "varint,2013,opt,name=optional_optional_bool",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         2014,
		Name:          "goproto.proto.test3.optional_optional_string",
		Tag:           "bytes,2014,opt,name=optional_optional_string",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         2015,
		Name:          "goproto.proto.test3.optional_optional_bytes",
		Tag:           "bytes,2015,opt,name=optional_optional_bytes",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignMessage)(nil),
		Field:         2016,
		Name:          "goproto.proto.test3.optional_optional_foreign_message",
		Tag:           "bytes,2016,opt,name=optional_optional_foreign_message",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignEnum)(nil),
		Field:         2017,
		Name:          "goproto.proto.test3.optional_optional_foreign_enum",
		Tag:           "varint,2017,opt,name=optional_optional_foreign_enum,enum=goproto.proto.test3.ForeignEnum",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
}

// Extension fields to descriptorpb.MessageOptions.
var (
	// optional int32 optional_int32 = 1001;
	E_OptionalInt32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[0]
	// optional int64 optional_int64 = 1002;
	E_OptionalInt64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[1]
	// optional uint32 optional_uint32 = 1003;
	E_OptionalUint32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[2]
	// optional uint64 optional_uint64 = 1004;
	E_OptionalUint64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[3]
	// optional sint32 optional_sint32 = 1005;
	E_OptionalSint32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[4]
	// optional sint64 optional_sint64 = 1006;
	E_OptionalSint64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[5]
	// optional fixed32 optional_fixed32 = 1007;
	E_OptionalFixed32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[6]
	// optional fixed64 optional_fixed64 = 1008;
	E_OptionalFixed64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[7]
	// optional sfixed32 optional_sfixed32 = 1009;
	E_OptionalSfixed32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[8]
	// optional sfixed64 optional_sfixed64 = 1010;
	E_OptionalSfixed64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[9]
	// optional float optional_float = 1011;
	E_OptionalFloat = &file_internal_testprotos_test3_test_extension_proto_extTypes[10]
	// optional double optional_double = 1012;
	E_OptionalDouble = &file_internal_testprotos_test3_test_extension_proto_extTypes[11]
	// optional bool optional_bool = 1013;
	E_OptionalBool = &file_internal_testprotos_test3_test_extension_proto_extTypes[12]
	// optional string optional_string = 1014;
	E_OptionalString = &file_internal_testprotos_test3_test_extension_proto_extTypes[13]
	// optional bytes optional_bytes = 1015;
	E_OptionalBytes = &file_internal_testprotos_test3_test_extension_proto_extTypes[14]
	// optional goproto.proto.test3.ForeignMessage optional_foreign_message = 1016;
	E_OptionalForeignMessage = &file_internal_testprotos_test3_test_extension_proto_extTypes[15]
	// optional goproto.proto.test3.ForeignEnum optional_foreign_enum = 1017;
	E_OptionalForeignEnum = &file_internal_testprotos_test3_test_extension_proto_extTypes[16]
	// optional int32 optional_optional_int32 = 2001;
	E_OptionalOptionalInt32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[17]
	// optional int64 optional_optional_int64 = 2002;
	E_OptionalOptionalInt64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[18]
	// optional uint32 optional_optional_uint32 = 2003;
	E_OptionalOptionalUint32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[19]
	// optional uint64 optional_optional_uint64 = 2004;
	E_OptionalOptionalUint64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[20]
	// optional sint32 optional_optional_sint32 = 2005;
	E_OptionalOptionalSint32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[21]
	// optional sint64 optional_optional_sint64 = 2006;
	E_OptionalOptionalSint64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[22]
	// optional fixed32 optional_optional_fixed32 = 2007;
	E_OptionalOptionalFixed32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[23]
	// optional fixed64 optional_optional_fixed64 = 2008;
	E_OptionalOptionalFixed64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[24]
	// optional sfixed32 optional_optional_sfixed32 = 2009;
	E_OptionalOptionalSfixed32 = &file_internal_testprotos_test3_test_extension_proto_extTypes[25]
	// optional sfixed64 optional_optional_sfixed64 = 2010;
	E_OptionalOptionalSfixed64 = &file_internal_testprotos_test3_test_extension_proto_extTypes[26]
	// optional float optional_optional_float = 2011;
	E_OptionalOptionalFloat = &file_internal_testprotos_test3_test_extension_proto_extTypes[27]
	// optional double optional_optional_double = 2012;
	E_OptionalOptionalDouble = &file_internal_testprotos_test3_test_extension_proto_extTypes[28]
	// optional bool optional_optional_bool = 2013;
	E_OptionalOptionalBool = &file_internal_testprotos_test3_test_extension_proto_extTypes[29]
	// optional string optional_optional_string = 2014;
	E_OptionalOptionalString = &file_internal_testprotos_test3_test_extension_proto_extTypes[30]
	// optional bytes optional_optional_bytes = 2015;
	E_OptionalOptionalBytes = &file_internal_testprotos_test3_test_extension_proto_extTypes[31]
	// optional goproto.proto.test3.ForeignMessage optional_optional_foreign_message = 2016;
	E_OptionalOptionalForeignMessage = &file_internal_testprotos_test3_test_extension_proto_extTypes[32]
	// optional goproto.proto.test3.ForeignEnum optional_optional_foreign_enum = 2017;
	E_OptionalOptionalForeignEnum = &file_internal_testprotos_test3_test_extension_proto_extTypes[33]
)

var File_internal_testprotos_test3_test_extension_proto protoreflect.FileDescriptor

var file_internal_testprotos_test3_test_extension_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x13, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73,
	0x74, 0x33, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3a, 0x47, 0x0a,
	0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x47, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xea, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x3a,
	0x49, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x49, 0x0a, 0x0f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xec,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x49, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xed, 0x07, 0x20, 0x01, 0x28, 0x11,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x3a, 0x49, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0xee, 0x07, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x4b, 0x0a, 0x10, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xef, 0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x4b, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf0, 0x07,
	0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x4d, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf1, 0x07, 0x20, 0x01,
	0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x3a, 0x4d, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf2, 0x07, 0x20, 0x01, 0x28,
	0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x3a, 0x47, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf3, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x49, 0x0a, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xf4, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x45, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf5, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x49,
	0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xf6, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x47, 0x0a, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf7, 0x07, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x3a, 0x7f, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xf8, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x16, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x3a, 0x76, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf9, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x5b, 0x0a, 0x17, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd1, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x88, 0x01, 0x01, 0x3a, 0x5b, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd2, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x88, 0x01, 0x01, 0x3a, 0x5d, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xd3, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x88, 0x01, 0x01, 0x3a, 0x5d, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xd4, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x88, 0x01, 0x01, 0x3a, 0x5d, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xd5, 0x0f, 0x20, 0x01, 0x28, 0x11, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x88,
	0x01, 0x01, 0x3a, 0x5d, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xd6, 0x0f, 0x20, 0x01, 0x28, 0x12, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x88, 0x01,
	0x01, 0x3a, 0x5f, 0x0a, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xd7, 0x0f, 0x20, 0x01, 0x28, 0x07, 0x52, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x88,
	0x01, 0x01, 0x3a, 0x5f, 0x0a, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xd8, 0x0f, 0x20, 0x01, 0x28, 0x06, 0x52, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x88, 0x01, 0x01, 0x3a, 0x61, 0x0a, 0x1a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xd9, 0x0f, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x88, 0x01, 0x01, 0x3a, 0x61, 0x0a, 0x1a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xda, 0x0f, 0x20, 0x01, 0x28, 0x10, 0x52, 0x18, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x88, 0x01, 0x01, 0x3a, 0x5b, 0x0a, 0x17, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdb, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x5d, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xdc, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x88, 0x01, 0x01, 0x3a, 0x59, 0x0a, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xdd, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x88, 0x01, 0x01,
	0x3a, 0x5d, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xde, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x3a,
	0x5b, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdf, 0x0f, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x3a, 0x93, 0x01, 0x0a,
	0x21, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xe0, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33,
	0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x1e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88,
	0x01, 0x01, 0x3a, 0x8a, 0x01, 0x0a, 0x1e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe1, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x1b, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x42,
	0x36, 0x5a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x33, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_internal_testprotos_test3_test_extension_proto_goTypes = []interface{}{
	(*descriptorpb.MessageOptions)(nil), // 0: google.protobuf.MessageOptions
	(*ForeignMessage)(nil),              // 1: goproto.proto.test3.ForeignMessage
	(ForeignEnum)(0),                    // 2: goproto.proto.test3.ForeignEnum
}
var file_internal_testprotos_test3_test_extension_proto_depIdxs = []int32{
	0,  // 0: goproto.proto.test3.optional_int32:extendee -> google.protobuf.MessageOptions
	0,  // 1: goproto.proto.test3.optional_int64:extendee -> google.protobuf.MessageOptions
	0,  // 2: goproto.proto.test3.optional_uint32:extendee -> google.protobuf.MessageOptions
	0,  // 3: goproto.proto.test3.optional_uint64:extendee -> google.protobuf.MessageOptions
	0,  // 4: goproto.proto.test3.optional_sint32:extendee -> google.protobuf.MessageOptions
	0,  // 5: goproto.proto.test3.optional_sint64:extendee -> google.protobuf.MessageOptions
	0,  // 6: goproto.proto.test3.optional_fixed32:extendee -> google.protobuf.MessageOptions
	0,  // 7: goproto.proto.test3.optional_fixed64:extendee -> google.protobuf.MessageOptions
	0,  // 8: goproto.proto.test3.optional_sfixed32:extendee -> google.protobuf.MessageOptions
	0,  // 9: goproto.proto.test3.optional_sfixed64:extendee -> google.protobuf.MessageOptions
	0,  // 10: goproto.proto.test3.optional_float:extendee -> google.protobuf.MessageOptions
	0,  // 11: goproto.proto.test3.optional_double:extendee -> google.protobuf.MessageOptions
	0,  // 12: goproto.proto.test3.optional_bool:extendee -> google.protobuf.MessageOptions
	0,  // 13: goproto.proto.test3.optional_string:extendee -> google.protobuf.MessageOptions
	0,  // 14: goproto.proto.test3.optional_bytes:extendee -> google.protobuf.MessageOptions
	0,  // 15: goproto.proto.test3.optional_foreign_message:extendee -> google.protobuf.MessageOptions
	0,  // 16: goproto.proto.test3.optional_foreign_enum:extendee -> google.protobuf.MessageOptions
	0,  // 17: goproto.proto.test3.optional_optional_int32:extendee -> google.protobuf.MessageOptions
	0,  // 18: goproto.proto.test3.optional_optional_int64:extendee -> google.protobuf.MessageOptions
	0,  // 19: goproto.proto.test3.optional_optional_uint32:extendee -> google.protobuf.MessageOptions
	0,  // 20: goproto.proto.test3.optional_optional_uint64:extendee -> google.protobuf.MessageOptions
	0,  // 21: goproto.proto.test3.optional_optional_sint32:extendee -> google.protobuf.MessageOptions
	0,  // 22: goproto.proto.test3.optional_optional_sint64:extendee -> google.protobuf.MessageOptions
	0,  // 23: goproto.proto.test3.optional_optional_fixed32:extendee -> google.protobuf.MessageOptions
	0,  // 24: goproto.proto.test3.optional_optional_fixed64:extendee -> google.protobuf.MessageOptions
	0,  // 25: goproto.proto.test3.optional_optional_sfixed32:extendee -> google.protobuf.MessageOptions
	0,  // 26: goproto.proto.test3.optional_optional_sfixed64:extendee -> google.protobuf.MessageOptions
	0,  // 27: goproto.proto.test3.optional_optional_float:extendee -> google.protobuf.MessageOptions
	0,  // 28: goproto.proto.test3.optional_optional_double:extendee -> google.protobuf.MessageOptions
	0,  // 29: goproto.proto.test3.optional_optional_bool:extendee -> google.protobuf.MessageOptions
	0,  // 30: goproto.proto.test3.optional_optional_string:extendee -> google.protobuf.MessageOptions
	0,  // 31: goproto.proto.test3.optional_optional_bytes:extendee -> google.protobuf.MessageOptions
	0,  // 32: goproto.proto.test3.optional_optional_foreign_message:extendee -> google.protobuf.MessageOptions
	0,  // 33: goproto.proto.test3.optional_optional_foreign_enum:extendee -> google.protobuf.MessageOptions
	1,  // 34: goproto.proto.test3.optional_foreign_message:type_name -> goproto.proto.test3.ForeignMessage
	2,  // 35: goproto.proto.test3.optional_foreign_enum:type_name -> goproto.proto.test3.ForeignEnum
	1,  // 36: goproto.proto.test3.optional_optional_foreign_message:type_name -> goproto.proto.test3.ForeignMessage
	2,  // 37: goproto.proto.test3.optional_optional_foreign_enum:type_name -> goproto.proto.test3.ForeignEnum
	38, // [38:38] is the sub-list for method output_type
	38, // [38:38] is the sub-list for method input_type
	34, // [34:38] is the sub-list for extension type_name
	0,  // [0:34] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_internal_testprotos_test3_test_extension_proto_init() }
func file_internal_testprotos_test3_test_extension_proto_init() {
	if File_internal_testprotos_test3_test_extension_proto != nil {
		return
	}
	file_internal_testprotos_test3_test_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_test3_test_extension_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 34,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_test3_test_extension_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_test3_test_extension_proto_depIdxs,
		ExtensionInfos:    file_internal_testprotos_test3_test_extension_proto_extTypes,
	}.Build()
	File_internal_testprotos_test3_test_extension_proto = out.File
	file_internal_testprotos_test3_test_extension_proto_rawDesc = nil
	file_internal_testprotos_test3_test_extension_proto_goTypes = nil
	file_internal_testprotos_test3_test_extension_proto_depIdxs = nil
}
