// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/testeditions/test.proto

package testeditions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type ForeignEnum int32

const (
	ForeignEnum_FOREIGN_ZERO ForeignEnum = 0
	ForeignEnum_FOREIGN_FOO  ForeignEnum = 4
	ForeignEnum_FOREIGN_BAR  ForeignEnum = 5
	ForeignEnum_FOREIGN_BAZ  ForeignEnum = 6
)

// Enum value maps for ForeignEnum.
var (
	ForeignEnum_name = map[int32]string{
		0: "FOREIGN_ZERO",
		4: "FOREIGN_FOO",
		5: "FOREIGN_BAR",
		6: "FOREIGN_BAZ",
	}
	ForeignEnum_value = map[string]int32{
		"FOREIGN_ZERO": 0,
		"FOREIGN_FOO":  4,
		"FOREIGN_BAR":  5,
		"FOREIGN_BAZ":  6,
	}
)

func (x ForeignEnum) Enum() *ForeignEnum {
	p := new(ForeignEnum)
	*p = x
	return p
}

func (x ForeignEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForeignEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_testeditions_test_proto_enumTypes[0].Descriptor()
}

func (ForeignEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_testeditions_test_proto_enumTypes[0]
}

func (x ForeignEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForeignEnum.Descriptor instead.
func (ForeignEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0}
}

type TestAllTypes_NestedEnum int32

const (
	TestAllTypes_FOO TestAllTypes_NestedEnum = 0
	TestAllTypes_BAR TestAllTypes_NestedEnum = 1
	TestAllTypes_BAZ TestAllTypes_NestedEnum = 2
	TestAllTypes_NEG TestAllTypes_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllTypes_NestedEnum.
var (
	TestAllTypes_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllTypes_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllTypes_NestedEnum) Enum() *TestAllTypes_NestedEnum {
	p := new(TestAllTypes_NestedEnum)
	*p = x
	return p
}

func (x TestAllTypes_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypes_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_testeditions_test_proto_enumTypes[1].Descriptor()
}

func (TestAllTypes_NestedEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_testeditions_test_proto_enumTypes[1]
}

func (x TestAllTypes_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllTypes_NestedEnum.Descriptor instead.
func (TestAllTypes_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0, 0}
}

type TestAllTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SingularInt32          int32                                  `protobuf:"varint,124,opt,name=singular_int32,json=singularInt32" json:"singular_int32,omitempty"`
	SingularInt64          int64                                  `protobuf:"varint,125,opt,name=singular_int64,json=singularInt64" json:"singular_int64,omitempty"`
	SingularUint32         uint32                                 `protobuf:"varint,126,opt,name=singular_uint32,json=singularUint32" json:"singular_uint32,omitempty"`
	SingularUint64         uint64                                 `protobuf:"varint,127,opt,name=singular_uint64,json=singularUint64" json:"singular_uint64,omitempty"`
	SingularSint32         int32                                  `protobuf:"zigzag32,128,opt,name=singular_sint32,json=singularSint32" json:"singular_sint32,omitempty"`
	SingularSint64         int64                                  `protobuf:"zigzag64,129,opt,name=singular_sint64,json=singularSint64" json:"singular_sint64,omitempty"`
	SingularFixed32        uint32                                 `protobuf:"fixed32,130,opt,name=singular_fixed32,json=singularFixed32" json:"singular_fixed32,omitempty"`
	SingularFixed64        uint64                                 `protobuf:"fixed64,131,opt,name=singular_fixed64,json=singularFixed64" json:"singular_fixed64,omitempty"`
	SingularSfixed32       int32                                  `protobuf:"fixed32,132,opt,name=singular_sfixed32,json=singularSfixed32" json:"singular_sfixed32,omitempty"`
	SingularSfixed64       int64                                  `protobuf:"fixed64,133,opt,name=singular_sfixed64,json=singularSfixed64" json:"singular_sfixed64,omitempty"`
	SingularFloat          float32                                `protobuf:"fixed32,134,opt,name=singular_float,json=singularFloat" json:"singular_float,omitempty"`
	SingularDouble         float64                                `protobuf:"fixed64,135,opt,name=singular_double,json=singularDouble" json:"singular_double,omitempty"`
	SingularBool           bool                                   `protobuf:"varint,136,opt,name=singular_bool,json=singularBool" json:"singular_bool,omitempty"`
	SingularString         string                                 `protobuf:"bytes,137,opt,name=singular_string,json=singularString" json:"singular_string,omitempty"`
	SingularBytes          []byte                                 `protobuf:"bytes,138,opt,name=singular_bytes,json=singularBytes" json:"singular_bytes,omitempty"`
	OptionalInt32          *int32                                 `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalInt64          *int64                                 `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalUint32         *uint32                                `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalUint64         *uint64                                `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalSint32         *int32                                 `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalSint64         *int64                                 `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalFixed32        *uint32                                `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalFixed64        *uint64                                `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed32       *int32                                 `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalSfixed64       *int64                                 `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalFloat          *float32                               `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalDouble         *float64                               `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalBool           *bool                                  `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalString         *string                                `protobuf:"bytes,14,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                                 `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	Optionalgroup          *TestAllTypes_OptionalGroup            `protobuf:"group,16,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	OptionalNestedMessage  *TestAllTypes_NestedMessage            `protobuf:"bytes,18,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	OptionalForeignMessage *ForeignMessage                        `protobuf:"bytes,19,opt,name=optional_foreign_message,json=optionalForeignMessage" json:"optional_foreign_message,omitempty"`
	OptionalNestedEnum     *TestAllTypes_NestedEnum               `protobuf:"varint,21,opt,name=optional_nested_enum,json=optionalNestedEnum,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum" json:"optional_nested_enum,omitempty"`
	OptionalForeignEnum    *ForeignEnum                           `protobuf:"varint,22,opt,name=optional_foreign_enum,json=optionalForeignEnum,enum=goproto.proto.testeditions.ForeignEnum" json:"optional_foreign_enum,omitempty"`
	RepeatedInt32          []int32                                `protobuf:"varint,31,rep,packed,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedInt64          []int64                                `protobuf:"varint,32,rep,packed,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedUint32         []uint32                               `protobuf:"varint,33,rep,packed,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedUint64         []uint64                               `protobuf:"varint,34,rep,packed,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedSint32         []int32                                `protobuf:"zigzag32,35,rep,packed,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedSint64         []int64                                `protobuf:"zigzag64,36,rep,packed,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedFixed32        []uint32                               `protobuf:"fixed32,37,rep,packed,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedFixed64        []uint64                               `protobuf:"fixed64,38,rep,packed,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed32       []int32                                `protobuf:"fixed32,39,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedSfixed64       []int64                                `protobuf:"fixed64,40,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedFloat          []float32                              `protobuf:"fixed32,41,rep,packed,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedDouble         []float64                              `protobuf:"fixed64,42,rep,packed,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedBool           []bool                                 `protobuf:"varint,43,rep,packed,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedString         []string                               `protobuf:"bytes,44,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                               `protobuf:"bytes,45,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	Repeatedgroup          []*TestAllTypes_RepeatedGroup          `protobuf:"group,46,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
	RepeatedNestedMessage  []*TestAllTypes_NestedMessage          `protobuf:"bytes,48,rep,name=repeated_nested_message,json=repeatedNestedMessage" json:"repeated_nested_message,omitempty"`
	RepeatedForeignMessage []*ForeignMessage                      `protobuf:"bytes,49,rep,name=repeated_foreign_message,json=repeatedForeignMessage" json:"repeated_foreign_message,omitempty"`
	RepeatedNestedEnum     []TestAllTypes_NestedEnum              `protobuf:"varint,51,rep,packed,name=repeated_nested_enum,json=repeatedNestedEnum,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum" json:"repeated_nested_enum,omitempty"`
	RepeatedForeignEnum    []ForeignEnum                          `protobuf:"varint,52,rep,packed,name=repeated_foreign_enum,json=repeatedForeignEnum,enum=goproto.proto.testeditions.ForeignEnum" json:"repeated_foreign_enum,omitempty"`
	MapInt32Int32          map[int32]int32                        `protobuf:"bytes,56,rep,name=map_int32_int32,json=mapInt32Int32" json:"map_int32_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Int64          map[int64]int64                        `protobuf:"bytes,57,rep,name=map_int64_int64,json=mapInt64Int64" json:"map_int64_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Uint32        map[uint32]uint32                      `protobuf:"bytes,58,rep,name=map_uint32_uint32,json=mapUint32Uint32" json:"map_uint32_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Uint64        map[uint64]uint64                      `protobuf:"bytes,59,rep,name=map_uint64_uint64,json=mapUint64Uint64" json:"map_uint64_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Sint32        map[int32]int32                        `protobuf:"bytes,60,rep,name=map_sint32_sint32,json=mapSint32Sint32" json:"map_sint32_sint32,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapSint64Sint64        map[int64]int64                        `protobuf:"bytes,61,rep,name=map_sint64_sint64,json=mapSint64Sint64" json:"map_sint64_sint64,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapFixed32Fixed32      map[uint32]uint32                      `protobuf:"bytes,62,rep,name=map_fixed32_fixed32,json=mapFixed32Fixed32" json:"map_fixed32_fixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapFixed64Fixed64      map[uint64]uint64                      `protobuf:"bytes,63,rep,name=map_fixed64_fixed64,json=mapFixed64Fixed64" json:"map_fixed64_fixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapSfixed32Sfixed32    map[int32]int32                        `protobuf:"bytes,64,rep,name=map_sfixed32_sfixed32,json=mapSfixed32Sfixed32" json:"map_sfixed32_sfixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapSfixed64Sfixed64    map[int64]int64                        `protobuf:"bytes,65,rep,name=map_sfixed64_sfixed64,json=mapSfixed64Sfixed64" json:"map_sfixed64_sfixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapInt32Float          map[int32]float32                      `protobuf:"bytes,66,rep,name=map_int32_float,json=mapInt32Float" json:"map_int32_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapInt32Double         map[int32]float64                      `protobuf:"bytes,67,rep,name=map_int32_double,json=mapInt32Double" json:"map_int32_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolBool            map[bool]bool                          `protobuf:"bytes,68,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringString        map[string]string                      `protobuf:"bytes,69,rep,name=map_string_string,json=mapStringString" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringBytes         map[string][]byte                      `protobuf:"bytes,70,rep,name=map_string_bytes,json=mapStringBytes" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedMessage map[string]*TestAllTypes_NestedMessage `protobuf:"bytes,71,rep,name=map_string_nested_message,json=mapStringNestedMessage" json:"map_string_nested_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedEnum    map[string]TestAllTypes_NestedEnum     `protobuf:"bytes,73,rep,name=map_string_nested_enum,json=mapStringNestedEnum" json:"map_string_nested_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum"`
	// Singular with defaults
	DefaultInt32       *int32                   `protobuf:"varint,81,opt,name=default_int32,json=defaultInt32,def=81" json:"default_int32,omitempty"`
	DefaultInt64       *int64                   `protobuf:"varint,82,opt,name=default_int64,json=defaultInt64,def=82" json:"default_int64,omitempty"`
	DefaultUint32      *uint32                  `protobuf:"varint,83,opt,name=default_uint32,json=defaultUint32,def=83" json:"default_uint32,omitempty"`
	DefaultUint64      *uint64                  `protobuf:"varint,84,opt,name=default_uint64,json=defaultUint64,def=84" json:"default_uint64,omitempty"`
	DefaultSint32      *int32                   `protobuf:"zigzag32,85,opt,name=default_sint32,json=defaultSint32,def=-85" json:"default_sint32,omitempty"`
	DefaultSint64      *int64                   `protobuf:"zigzag64,86,opt,name=default_sint64,json=defaultSint64,def=86" json:"default_sint64,omitempty"`
	DefaultFixed32     *uint32                  `protobuf:"fixed32,87,opt,name=default_fixed32,json=defaultFixed32,def=87" json:"default_fixed32,omitempty"`
	DefaultFixed64     *uint64                  `protobuf:"fixed64,88,opt,name=default_fixed64,json=defaultFixed64,def=88" json:"default_fixed64,omitempty"`
	DefaultSfixed32    *int32                   `protobuf:"fixed32,89,opt,name=default_sfixed32,json=defaultSfixed32,def=89" json:"default_sfixed32,omitempty"`
	DefaultSfixed64    *int64                   `protobuf:"fixed64,80,opt,name=default_sfixed64,json=defaultSfixed64,def=-90" json:"default_sfixed64,omitempty"`
	DefaultFloat       *float32                 `protobuf:"fixed32,91,opt,name=default_float,json=defaultFloat,def=91.5" json:"default_float,omitempty"`
	DefaultDouble      *float64                 `protobuf:"fixed64,92,opt,name=default_double,json=defaultDouble,def=92000" json:"default_double,omitempty"`
	DefaultBool        *bool                    `protobuf:"varint,93,opt,name=default_bool,json=defaultBool,def=1" json:"default_bool,omitempty"`
	DefaultString      *string                  `protobuf:"bytes,94,opt,name=default_string,json=defaultString,def=hello" json:"default_string,omitempty"`
	DefaultBytes       []byte                   `protobuf:"bytes,95,opt,name=default_bytes,json=defaultBytes,def=world" json:"default_bytes,omitempty"`
	DefaultNestedEnum  *TestAllTypes_NestedEnum `protobuf:"varint,96,opt,name=default_nested_enum,json=defaultNestedEnum,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum,def=1" json:"default_nested_enum,omitempty"`
	DefaultForeignEnum *ForeignEnum             `protobuf:"varint,97,opt,name=default_foreign_enum,json=defaultForeignEnum,enum=goproto.proto.testeditions.ForeignEnum,def=5" json:"default_foreign_enum,omitempty"`
	// Types that are assignable to OneofField:
	//
	//	*TestAllTypes_OneofUint32
	//	*TestAllTypes_OneofNestedMessage
	//	*TestAllTypes_OneofString
	//	*TestAllTypes_OneofBytes
	//	*TestAllTypes_OneofBool
	//	*TestAllTypes_OneofUint64
	//	*TestAllTypes_OneofFloat
	//	*TestAllTypes_OneofDouble
	//	*TestAllTypes_OneofEnum
	//	*TestAllTypes_Oneofgroup
	OneofField isTestAllTypes_OneofField `protobuf_oneof:"oneof_field"`
	// A oneof with exactly one field.
	//
	// Types that are assignable to OneofOptional:
	//
	//	*TestAllTypes_OneofOptionalUint32
	OneofOptional isTestAllTypes_OneofOptional `protobuf_oneof:"oneof_optional"`
}

// Default values for TestAllTypes fields.
const (
	Default_TestAllTypes_DefaultInt32       = int32(81)
	Default_TestAllTypes_DefaultInt64       = int64(82)
	Default_TestAllTypes_DefaultUint32      = uint32(83)
	Default_TestAllTypes_DefaultUint64      = uint64(84)
	Default_TestAllTypes_DefaultSint32      = int32(-85)
	Default_TestAllTypes_DefaultSint64      = int64(86)
	Default_TestAllTypes_DefaultFixed32     = uint32(87)
	Default_TestAllTypes_DefaultFixed64     = uint64(88)
	Default_TestAllTypes_DefaultSfixed32    = int32(89)
	Default_TestAllTypes_DefaultSfixed64    = int64(-90)
	Default_TestAllTypes_DefaultFloat       = float32(91.5)
	Default_TestAllTypes_DefaultDouble      = float64(92000)
	Default_TestAllTypes_DefaultBool        = bool(true)
	Default_TestAllTypes_DefaultString      = string("hello")
	Default_TestAllTypes_DefaultNestedEnum  = TestAllTypes_BAR
	Default_TestAllTypes_DefaultForeignEnum = ForeignEnum_FOREIGN_BAR
)

// Default values for TestAllTypes fields.
var (
	Default_TestAllTypes_DefaultBytes = []byte("world")
)

func (x *TestAllTypes) Reset() {
	*x = TestAllTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes) ProtoMessage() {}

func (x *TestAllTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes.ProtoReflect.Descriptor instead.
func (*TestAllTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0}
}

func (x *TestAllTypes) GetSingularInt32() int32 {
	if x != nil {
		return x.SingularInt32
	}
	return 0
}

func (x *TestAllTypes) GetSingularInt64() int64 {
	if x != nil {
		return x.SingularInt64
	}
	return 0
}

func (x *TestAllTypes) GetSingularUint32() uint32 {
	if x != nil {
		return x.SingularUint32
	}
	return 0
}

func (x *TestAllTypes) GetSingularUint64() uint64 {
	if x != nil {
		return x.SingularUint64
	}
	return 0
}

func (x *TestAllTypes) GetSingularSint32() int32 {
	if x != nil {
		return x.SingularSint32
	}
	return 0
}

func (x *TestAllTypes) GetSingularSint64() int64 {
	if x != nil {
		return x.SingularSint64
	}
	return 0
}

func (x *TestAllTypes) GetSingularFixed32() uint32 {
	if x != nil {
		return x.SingularFixed32
	}
	return 0
}

func (x *TestAllTypes) GetSingularFixed64() uint64 {
	if x != nil {
		return x.SingularFixed64
	}
	return 0
}

func (x *TestAllTypes) GetSingularSfixed32() int32 {
	if x != nil {
		return x.SingularSfixed32
	}
	return 0
}

func (x *TestAllTypes) GetSingularSfixed64() int64 {
	if x != nil {
		return x.SingularSfixed64
	}
	return 0
}

func (x *TestAllTypes) GetSingularFloat() float32 {
	if x != nil {
		return x.SingularFloat
	}
	return 0
}

func (x *TestAllTypes) GetSingularDouble() float64 {
	if x != nil {
		return x.SingularDouble
	}
	return 0
}

func (x *TestAllTypes) GetSingularBool() bool {
	if x != nil {
		return x.SingularBool
	}
	return false
}

func (x *TestAllTypes) GetSingularString() string {
	if x != nil {
		return x.SingularString
	}
	return ""
}

func (x *TestAllTypes) GetSingularBytes() []byte {
	if x != nil {
		return x.SingularBytes
	}
	return nil
}

func (x *TestAllTypes) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

func (x *TestAllTypes) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

func (x *TestAllTypes) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *TestAllTypes) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *TestAllTypes) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *TestAllTypes) GetOptionalgroup() *TestAllTypes_OptionalGroup {
	if x != nil {
		return x.Optionalgroup
	}
	return nil
}

func (x *TestAllTypes) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalForeignMessage() *ForeignMessage {
	if x != nil {
		return x.OptionalForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalNestedEnum() TestAllTypes_NestedEnum {
	if x != nil && x.OptionalNestedEnum != nil {
		return *x.OptionalNestedEnum
	}
	return TestAllTypes_FOO
}

func (x *TestAllTypes) GetOptionalForeignEnum() ForeignEnum {
	if x != nil && x.OptionalForeignEnum != nil {
		return *x.OptionalForeignEnum
	}
	return ForeignEnum_FOREIGN_ZERO
}

func (x *TestAllTypes) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedgroup() []*TestAllTypes_RepeatedGroup {
	if x != nil {
		return x.Repeatedgroup
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedNestedMessage() []*TestAllTypes_NestedMessage {
	if x != nil {
		return x.RepeatedNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedForeignMessage() []*ForeignMessage {
	if x != nil {
		return x.RepeatedForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedNestedEnum() []TestAllTypes_NestedEnum {
	if x != nil {
		return x.RepeatedNestedEnum
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedForeignEnum() []ForeignEnum {
	if x != nil {
		return x.RepeatedForeignEnum
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Int32() map[int32]int32 {
	if x != nil {
		return x.MapInt32Int32
	}
	return nil
}

func (x *TestAllTypes) GetMapInt64Int64() map[int64]int64 {
	if x != nil {
		return x.MapInt64Int64
	}
	return nil
}

func (x *TestAllTypes) GetMapUint32Uint32() map[uint32]uint32 {
	if x != nil {
		return x.MapUint32Uint32
	}
	return nil
}

func (x *TestAllTypes) GetMapUint64Uint64() map[uint64]uint64 {
	if x != nil {
		return x.MapUint64Uint64
	}
	return nil
}

func (x *TestAllTypes) GetMapSint32Sint32() map[int32]int32 {
	if x != nil {
		return x.MapSint32Sint32
	}
	return nil
}

func (x *TestAllTypes) GetMapSint64Sint64() map[int64]int64 {
	if x != nil {
		return x.MapSint64Sint64
	}
	return nil
}

func (x *TestAllTypes) GetMapFixed32Fixed32() map[uint32]uint32 {
	if x != nil {
		return x.MapFixed32Fixed32
	}
	return nil
}

func (x *TestAllTypes) GetMapFixed64Fixed64() map[uint64]uint64 {
	if x != nil {
		return x.MapFixed64Fixed64
	}
	return nil
}

func (x *TestAllTypes) GetMapSfixed32Sfixed32() map[int32]int32 {
	if x != nil {
		return x.MapSfixed32Sfixed32
	}
	return nil
}

func (x *TestAllTypes) GetMapSfixed64Sfixed64() map[int64]int64 {
	if x != nil {
		return x.MapSfixed64Sfixed64
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Float() map[int32]float32 {
	if x != nil {
		return x.MapInt32Float
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Double() map[int32]float64 {
	if x != nil {
		return x.MapInt32Double
	}
	return nil
}

func (x *TestAllTypes) GetMapBoolBool() map[bool]bool {
	if x != nil {
		return x.MapBoolBool
	}
	return nil
}

func (x *TestAllTypes) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

func (x *TestAllTypes) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

func (x *TestAllTypes) GetMapStringNestedMessage() map[string]*TestAllTypes_NestedMessage {
	if x != nil {
		return x.MapStringNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetMapStringNestedEnum() map[string]TestAllTypes_NestedEnum {
	if x != nil {
		return x.MapStringNestedEnum
	}
	return nil
}

func (x *TestAllTypes) GetDefaultInt32() int32 {
	if x != nil && x.DefaultInt32 != nil {
		return *x.DefaultInt32
	}
	return Default_TestAllTypes_DefaultInt32
}

func (x *TestAllTypes) GetDefaultInt64() int64 {
	if x != nil && x.DefaultInt64 != nil {
		return *x.DefaultInt64
	}
	return Default_TestAllTypes_DefaultInt64
}

func (x *TestAllTypes) GetDefaultUint32() uint32 {
	if x != nil && x.DefaultUint32 != nil {
		return *x.DefaultUint32
	}
	return Default_TestAllTypes_DefaultUint32
}

func (x *TestAllTypes) GetDefaultUint64() uint64 {
	if x != nil && x.DefaultUint64 != nil {
		return *x.DefaultUint64
	}
	return Default_TestAllTypes_DefaultUint64
}

func (x *TestAllTypes) GetDefaultSint32() int32 {
	if x != nil && x.DefaultSint32 != nil {
		return *x.DefaultSint32
	}
	return Default_TestAllTypes_DefaultSint32
}

func (x *TestAllTypes) GetDefaultSint64() int64 {
	if x != nil && x.DefaultSint64 != nil {
		return *x.DefaultSint64
	}
	return Default_TestAllTypes_DefaultSint64
}

func (x *TestAllTypes) GetDefaultFixed32() uint32 {
	if x != nil && x.DefaultFixed32 != nil {
		return *x.DefaultFixed32
	}
	return Default_TestAllTypes_DefaultFixed32
}

func (x *TestAllTypes) GetDefaultFixed64() uint64 {
	if x != nil && x.DefaultFixed64 != nil {
		return *x.DefaultFixed64
	}
	return Default_TestAllTypes_DefaultFixed64
}

func (x *TestAllTypes) GetDefaultSfixed32() int32 {
	if x != nil && x.DefaultSfixed32 != nil {
		return *x.DefaultSfixed32
	}
	return Default_TestAllTypes_DefaultSfixed32
}

func (x *TestAllTypes) GetDefaultSfixed64() int64 {
	if x != nil && x.DefaultSfixed64 != nil {
		return *x.DefaultSfixed64
	}
	return Default_TestAllTypes_DefaultSfixed64
}

func (x *TestAllTypes) GetDefaultFloat() float32 {
	if x != nil && x.DefaultFloat != nil {
		return *x.DefaultFloat
	}
	return Default_TestAllTypes_DefaultFloat
}

func (x *TestAllTypes) GetDefaultDouble() float64 {
	if x != nil && x.DefaultDouble != nil {
		return *x.DefaultDouble
	}
	return Default_TestAllTypes_DefaultDouble
}

func (x *TestAllTypes) GetDefaultBool() bool {
	if x != nil && x.DefaultBool != nil {
		return *x.DefaultBool
	}
	return Default_TestAllTypes_DefaultBool
}

func (x *TestAllTypes) GetDefaultString() string {
	if x != nil && x.DefaultString != nil {
		return *x.DefaultString
	}
	return Default_TestAllTypes_DefaultString
}

func (x *TestAllTypes) GetDefaultBytes() []byte {
	if x != nil && x.DefaultBytes != nil {
		return x.DefaultBytes
	}
	return append([]byte(nil), Default_TestAllTypes_DefaultBytes...)
}

func (x *TestAllTypes) GetDefaultNestedEnum() TestAllTypes_NestedEnum {
	if x != nil && x.DefaultNestedEnum != nil {
		return *x.DefaultNestedEnum
	}
	return Default_TestAllTypes_DefaultNestedEnum
}

func (x *TestAllTypes) GetDefaultForeignEnum() ForeignEnum {
	if x != nil && x.DefaultForeignEnum != nil {
		return *x.DefaultForeignEnum
	}
	return Default_TestAllTypes_DefaultForeignEnum
}

func (m *TestAllTypes) GetOneofField() isTestAllTypes_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestAllTypes) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *TestAllTypes) GetOneofNestedMessage() *TestAllTypes_NestedMessage {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofNestedMessage); ok {
		return x.OneofNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetOneofString() string {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *TestAllTypes) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *TestAllTypes) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *TestAllTypes) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *TestAllTypes) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *TestAllTypes) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *TestAllTypes) GetOneofEnum() TestAllTypes_NestedEnum {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofEnum); ok {
		return x.OneofEnum
	}
	return TestAllTypes_FOO
}

func (x *TestAllTypes) GetOneofgroup() *TestAllTypes_OneofGroup {
	if x, ok := x.GetOneofField().(*TestAllTypes_Oneofgroup); ok {
		return x.Oneofgroup
	}
	return nil
}

func (m *TestAllTypes) GetOneofOptional() isTestAllTypes_OneofOptional {
	if m != nil {
		return m.OneofOptional
	}
	return nil
}

func (x *TestAllTypes) GetOneofOptionalUint32() uint32 {
	if x, ok := x.GetOneofOptional().(*TestAllTypes_OneofOptionalUint32); ok {
		return x.OneofOptionalUint32
	}
	return 0
}

type isTestAllTypes_OneofField interface {
	isTestAllTypes_OneofField()
}

type TestAllTypes_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,111,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type TestAllTypes_OneofNestedMessage struct {
	OneofNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,112,opt,name=oneof_nested_message,json=oneofNestedMessage,oneof"`
}

type TestAllTypes_OneofString struct {
	OneofString string `protobuf:"bytes,113,opt,name=oneof_string,json=oneofString,oneof"`
}

type TestAllTypes_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,114,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type TestAllTypes_OneofBool struct {
	OneofBool bool `protobuf:"varint,115,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type TestAllTypes_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,116,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type TestAllTypes_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,117,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type TestAllTypes_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,118,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type TestAllTypes_OneofEnum struct {
	OneofEnum TestAllTypes_NestedEnum `protobuf:"varint,119,opt,name=oneof_enum,json=oneofEnum,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum,oneof"`
}

type TestAllTypes_Oneofgroup struct {
	Oneofgroup *TestAllTypes_OneofGroup `protobuf:"group,121,opt,name=OneofGroup,json=oneofgroup,oneof"`
}

func (*TestAllTypes_OneofUint32) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofNestedMessage) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofString) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofBytes) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofBool) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofUint64) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofFloat) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofDouble) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofEnum) isTestAllTypes_OneofField() {}

func (*TestAllTypes_Oneofgroup) isTestAllTypes_OneofField() {}

type isTestAllTypes_OneofOptional interface {
	isTestAllTypes_OneofOptional()
}

type TestAllTypes_OneofOptionalUint32 struct {
	OneofOptionalUint32 uint32 `protobuf:"varint,120,opt,name=oneof_optional_uint32,json=oneofOptionalUint32,oneof"`
}

func (*TestAllTypes_OneofOptionalUint32) isTestAllTypes_OneofOptional() {}

type ForeignMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"varint,1,opt,name=c" json:"c,omitempty"`
	D *int32 `protobuf:"varint,2,opt,name=d" json:"d,omitempty"`
}

func (x *ForeignMessage) Reset() {
	*x = ForeignMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeignMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeignMessage) ProtoMessage() {}

func (x *ForeignMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeignMessage.ProtoReflect.Descriptor instead.
func (*ForeignMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{1}
}

func (x *ForeignMessage) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

func (x *ForeignMessage) GetD() int32 {
	if x != nil && x.D != nil {
		return *x.D
	}
	return 0
}

type TestRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequiredField *int32 `protobuf:"varint,1,req,name=required_field,json=requiredField" json:"required_field,omitempty"`
}

func (x *TestRequired) Reset() {
	*x = TestRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequired) ProtoMessage() {}

func (x *TestRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequired.ProtoReflect.Descriptor instead.
func (*TestRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{2}
}

func (x *TestRequired) GetRequiredField() int32 {
	if x != nil && x.RequiredField != nil {
		return *x.RequiredField
	}
	return 0
}

type TestRequiredForeign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionalMessage *TestRequired           `protobuf:"bytes,1,opt,name=optional_message,json=optionalMessage" json:"optional_message,omitempty"`
	RepeatedMessage []*TestRequired         `protobuf:"bytes,2,rep,name=repeated_message,json=repeatedMessage" json:"repeated_message,omitempty"`
	MapMessage      map[int32]*TestRequired `protobuf:"bytes,3,rep,name=map_message,json=mapMessage" json:"map_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Types that are assignable to OneofField:
	//
	//	*TestRequiredForeign_OneofMessage
	OneofField isTestRequiredForeign_OneofField `protobuf_oneof:"oneof_field"`
}

func (x *TestRequiredForeign) Reset() {
	*x = TestRequiredForeign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredForeign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredForeign) ProtoMessage() {}

func (x *TestRequiredForeign) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredForeign.ProtoReflect.Descriptor instead.
func (*TestRequiredForeign) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{3}
}

func (x *TestRequiredForeign) GetOptionalMessage() *TestRequired {
	if x != nil {
		return x.OptionalMessage
	}
	return nil
}

func (x *TestRequiredForeign) GetRepeatedMessage() []*TestRequired {
	if x != nil {
		return x.RepeatedMessage
	}
	return nil
}

func (x *TestRequiredForeign) GetMapMessage() map[int32]*TestRequired {
	if x != nil {
		return x.MapMessage
	}
	return nil
}

func (m *TestRequiredForeign) GetOneofField() isTestRequiredForeign_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestRequiredForeign) GetOneofMessage() *TestRequired {
	if x, ok := x.GetOneofField().(*TestRequiredForeign_OneofMessage); ok {
		return x.OneofMessage
	}
	return nil
}

type isTestRequiredForeign_OneofField interface {
	isTestRequiredForeign_OneofField()
}

type TestRequiredForeign_OneofMessage struct {
	OneofMessage *TestRequired `protobuf:"bytes,4,opt,name=oneof_message,json=oneofMessage,oneof"`
}

func (*TestRequiredForeign_OneofMessage) isTestRequiredForeign_OneofField() {}

type TestRequiredGroupFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Optionalgroup *TestRequiredGroupFields_OptionalGroup   `protobuf:"group,1,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	Repeatedgroup []*TestRequiredGroupFields_RepeatedGroup `protobuf:"group,3,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
}

func (x *TestRequiredGroupFields) Reset() {
	*x = TestRequiredGroupFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredGroupFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredGroupFields) ProtoMessage() {}

func (x *TestRequiredGroupFields) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredGroupFields.ProtoReflect.Descriptor instead.
func (*TestRequiredGroupFields) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{4}
}

func (x *TestRequiredGroupFields) GetOptionalgroup() *TestRequiredGroupFields_OptionalGroup {
	if x != nil {
		return x.Optionalgroup
	}
	return nil
}

func (x *TestRequiredGroupFields) GetRepeatedgroup() []*TestRequiredGroupFields_RepeatedGroup {
	if x != nil {
		return x.Repeatedgroup
	}
	return nil
}

type TestPackedTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackedInt32    []int32       `protobuf:"varint,90,rep,packed,name=packed_int32,json=packedInt32" json:"packed_int32,omitempty"`
	PackedInt64    []int64       `protobuf:"varint,91,rep,packed,name=packed_int64,json=packedInt64" json:"packed_int64,omitempty"`
	PackedUint32   []uint32      `protobuf:"varint,92,rep,packed,name=packed_uint32,json=packedUint32" json:"packed_uint32,omitempty"`
	PackedUint64   []uint64      `protobuf:"varint,93,rep,packed,name=packed_uint64,json=packedUint64" json:"packed_uint64,omitempty"`
	PackedSint32   []int32       `protobuf:"zigzag32,94,rep,packed,name=packed_sint32,json=packedSint32" json:"packed_sint32,omitempty"`
	PackedSint64   []int64       `protobuf:"zigzag64,95,rep,packed,name=packed_sint64,json=packedSint64" json:"packed_sint64,omitempty"`
	PackedFixed32  []uint32      `protobuf:"fixed32,96,rep,packed,name=packed_fixed32,json=packedFixed32" json:"packed_fixed32,omitempty"`
	PackedFixed64  []uint64      `protobuf:"fixed64,97,rep,packed,name=packed_fixed64,json=packedFixed64" json:"packed_fixed64,omitempty"`
	PackedSfixed32 []int32       `protobuf:"fixed32,98,rep,packed,name=packed_sfixed32,json=packedSfixed32" json:"packed_sfixed32,omitempty"`
	PackedSfixed64 []int64       `protobuf:"fixed64,99,rep,packed,name=packed_sfixed64,json=packedSfixed64" json:"packed_sfixed64,omitempty"`
	PackedFloat    []float32     `protobuf:"fixed32,100,rep,packed,name=packed_float,json=packedFloat" json:"packed_float,omitempty"`
	PackedDouble   []float64     `protobuf:"fixed64,101,rep,packed,name=packed_double,json=packedDouble" json:"packed_double,omitempty"`
	PackedBool     []bool        `protobuf:"varint,102,rep,packed,name=packed_bool,json=packedBool" json:"packed_bool,omitempty"`
	PackedEnum     []ForeignEnum `protobuf:"varint,103,rep,packed,name=packed_enum,json=packedEnum,enum=goproto.proto.testeditions.ForeignEnum" json:"packed_enum,omitempty"`
}

func (x *TestPackedTypes) Reset() {
	*x = TestPackedTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestPackedTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestPackedTypes) ProtoMessage() {}

func (x *TestPackedTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestPackedTypes.ProtoReflect.Descriptor instead.
func (*TestPackedTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{5}
}

func (x *TestPackedTypes) GetPackedInt32() []int32 {
	if x != nil {
		return x.PackedInt32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedInt64() []int64 {
	if x != nil {
		return x.PackedInt64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedUint32() []uint32 {
	if x != nil {
		return x.PackedUint32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedUint64() []uint64 {
	if x != nil {
		return x.PackedUint64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSint32() []int32 {
	if x != nil {
		return x.PackedSint32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSint64() []int64 {
	if x != nil {
		return x.PackedSint64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedFixed32() []uint32 {
	if x != nil {
		return x.PackedFixed32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedFixed64() []uint64 {
	if x != nil {
		return x.PackedFixed64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSfixed32() []int32 {
	if x != nil {
		return x.PackedSfixed32
	}
	return nil
}

func (x *TestPackedTypes) GetPackedSfixed64() []int64 {
	if x != nil {
		return x.PackedSfixed64
	}
	return nil
}

func (x *TestPackedTypes) GetPackedFloat() []float32 {
	if x != nil {
		return x.PackedFloat
	}
	return nil
}

func (x *TestPackedTypes) GetPackedDouble() []float64 {
	if x != nil {
		return x.PackedDouble
	}
	return nil
}

func (x *TestPackedTypes) GetPackedBool() []bool {
	if x != nil {
		return x.PackedBool
	}
	return nil
}

func (x *TestPackedTypes) GetPackedEnum() []ForeignEnum {
	if x != nil {
		return x.PackedEnum
	}
	return nil
}

type TestPackedExtensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestPackedExtensions) Reset() {
	*x = TestPackedExtensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestPackedExtensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestPackedExtensions) ProtoMessage() {}

func (x *TestPackedExtensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestPackedExtensions.ProtoReflect.Descriptor instead.
func (*TestPackedExtensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{6}
}

type TestAllTypes_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           *int32        `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllTypes `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllTypes_NestedMessage) Reset() {
	*x = TestAllTypes_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_NestedMessage) ProtoMessage() {}

func (x *TestAllTypes_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllTypes_NestedMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllTypes_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_NestedMessage) GetCorecursive() *TestAllTypes {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

type TestAllTypes_OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                      `protobuf:"varint,17,opt,name=a" json:"a,omitempty"`
	OptionalNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,1000,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	SameFieldNumber       *int32                      `protobuf:"varint,16,opt,name=same_field_number,json=sameFieldNumber" json:"same_field_number,omitempty"`
}

func (x *TestAllTypes_OptionalGroup) Reset() {
	*x = TestAllTypes_OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_OptionalGroup) ProtoMessage() {}

func (x *TestAllTypes_OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_OptionalGroup.ProtoReflect.Descriptor instead.
func (*TestAllTypes_OptionalGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0, 1}
}

func (x *TestAllTypes_OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_OptionalGroup) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypes_OptionalGroup) GetSameFieldNumber() int32 {
	if x != nil && x.SameFieldNumber != nil {
		return *x.SameFieldNumber
	}
	return 0
}

type TestAllTypes_RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                      `protobuf:"varint,47,opt,name=a" json:"a,omitempty"`
	OptionalNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,1001,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
}

func (x *TestAllTypes_RepeatedGroup) Reset() {
	*x = TestAllTypes_RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_RepeatedGroup) ProtoMessage() {}

func (x *TestAllTypes_RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_RepeatedGroup.ProtoReflect.Descriptor instead.
func (*TestAllTypes_RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0, 2}
}

func (x *TestAllTypes_RepeatedGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_RepeatedGroup) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

type TestAllTypes_OneofGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	B *int32 `protobuf:"varint,2,opt,name=b" json:"b,omitempty"`
}

func (x *TestAllTypes_OneofGroup) Reset() {
	*x = TestAllTypes_OneofGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_OneofGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_OneofGroup) ProtoMessage() {}

func (x *TestAllTypes_OneofGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_OneofGroup.ProtoReflect.Descriptor instead.
func (*TestAllTypes_OneofGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{0, 20}
}

func (x *TestAllTypes_OneofGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypes_OneofGroup) GetB() int32 {
	if x != nil && x.B != nil {
		return *x.B
	}
	return 0
}

type TestRequiredGroupFields_OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,2,req,name=a" json:"a,omitempty"`
}

func (x *TestRequiredGroupFields_OptionalGroup) Reset() {
	*x = TestRequiredGroupFields_OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredGroupFields_OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredGroupFields_OptionalGroup) ProtoMessage() {}

func (x *TestRequiredGroupFields_OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredGroupFields_OptionalGroup.ProtoReflect.Descriptor instead.
func (*TestRequiredGroupFields_OptionalGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{4, 0}
}

func (x *TestRequiredGroupFields_OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

type TestRequiredGroupFields_RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,4,req,name=a" json:"a,omitempty"`
}

func (x *TestRequiredGroupFields_RepeatedGroup) Reset() {
	*x = TestRequiredGroupFields_RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequiredGroupFields_RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequiredGroupFields_RepeatedGroup) ProtoMessage() {}

func (x *TestRequiredGroupFields_RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequiredGroupFields_RepeatedGroup.ProtoReflect.Descriptor instead.
func (*TestRequiredGroupFields_RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_proto_rawDescGZIP(), []int{4, 1}
}

func (x *TestRequiredGroupFields_RepeatedGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

var file_internal_testprotos_testeditions_test_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         90,
		Name:          "goproto.proto.testeditions.packed_int32",
		Tag:           "varint,90,rep,name=packed_int32",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         91,
		Name:          "goproto.proto.testeditions.packed_int64",
		Tag:           "varint,91,rep,name=packed_int64",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         92,
		Name:          "goproto.proto.testeditions.packed_uint32",
		Tag:           "varint,92,rep,name=packed_uint32",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         93,
		Name:          "goproto.proto.testeditions.packed_uint64",
		Tag:           "varint,93,rep,name=packed_uint64",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         94,
		Name:          "goproto.proto.testeditions.packed_sint32",
		Tag:           "zigzag32,94,rep,name=packed_sint32",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         95,
		Name:          "goproto.proto.testeditions.packed_sint64",
		Tag:           "zigzag64,95,rep,name=packed_sint64",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         96,
		Name:          "goproto.proto.testeditions.packed_fixed32",
		Tag:           "fixed32,96,rep,name=packed_fixed32",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         97,
		Name:          "goproto.proto.testeditions.packed_fixed64",
		Tag:           "fixed64,97,rep,name=packed_fixed64",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         98,
		Name:          "goproto.proto.testeditions.packed_sfixed32",
		Tag:           "fixed32,98,rep,name=packed_sfixed32",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         99,
		Name:          "goproto.proto.testeditions.packed_sfixed64",
		Tag:           "fixed64,99,rep,name=packed_sfixed64",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         100,
		Name:          "goproto.proto.testeditions.packed_float",
		Tag:           "fixed32,100,rep,name=packed_float",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         101,
		Name:          "goproto.proto.testeditions.packed_double",
		Tag:           "fixed64,101,rep,name=packed_double",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         102,
		Name:          "goproto.proto.testeditions.packed_bool",
		Tag:           "varint,102,rep,name=packed_bool",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
	{
		ExtendedType:  (*TestPackedExtensions)(nil),
		ExtensionType: ([]ForeignEnum)(nil),
		Field:         103,
		Name:          "goproto.proto.testeditions.packed_enum",
		Tag:           "varint,103,rep,name=packed_enum,enum=goproto.proto.testeditions.ForeignEnum",
		Filename:      "internal/testprotos/testeditions/test.proto",
	},
}

// Extension fields to TestPackedExtensions.
var (
	// repeated int32 packed_int32 = 90;
	E_PackedInt32 = &file_internal_testprotos_testeditions_test_proto_extTypes[0]
	// repeated int64 packed_int64 = 91;
	E_PackedInt64 = &file_internal_testprotos_testeditions_test_proto_extTypes[1]
	// repeated uint32 packed_uint32 = 92;
	E_PackedUint32 = &file_internal_testprotos_testeditions_test_proto_extTypes[2]
	// repeated uint64 packed_uint64 = 93;
	E_PackedUint64 = &file_internal_testprotos_testeditions_test_proto_extTypes[3]
	// repeated sint32 packed_sint32 = 94;
	E_PackedSint32 = &file_internal_testprotos_testeditions_test_proto_extTypes[4]
	// repeated sint64 packed_sint64 = 95;
	E_PackedSint64 = &file_internal_testprotos_testeditions_test_proto_extTypes[5]
	// repeated fixed32 packed_fixed32 = 96;
	E_PackedFixed32 = &file_internal_testprotos_testeditions_test_proto_extTypes[6]
	// repeated fixed64 packed_fixed64 = 97;
	E_PackedFixed64 = &file_internal_testprotos_testeditions_test_proto_extTypes[7]
	// repeated sfixed32 packed_sfixed32 = 98;
	E_PackedSfixed32 = &file_internal_testprotos_testeditions_test_proto_extTypes[8]
	// repeated sfixed64 packed_sfixed64 = 99;
	E_PackedSfixed64 = &file_internal_testprotos_testeditions_test_proto_extTypes[9]
	// repeated float packed_float = 100;
	E_PackedFloat = &file_internal_testprotos_testeditions_test_proto_extTypes[10]
	// repeated double packed_double = 101;
	E_PackedDouble = &file_internal_testprotos_testeditions_test_proto_extTypes[11]
	// repeated bool packed_bool = 102;
	E_PackedBool = &file_internal_testprotos_testeditions_test_proto_extTypes[12]
	// repeated goproto.proto.testeditions.ForeignEnum packed_enum = 103;
	E_PackedEnum = &file_internal_testprotos_testeditions_test_proto_extTypes[13]
)

var File_internal_testprotos_testeditions_test_proto protoreflect.FileDescriptor

var file_internal_testprotos_testeditions_test_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xe8, 0x3f, 0x0a, 0x0c, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0e, 0x73, 0x69,
	0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x7c, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x73, 0x69, 0x6e, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x7d, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x7e, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x7f, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2f, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x80, 0x01, 0x20, 0x01, 0x28, 0x11,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2f, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x81, 0x01, 0x20, 0x01, 0x28,
	0x12, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x31, 0x0a, 0x10, 0x73, 0x69, 0x6e, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x82, 0x01, 0x20,
	0x01, 0x28, 0x07, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0f, 0x73, 0x69, 0x6e, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x31, 0x0a, 0x10, 0x73,
	0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18,
	0x83, 0x01, 0x20, 0x01, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0f, 0x73,
	0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x33,
	0x0a, 0x11, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x18, 0x84, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08,
	0x02, 0x52, 0x10, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x85, 0x01, 0x20, 0x01, 0x28, 0x10, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x10, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d, 0x0a, 0x0e, 0x73, 0x69, 0x6e, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x86, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2f, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x87, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x0d, 0x73, 0x69, 0x6e, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x88, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0c, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x89, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x8a, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x02, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27,
	0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a,
	0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x63, 0x0a, 0x0d,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x6e, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x64, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x65, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x5b,
	0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x1f, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x20, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x21, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x23,
	0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x24, 0x20, 0x03, 0x28, 0x12, 0x52, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a,
	0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x25, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x26, 0x20, 0x03,
	0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18,
	0x29, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x2b,
	0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f,
	0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x2d, 0x20,
	0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x65, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x2e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x42, 0x07, 0xaa, 0x01, 0x04, 0x18, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x6e, 0x0a, 0x17, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x64, 0x0a, 0x18, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x31, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x65, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x33, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x5b, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x34, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x63, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x38, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x63, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x39, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x69, 0x0a,
	0x11, 0x6d, 0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x3a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x69, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61,
	0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x69, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d,
	0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x69,
	0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x6f, 0x0a, 0x13, 0x6d, 0x61, 0x70,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x18, 0x3e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x6f, 0x0a, 0x13, 0x6d, 0x61,
	0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0x3f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x75, 0x0a, 0x15, 0x6d,
	0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x18, 0x40, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d,
	0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x75, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x41, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x63, 0x0a, 0x0f, 0x6d, 0x61, 0x70,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x42, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x66,
	0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x5d, 0x0a, 0x0d, 0x6d, 0x61, 0x70, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x44, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42,
	0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6f,
	0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x69, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x45, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x66, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x46, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x7f, 0x0a, 0x19, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x47, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x16, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x76, 0x0a, 0x16, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x49, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x51, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x02, 0x38, 0x31, 0x52, 0x0c, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x52, 0x20, 0x01, 0x28,
	0x03, 0x3a, 0x02, 0x38, 0x32, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x02, 0x38, 0x33, 0x52,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x29,
	0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x54, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x02, 0x38, 0x34, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a, 0x0e, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x55, 0x20, 0x01, 0x28,
	0x11, 0x3a, 0x03, 0x2d, 0x38, 0x35, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x56, 0x20, 0x01, 0x28, 0x12, 0x3a, 0x02, 0x38,
	0x36, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x2b, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x18, 0x57, 0x20, 0x01, 0x28, 0x07, 0x3a, 0x02, 0x38, 0x37, 0x52, 0x0e, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a,
	0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x18, 0x58, 0x20, 0x01, 0x28, 0x06, 0x3a, 0x02, 0x38, 0x38, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d, 0x0a, 0x10, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x59,
	0x20, 0x01, 0x28, 0x0f, 0x3a, 0x02, 0x38, 0x39, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x10, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x50, 0x20,
	0x01, 0x28, 0x10, 0x3a, 0x03, 0x2d, 0x39, 0x30, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x02,
	0x3a, 0x04, 0x39, 0x31, 0x2e, 0x35, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x05, 0x39, 0x32,
	0x30, 0x30, 0x30, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x0b,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x2c, 0x0a, 0x0e, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x5e, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x05, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x0c,
	0x3a, 0x05, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x13, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x60, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x03, 0x42, 0x41, 0x52, 0x52, 0x11, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x66, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x61, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f,
	0x42, 0x41, 0x52, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52,
	0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x6a, 0x0a, 0x14,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x48, 0x00, 0x52, 0x12, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x71, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a,
	0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x72, 0x20, 0x01,
	0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x1f, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x73,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42, 0x6f, 0x6f,
	0x6c, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x74, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x75, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x76, 0x20, 0x01, 0x28, 0x01, 0x48,
	0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x54,
	0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x77, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x45, 0x6e, 0x75, 0x6d, 0x12, 0x5c, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x79, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x28, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x34, 0x0a, 0x15, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x78, 0x20, 0x01, 0x28,
	0x0d, 0x48, 0x01, 0x52, 0x13, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x1a, 0x69, 0x0a, 0x0d, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x4a, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73,
	0x69, 0x76, 0x65, 0x1a, 0xba, 0x01, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x61, 0x12, 0x6f, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe8,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x73, 0x61, 0x6d, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x1a, 0x8e, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61,
	0x12, 0x6f, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe9, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a,
	0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x11, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x07,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d,
	0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x06, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x10, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x10, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f,
	0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x81, 0x01,
	0x0a, 0x1b, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x4c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x7b, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x28,
	0x0a, 0x0a, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x0c, 0x0a, 0x01, 0x62, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x62, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x42, 0x41, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x45, 0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x22, 0x2c, 0x0a, 0x0e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x63, 0x12, 0x0c, 0x0a, 0x01, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x64, 0x22, 0x3c, 0x0a, 0x0c, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08,
	0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x22, 0xea, 0x03, 0x0a, 0x13, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x12, 0x53, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a,
	0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x60, 0x0a, 0x0b, 0x6d, 0x61, 0x70, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x2e, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6d, 0x61, 0x70, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x67, 0x0a, 0x0f, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x22, 0xc5, 0x02,
	0x0a, 0x17, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x6e, 0x0a, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x6e, 0x0a, 0x0d, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x24, 0x0a, 0x0d, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x13, 0x0a, 0x01, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x01, 0x61, 0x1a,
	0x24, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x13, 0x0a, 0x01, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x01, 0x61, 0x22, 0xa0, 0x05, 0x0a, 0x0f, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5a, 0x20, 0x03, 0x28, 0x05, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5c,
	0x20, 0x03, 0x28, 0x0d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x04,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x11, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x12, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2c, 0x0a,
	0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0x60, 0x20, 0x03, 0x28, 0x07, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x61, 0x20,
	0x03, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x62, 0x20, 0x03,
	0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x63, 0x20, 0x03,
	0x28, 0x10, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x64, 0x20, 0x03, 0x28, 0x02, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x18, 0x65, 0x20, 0x03, 0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x26, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x66,
	0x20, 0x03, 0x28, 0x08, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x4f, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x67, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x22, 0x20, 0x0a, 0x14, 0x54, 0x65, 0x73, 0x74,
	0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2a, 0x08, 0x08, 0x01, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x2a, 0x52, 0x0a, 0x0b, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x4f, 0x52,
	0x45, 0x49, 0x47, 0x4e, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46,
	0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x4f, 0x4f, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b,
	0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x05, 0x12, 0x0f, 0x0a,
	0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x5a, 0x10, 0x06, 0x3a, 0x5a,
	0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x30,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x5a, 0x20, 0x03, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x5a, 0x0a, 0x0c, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5b, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x5c, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x0d, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x3a, 0x5c, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x04, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x3a, 0x5c, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x11, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x3a, 0x5c, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x12, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x5e,
	0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x60, 0x20, 0x03, 0x28, 0x07, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x5e,
	0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x61, 0x20, 0x03, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x60,
	0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x62, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x3a, 0x60, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x63, 0x20, 0x03, 0x28, 0x10, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x18, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x3a, 0x5a, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x64, 0x20, 0x03, 0x28, 0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x5c,
	0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x65, 0x20, 0x03, 0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x58, 0x0a, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x30, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x66, 0x20,
	0x03, 0x28, 0x08, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x81, 0x01, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x67, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_internal_testprotos_testeditions_test_proto_rawDescOnce sync.Once
	file_internal_testprotos_testeditions_test_proto_rawDescData = file_internal_testprotos_testeditions_test_proto_rawDesc
)

func file_internal_testprotos_testeditions_test_proto_rawDescGZIP() []byte {
	file_internal_testprotos_testeditions_test_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_testeditions_test_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_testeditions_test_proto_rawDescData)
	})
	return file_internal_testprotos_testeditions_test_proto_rawDescData
}

var file_internal_testprotos_testeditions_test_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_internal_testprotos_testeditions_test_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_internal_testprotos_testeditions_test_proto_goTypes = []interface{}{
	(ForeignEnum)(0),                   // 0: goproto.proto.testeditions.ForeignEnum
	(TestAllTypes_NestedEnum)(0),       // 1: goproto.proto.testeditions.TestAllTypes.NestedEnum
	(*TestAllTypes)(nil),               // 2: goproto.proto.testeditions.TestAllTypes
	(*ForeignMessage)(nil),             // 3: goproto.proto.testeditions.ForeignMessage
	(*TestRequired)(nil),               // 4: goproto.proto.testeditions.TestRequired
	(*TestRequiredForeign)(nil),        // 5: goproto.proto.testeditions.TestRequiredForeign
	(*TestRequiredGroupFields)(nil),    // 6: goproto.proto.testeditions.TestRequiredGroupFields
	(*TestPackedTypes)(nil),            // 7: goproto.proto.testeditions.TestPackedTypes
	(*TestPackedExtensions)(nil),       // 8: goproto.proto.testeditions.TestPackedExtensions
	(*TestAllTypes_NestedMessage)(nil), // 9: goproto.proto.testeditions.TestAllTypes.NestedMessage
	(*TestAllTypes_OptionalGroup)(nil), // 10: goproto.proto.testeditions.TestAllTypes.OptionalGroup
	(*TestAllTypes_RepeatedGroup)(nil), // 11: goproto.proto.testeditions.TestAllTypes.RepeatedGroup
	nil,                                // 12: goproto.proto.testeditions.TestAllTypes.MapInt32Int32Entry
	nil,                                // 13: goproto.proto.testeditions.TestAllTypes.MapInt64Int64Entry
	nil,                                // 14: goproto.proto.testeditions.TestAllTypes.MapUint32Uint32Entry
	nil,                                // 15: goproto.proto.testeditions.TestAllTypes.MapUint64Uint64Entry
	nil,                                // 16: goproto.proto.testeditions.TestAllTypes.MapSint32Sint32Entry
	nil,                                // 17: goproto.proto.testeditions.TestAllTypes.MapSint64Sint64Entry
	nil,                                // 18: goproto.proto.testeditions.TestAllTypes.MapFixed32Fixed32Entry
	nil,                                // 19: goproto.proto.testeditions.TestAllTypes.MapFixed64Fixed64Entry
	nil,                                // 20: goproto.proto.testeditions.TestAllTypes.MapSfixed32Sfixed32Entry
	nil,                                // 21: goproto.proto.testeditions.TestAllTypes.MapSfixed64Sfixed64Entry
	nil,                                // 22: goproto.proto.testeditions.TestAllTypes.MapInt32FloatEntry
	nil,                                // 23: goproto.proto.testeditions.TestAllTypes.MapInt32DoubleEntry
	nil,                                // 24: goproto.proto.testeditions.TestAllTypes.MapBoolBoolEntry
	nil,                                // 25: goproto.proto.testeditions.TestAllTypes.MapStringStringEntry
	nil,                                // 26: goproto.proto.testeditions.TestAllTypes.MapStringBytesEntry
	nil,                                // 27: goproto.proto.testeditions.TestAllTypes.MapStringNestedMessageEntry
	nil,                                // 28: goproto.proto.testeditions.TestAllTypes.MapStringNestedEnumEntry
	(*TestAllTypes_OneofGroup)(nil),    // 29: goproto.proto.testeditions.TestAllTypes.OneofGroup
	nil,                                // 30: goproto.proto.testeditions.TestRequiredForeign.MapMessageEntry
	(*TestRequiredGroupFields_OptionalGroup)(nil), // 31: goproto.proto.testeditions.TestRequiredGroupFields.OptionalGroup
	(*TestRequiredGroupFields_RepeatedGroup)(nil), // 32: goproto.proto.testeditions.TestRequiredGroupFields.RepeatedGroup
}
var file_internal_testprotos_testeditions_test_proto_depIdxs = []int32{
	10, // 0: goproto.proto.testeditions.TestAllTypes.optionalgroup:type_name -> goproto.proto.testeditions.TestAllTypes.OptionalGroup
	9,  // 1: goproto.proto.testeditions.TestAllTypes.optional_nested_message:type_name -> goproto.proto.testeditions.TestAllTypes.NestedMessage
	3,  // 2: goproto.proto.testeditions.TestAllTypes.optional_foreign_message:type_name -> goproto.proto.testeditions.ForeignMessage
	1,  // 3: goproto.proto.testeditions.TestAllTypes.optional_nested_enum:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	0,  // 4: goproto.proto.testeditions.TestAllTypes.optional_foreign_enum:type_name -> goproto.proto.testeditions.ForeignEnum
	11, // 5: goproto.proto.testeditions.TestAllTypes.repeatedgroup:type_name -> goproto.proto.testeditions.TestAllTypes.RepeatedGroup
	9,  // 6: goproto.proto.testeditions.TestAllTypes.repeated_nested_message:type_name -> goproto.proto.testeditions.TestAllTypes.NestedMessage
	3,  // 7: goproto.proto.testeditions.TestAllTypes.repeated_foreign_message:type_name -> goproto.proto.testeditions.ForeignMessage
	1,  // 8: goproto.proto.testeditions.TestAllTypes.repeated_nested_enum:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	0,  // 9: goproto.proto.testeditions.TestAllTypes.repeated_foreign_enum:type_name -> goproto.proto.testeditions.ForeignEnum
	12, // 10: goproto.proto.testeditions.TestAllTypes.map_int32_int32:type_name -> goproto.proto.testeditions.TestAllTypes.MapInt32Int32Entry
	13, // 11: goproto.proto.testeditions.TestAllTypes.map_int64_int64:type_name -> goproto.proto.testeditions.TestAllTypes.MapInt64Int64Entry
	14, // 12: goproto.proto.testeditions.TestAllTypes.map_uint32_uint32:type_name -> goproto.proto.testeditions.TestAllTypes.MapUint32Uint32Entry
	15, // 13: goproto.proto.testeditions.TestAllTypes.map_uint64_uint64:type_name -> goproto.proto.testeditions.TestAllTypes.MapUint64Uint64Entry
	16, // 14: goproto.proto.testeditions.TestAllTypes.map_sint32_sint32:type_name -> goproto.proto.testeditions.TestAllTypes.MapSint32Sint32Entry
	17, // 15: goproto.proto.testeditions.TestAllTypes.map_sint64_sint64:type_name -> goproto.proto.testeditions.TestAllTypes.MapSint64Sint64Entry
	18, // 16: goproto.proto.testeditions.TestAllTypes.map_fixed32_fixed32:type_name -> goproto.proto.testeditions.TestAllTypes.MapFixed32Fixed32Entry
	19, // 17: goproto.proto.testeditions.TestAllTypes.map_fixed64_fixed64:type_name -> goproto.proto.testeditions.TestAllTypes.MapFixed64Fixed64Entry
	20, // 18: goproto.proto.testeditions.TestAllTypes.map_sfixed32_sfixed32:type_name -> goproto.proto.testeditions.TestAllTypes.MapSfixed32Sfixed32Entry
	21, // 19: goproto.proto.testeditions.TestAllTypes.map_sfixed64_sfixed64:type_name -> goproto.proto.testeditions.TestAllTypes.MapSfixed64Sfixed64Entry
	22, // 20: goproto.proto.testeditions.TestAllTypes.map_int32_float:type_name -> goproto.proto.testeditions.TestAllTypes.MapInt32FloatEntry
	23, // 21: goproto.proto.testeditions.TestAllTypes.map_int32_double:type_name -> goproto.proto.testeditions.TestAllTypes.MapInt32DoubleEntry
	24, // 22: goproto.proto.testeditions.TestAllTypes.map_bool_bool:type_name -> goproto.proto.testeditions.TestAllTypes.MapBoolBoolEntry
	25, // 23: goproto.proto.testeditions.TestAllTypes.map_string_string:type_name -> goproto.proto.testeditions.TestAllTypes.MapStringStringEntry
	26, // 24: goproto.proto.testeditions.TestAllTypes.map_string_bytes:type_name -> goproto.proto.testeditions.TestAllTypes.MapStringBytesEntry
	27, // 25: goproto.proto.testeditions.TestAllTypes.map_string_nested_message:type_name -> goproto.proto.testeditions.TestAllTypes.MapStringNestedMessageEntry
	28, // 26: goproto.proto.testeditions.TestAllTypes.map_string_nested_enum:type_name -> goproto.proto.testeditions.TestAllTypes.MapStringNestedEnumEntry
	1,  // 27: goproto.proto.testeditions.TestAllTypes.default_nested_enum:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	0,  // 28: goproto.proto.testeditions.TestAllTypes.default_foreign_enum:type_name -> goproto.proto.testeditions.ForeignEnum
	9,  // 29: goproto.proto.testeditions.TestAllTypes.oneof_nested_message:type_name -> goproto.proto.testeditions.TestAllTypes.NestedMessage
	1,  // 30: goproto.proto.testeditions.TestAllTypes.oneof_enum:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	29, // 31: goproto.proto.testeditions.TestAllTypes.oneofgroup:type_name -> goproto.proto.testeditions.TestAllTypes.OneofGroup
	4,  // 32: goproto.proto.testeditions.TestRequiredForeign.optional_message:type_name -> goproto.proto.testeditions.TestRequired
	4,  // 33: goproto.proto.testeditions.TestRequiredForeign.repeated_message:type_name -> goproto.proto.testeditions.TestRequired
	30, // 34: goproto.proto.testeditions.TestRequiredForeign.map_message:type_name -> goproto.proto.testeditions.TestRequiredForeign.MapMessageEntry
	4,  // 35: goproto.proto.testeditions.TestRequiredForeign.oneof_message:type_name -> goproto.proto.testeditions.TestRequired
	31, // 36: goproto.proto.testeditions.TestRequiredGroupFields.optionalgroup:type_name -> goproto.proto.testeditions.TestRequiredGroupFields.OptionalGroup
	32, // 37: goproto.proto.testeditions.TestRequiredGroupFields.repeatedgroup:type_name -> goproto.proto.testeditions.TestRequiredGroupFields.RepeatedGroup
	0,  // 38: goproto.proto.testeditions.TestPackedTypes.packed_enum:type_name -> goproto.proto.testeditions.ForeignEnum
	2,  // 39: goproto.proto.testeditions.TestAllTypes.NestedMessage.corecursive:type_name -> goproto.proto.testeditions.TestAllTypes
	9,  // 40: goproto.proto.testeditions.TestAllTypes.OptionalGroup.optional_nested_message:type_name -> goproto.proto.testeditions.TestAllTypes.NestedMessage
	9,  // 41: goproto.proto.testeditions.TestAllTypes.RepeatedGroup.optional_nested_message:type_name -> goproto.proto.testeditions.TestAllTypes.NestedMessage
	9,  // 42: goproto.proto.testeditions.TestAllTypes.MapStringNestedMessageEntry.value:type_name -> goproto.proto.testeditions.TestAllTypes.NestedMessage
	1,  // 43: goproto.proto.testeditions.TestAllTypes.MapStringNestedEnumEntry.value:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	4,  // 44: goproto.proto.testeditions.TestRequiredForeign.MapMessageEntry.value:type_name -> goproto.proto.testeditions.TestRequired
	8,  // 45: goproto.proto.testeditions.packed_int32:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 46: goproto.proto.testeditions.packed_int64:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 47: goproto.proto.testeditions.packed_uint32:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 48: goproto.proto.testeditions.packed_uint64:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 49: goproto.proto.testeditions.packed_sint32:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 50: goproto.proto.testeditions.packed_sint64:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 51: goproto.proto.testeditions.packed_fixed32:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 52: goproto.proto.testeditions.packed_fixed64:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 53: goproto.proto.testeditions.packed_sfixed32:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 54: goproto.proto.testeditions.packed_sfixed64:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 55: goproto.proto.testeditions.packed_float:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 56: goproto.proto.testeditions.packed_double:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 57: goproto.proto.testeditions.packed_bool:extendee -> goproto.proto.testeditions.TestPackedExtensions
	8,  // 58: goproto.proto.testeditions.packed_enum:extendee -> goproto.proto.testeditions.TestPackedExtensions
	0,  // 59: goproto.proto.testeditions.packed_enum:type_name -> goproto.proto.testeditions.ForeignEnum
	60, // [60:60] is the sub-list for method output_type
	60, // [60:60] is the sub-list for method input_type
	59, // [59:60] is the sub-list for extension type_name
	45, // [45:59] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_internal_testprotos_testeditions_test_proto_init() }
func file_internal_testprotos_testeditions_test_proto_init() {
	if File_internal_testprotos_testeditions_test_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_testeditions_test_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeignMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredForeign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredGroupFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestPackedTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestPackedExtensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_OneofGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredGroupFields_OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequiredGroupFields_RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_internal_testprotos_testeditions_test_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TestAllTypes_OneofUint32)(nil),
		(*TestAllTypes_OneofNestedMessage)(nil),
		(*TestAllTypes_OneofString)(nil),
		(*TestAllTypes_OneofBytes)(nil),
		(*TestAllTypes_OneofBool)(nil),
		(*TestAllTypes_OneofUint64)(nil),
		(*TestAllTypes_OneofFloat)(nil),
		(*TestAllTypes_OneofDouble)(nil),
		(*TestAllTypes_OneofEnum)(nil),
		(*TestAllTypes_Oneofgroup)(nil),
		(*TestAllTypes_OneofOptionalUint32)(nil),
	}
	file_internal_testprotos_testeditions_test_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*TestRequiredForeign_OneofMessage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_testeditions_test_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   31,
			NumExtensions: 14,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_testeditions_test_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_testeditions_test_proto_depIdxs,
		EnumInfos:         file_internal_testprotos_testeditions_test_proto_enumTypes,
		MessageInfos:      file_internal_testprotos_testeditions_test_proto_msgTypes,
		ExtensionInfos:    file_internal_testprotos_testeditions_test_proto_extTypes,
	}.Build()
	File_internal_testprotos_testeditions_test_proto = out.File
	file_internal_testprotos_testeditions_test_proto_rawDesc = nil
	file_internal_testprotos_testeditions_test_proto_goTypes = nil
	file_internal_testprotos_testeditions_test_proto_depIdxs = nil
}
