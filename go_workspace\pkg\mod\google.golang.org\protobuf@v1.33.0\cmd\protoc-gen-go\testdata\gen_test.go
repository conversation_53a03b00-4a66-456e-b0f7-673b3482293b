// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package main

import (
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/annotations"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/comments"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/extensions/base"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/extensions/ext"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/extensions/extra"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/extensions/proto3"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/fieldnames"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/import_public"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/import_public/sub"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/import_public/sub2"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/imports"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/imports/fmt"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/imports/test_a_1"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/imports/test_a_2"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/imports/test_b_1"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/issue780_oneof_conflict"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/nopackage"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/proto2"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/proto3"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/protoeditions"
	_ "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/retention"
)
