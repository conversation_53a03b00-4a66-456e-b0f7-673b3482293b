// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Test Protobuf definitions with proto2 syntax.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/textpb2/test.proto

package textpb2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

type Enum int32

const (
	Enum_ONE Enum = 1
	Enum_TWO Enum = 2
	Enum_TEN Enum = 10
)

// Enum value maps for Enum.
var (
	Enum_name = map[int32]string{
		1:  "ONE",
		2:  "TWO",
		10: "TEN",
	}
	Enum_value = map[string]int32{
		"ONE": 1,
		"TWO": 2,
		"TEN": 10,
	}
)

func (x Enum) Enum() *Enum {
	p := new(Enum)
	*p = x
	return p
}

func (x Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_textpb2_test_proto_enumTypes[0].Descriptor()
}

func (Enum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_textpb2_test_proto_enumTypes[0]
}

func (x Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Enum) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Enum(num)
	return nil
}

// Deprecated: Use Enum.Descriptor instead.
func (Enum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{0}
}

type Enums_NestedEnum int32

const (
	Enums_UNO  Enums_NestedEnum = 1
	Enums_DOS  Enums_NestedEnum = 2
	Enums_DIEZ Enums_NestedEnum = 10
)

// Enum value maps for Enums_NestedEnum.
var (
	Enums_NestedEnum_name = map[int32]string{
		1:  "UNO",
		2:  "DOS",
		10: "DIEZ",
	}
	Enums_NestedEnum_value = map[string]int32{
		"UNO":  1,
		"DOS":  2,
		"DIEZ": 10,
	}
)

func (x Enums_NestedEnum) Enum() *Enums_NestedEnum {
	p := new(Enums_NestedEnum)
	*p = x
	return p
}

func (x Enums_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enums_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_textpb2_test_proto_enumTypes[1].Descriptor()
}

func (Enums_NestedEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_textpb2_test_proto_enumTypes[1]
}

func (x Enums_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Enums_NestedEnum) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Enums_NestedEnum(num)
	return nil
}

// Deprecated: Use Enums_NestedEnum.Descriptor instead.
func (Enums_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{1, 0}
}

// Scalars contains optional scalar fields.
type Scalars struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptBool     *bool    `protobuf:"varint,1,opt,name=opt_bool,json=optBool" json:"opt_bool,omitempty"`
	OptInt32    *int32   `protobuf:"varint,2,opt,name=opt_int32,json=optInt32" json:"opt_int32,omitempty"`
	OptInt64    *int64   `protobuf:"varint,3,opt,name=opt_int64,json=optInt64" json:"opt_int64,omitempty"`
	OptUint32   *uint32  `protobuf:"varint,4,opt,name=opt_uint32,json=optUint32" json:"opt_uint32,omitempty"`
	OptUint64   *uint64  `protobuf:"varint,5,opt,name=opt_uint64,json=optUint64" json:"opt_uint64,omitempty"`
	OptSint32   *int32   `protobuf:"zigzag32,6,opt,name=opt_sint32,json=optSint32" json:"opt_sint32,omitempty"`
	OptSint64   *int64   `protobuf:"zigzag64,7,opt,name=opt_sint64,json=optSint64" json:"opt_sint64,omitempty"`
	OptFixed32  *uint32  `protobuf:"fixed32,8,opt,name=opt_fixed32,json=optFixed32" json:"opt_fixed32,omitempty"`
	OptFixed64  *uint64  `protobuf:"fixed64,9,opt,name=opt_fixed64,json=optFixed64" json:"opt_fixed64,omitempty"`
	OptSfixed32 *int32   `protobuf:"fixed32,10,opt,name=opt_sfixed32,json=optSfixed32" json:"opt_sfixed32,omitempty"`
	OptSfixed64 *int64   `protobuf:"fixed64,11,opt,name=opt_sfixed64,json=optSfixed64" json:"opt_sfixed64,omitempty"`
	OptFloat    *float32 `protobuf:"fixed32,20,opt,name=opt_float,json=optFloat" json:"opt_float,omitempty"`
	OptDouble   *float64 `protobuf:"fixed64,21,opt,name=opt_double,json=optDouble" json:"opt_double,omitempty"`
	OptBytes    []byte   `protobuf:"bytes,14,opt,name=opt_bytes,json=optBytes" json:"opt_bytes,omitempty"`
	OptString   *string  `protobuf:"bytes,13,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *Scalars) Reset() {
	*x = Scalars{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scalars) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scalars) ProtoMessage() {}

func (x *Scalars) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scalars.ProtoReflect.Descriptor instead.
func (*Scalars) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{0}
}

func (x *Scalars) GetOptBool() bool {
	if x != nil && x.OptBool != nil {
		return *x.OptBool
	}
	return false
}

func (x *Scalars) GetOptInt32() int32 {
	if x != nil && x.OptInt32 != nil {
		return *x.OptInt32
	}
	return 0
}

func (x *Scalars) GetOptInt64() int64 {
	if x != nil && x.OptInt64 != nil {
		return *x.OptInt64
	}
	return 0
}

func (x *Scalars) GetOptUint32() uint32 {
	if x != nil && x.OptUint32 != nil {
		return *x.OptUint32
	}
	return 0
}

func (x *Scalars) GetOptUint64() uint64 {
	if x != nil && x.OptUint64 != nil {
		return *x.OptUint64
	}
	return 0
}

func (x *Scalars) GetOptSint32() int32 {
	if x != nil && x.OptSint32 != nil {
		return *x.OptSint32
	}
	return 0
}

func (x *Scalars) GetOptSint64() int64 {
	if x != nil && x.OptSint64 != nil {
		return *x.OptSint64
	}
	return 0
}

func (x *Scalars) GetOptFixed32() uint32 {
	if x != nil && x.OptFixed32 != nil {
		return *x.OptFixed32
	}
	return 0
}

func (x *Scalars) GetOptFixed64() uint64 {
	if x != nil && x.OptFixed64 != nil {
		return *x.OptFixed64
	}
	return 0
}

func (x *Scalars) GetOptSfixed32() int32 {
	if x != nil && x.OptSfixed32 != nil {
		return *x.OptSfixed32
	}
	return 0
}

func (x *Scalars) GetOptSfixed64() int64 {
	if x != nil && x.OptSfixed64 != nil {
		return *x.OptSfixed64
	}
	return 0
}

func (x *Scalars) GetOptFloat() float32 {
	if x != nil && x.OptFloat != nil {
		return *x.OptFloat
	}
	return 0
}

func (x *Scalars) GetOptDouble() float64 {
	if x != nil && x.OptDouble != nil {
		return *x.OptDouble
	}
	return 0
}

func (x *Scalars) GetOptBytes() []byte {
	if x != nil {
		return x.OptBytes
	}
	return nil
}

func (x *Scalars) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

// Message contains enum fields.
type Enums struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptEnum       *Enum              `protobuf:"varint,1,opt,name=opt_enum,json=optEnum,enum=pb2.Enum" json:"opt_enum,omitempty"`
	RptEnum       []Enum             `protobuf:"varint,2,rep,name=rpt_enum,json=rptEnum,enum=pb2.Enum" json:"rpt_enum,omitempty"`
	OptNestedEnum *Enums_NestedEnum  `protobuf:"varint,3,opt,name=opt_nested_enum,json=optNestedEnum,enum=pb2.Enums_NestedEnum" json:"opt_nested_enum,omitempty"`
	RptNestedEnum []Enums_NestedEnum `protobuf:"varint,4,rep,name=rpt_nested_enum,json=rptNestedEnum,enum=pb2.Enums_NestedEnum" json:"rpt_nested_enum,omitempty"`
}

func (x *Enums) Reset() {
	*x = Enums{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enums) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enums) ProtoMessage() {}

func (x *Enums) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enums.ProtoReflect.Descriptor instead.
func (*Enums) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{1}
}

func (x *Enums) GetOptEnum() Enum {
	if x != nil && x.OptEnum != nil {
		return *x.OptEnum
	}
	return Enum_ONE
}

func (x *Enums) GetRptEnum() []Enum {
	if x != nil {
		return x.RptEnum
	}
	return nil
}

func (x *Enums) GetOptNestedEnum() Enums_NestedEnum {
	if x != nil && x.OptNestedEnum != nil {
		return *x.OptNestedEnum
	}
	return Enums_UNO
}

func (x *Enums) GetRptNestedEnum() []Enums_NestedEnum {
	if x != nil {
		return x.RptNestedEnum
	}
	return nil
}

// Message contains repeated fields.
type Repeats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RptBool   []bool    `protobuf:"varint,1,rep,name=rpt_bool,json=rptBool" json:"rpt_bool,omitempty"`
	RptInt32  []int32   `protobuf:"varint,2,rep,name=rpt_int32,json=rptInt32" json:"rpt_int32,omitempty"`
	RptInt64  []int64   `protobuf:"varint,3,rep,name=rpt_int64,json=rptInt64" json:"rpt_int64,omitempty"`
	RptUint32 []uint32  `protobuf:"varint,4,rep,name=rpt_uint32,json=rptUint32" json:"rpt_uint32,omitempty"`
	RptUint64 []uint64  `protobuf:"varint,5,rep,name=rpt_uint64,json=rptUint64" json:"rpt_uint64,omitempty"`
	RptFloat  []float32 `protobuf:"fixed32,6,rep,name=rpt_float,json=rptFloat" json:"rpt_float,omitempty"`
	RptDouble []float64 `protobuf:"fixed64,7,rep,name=rpt_double,json=rptDouble" json:"rpt_double,omitempty"`
	RptString []string  `protobuf:"bytes,8,rep,name=rpt_string,json=rptString" json:"rpt_string,omitempty"`
	RptBytes  [][]byte  `protobuf:"bytes,9,rep,name=rpt_bytes,json=rptBytes" json:"rpt_bytes,omitempty"`
}

func (x *Repeats) Reset() {
	*x = Repeats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Repeats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Repeats) ProtoMessage() {}

func (x *Repeats) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Repeats.ProtoReflect.Descriptor instead.
func (*Repeats) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{2}
}

func (x *Repeats) GetRptBool() []bool {
	if x != nil {
		return x.RptBool
	}
	return nil
}

func (x *Repeats) GetRptInt32() []int32 {
	if x != nil {
		return x.RptInt32
	}
	return nil
}

func (x *Repeats) GetRptInt64() []int64 {
	if x != nil {
		return x.RptInt64
	}
	return nil
}

func (x *Repeats) GetRptUint32() []uint32 {
	if x != nil {
		return x.RptUint32
	}
	return nil
}

func (x *Repeats) GetRptUint64() []uint64 {
	if x != nil {
		return x.RptUint64
	}
	return nil
}

func (x *Repeats) GetRptFloat() []float32 {
	if x != nil {
		return x.RptFloat
	}
	return nil
}

func (x *Repeats) GetRptDouble() []float64 {
	if x != nil {
		return x.RptDouble
	}
	return nil
}

func (x *Repeats) GetRptString() []string {
	if x != nil {
		return x.RptString
	}
	return nil
}

func (x *Repeats) GetRptBytes() [][]byte {
	if x != nil {
		return x.RptBytes
	}
	return nil
}

// Message contains map fields.
type Maps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Int32ToStr  map[int32]string   `protobuf:"bytes,1,rep,name=int32_to_str,json=int32ToStr" json:"int32_to_str,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	StrToNested map[string]*Nested `protobuf:"bytes,4,rep,name=str_to_nested,json=strToNested" json:"str_to_nested,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

func (x *Maps) Reset() {
	*x = Maps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Maps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Maps) ProtoMessage() {}

func (x *Maps) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Maps.ProtoReflect.Descriptor instead.
func (*Maps) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{3}
}

func (x *Maps) GetInt32ToStr() map[int32]string {
	if x != nil {
		return x.Int32ToStr
	}
	return nil
}

func (x *Maps) GetStrToNested() map[string]*Nested {
	if x != nil {
		return x.StrToNested
	}
	return nil
}

// Message type used as submessage.
type Nested struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptNested *Nested `protobuf:"bytes,2,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
}

func (x *Nested) Reset() {
	*x = Nested{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nested) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nested) ProtoMessage() {}

func (x *Nested) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nested.ProtoReflect.Descriptor instead.
func (*Nested) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{4}
}

func (x *Nested) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

func (x *Nested) GetOptNested() *Nested {
	if x != nil {
		return x.OptNested
	}
	return nil
}

// Message contains message and group fields.
type Nests struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptNested *Nested           `protobuf:"bytes,1,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
	Optgroup  *Nests_OptGroup   `protobuf:"group,2,opt,name=OptGroup,json=optgroup" json:"optgroup,omitempty"`
	RptNested []*Nested         `protobuf:"bytes,4,rep,name=rpt_nested,json=rptNested" json:"rpt_nested,omitempty"`
	Rptgroup  []*Nests_RptGroup `protobuf:"group,5,rep,name=RptGroup,json=rptgroup" json:"rptgroup,omitempty"`
}

func (x *Nests) Reset() {
	*x = Nests{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests) ProtoMessage() {}

func (x *Nests) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests.ProtoReflect.Descriptor instead.
func (*Nests) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{5}
}

func (x *Nests) GetOptNested() *Nested {
	if x != nil {
		return x.OptNested
	}
	return nil
}

func (x *Nests) GetOptgroup() *Nests_OptGroup {
	if x != nil {
		return x.Optgroup
	}
	return nil
}

func (x *Nests) GetRptNested() []*Nested {
	if x != nil {
		return x.RptNested
	}
	return nil
}

func (x *Nests) GetRptgroup() []*Nests_RptGroup {
	if x != nil {
		return x.Rptgroup
	}
	return nil
}

// Message contains required fields.
type Requireds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqBool     *bool    `protobuf:"varint,1,req,name=req_bool,json=reqBool" json:"req_bool,omitempty"`
	ReqSfixed64 *int64   `protobuf:"fixed64,2,req,name=req_sfixed64,json=reqSfixed64" json:"req_sfixed64,omitempty"`
	ReqDouble   *float64 `protobuf:"fixed64,3,req,name=req_double,json=reqDouble" json:"req_double,omitempty"`
	ReqString   *string  `protobuf:"bytes,4,req,name=req_string,json=reqString" json:"req_string,omitempty"`
	ReqEnum     *Enum    `protobuf:"varint,5,req,name=req_enum,json=reqEnum,enum=pb2.Enum" json:"req_enum,omitempty"`
	ReqNested   *Nested  `protobuf:"bytes,6,req,name=req_nested,json=reqNested" json:"req_nested,omitempty"`
}

func (x *Requireds) Reset() {
	*x = Requireds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Requireds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Requireds) ProtoMessage() {}

func (x *Requireds) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Requireds.ProtoReflect.Descriptor instead.
func (*Requireds) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{6}
}

func (x *Requireds) GetReqBool() bool {
	if x != nil && x.ReqBool != nil {
		return *x.ReqBool
	}
	return false
}

func (x *Requireds) GetReqSfixed64() int64 {
	if x != nil && x.ReqSfixed64 != nil {
		return *x.ReqSfixed64
	}
	return 0
}

func (x *Requireds) GetReqDouble() float64 {
	if x != nil && x.ReqDouble != nil {
		return *x.ReqDouble
	}
	return 0
}

func (x *Requireds) GetReqString() string {
	if x != nil && x.ReqString != nil {
		return *x.ReqString
	}
	return ""
}

func (x *Requireds) GetReqEnum() Enum {
	if x != nil && x.ReqEnum != nil {
		return *x.ReqEnum
	}
	return Enum_ONE
}

func (x *Requireds) GetReqNested() *Nested {
	if x != nil {
		return x.ReqNested
	}
	return nil
}

// Message contains both required and optional fields.
type PartialRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqString *string `protobuf:"bytes,1,req,name=req_string,json=reqString" json:"req_string,omitempty"`
	OptString *string `protobuf:"bytes,2,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *PartialRequired) Reset() {
	*x = PartialRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartialRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartialRequired) ProtoMessage() {}

func (x *PartialRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartialRequired.ProtoReflect.Descriptor instead.
func (*PartialRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{7}
}

func (x *PartialRequired) GetReqString() string {
	if x != nil && x.ReqString != nil {
		return *x.ReqString
	}
	return ""
}

func (x *PartialRequired) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

type NestedWithRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqString *string `protobuf:"bytes,1,req,name=req_string,json=reqString" json:"req_string,omitempty"`
}

func (x *NestedWithRequired) Reset() {
	*x = NestedWithRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NestedWithRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NestedWithRequired) ProtoMessage() {}

func (x *NestedWithRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NestedWithRequired.ProtoReflect.Descriptor instead.
func (*NestedWithRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{8}
}

func (x *NestedWithRequired) GetReqString() string {
	if x != nil && x.ReqString != nil {
		return *x.ReqString
	}
	return ""
}

type IndirectRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptNested   *NestedWithRequired            `protobuf:"bytes,1,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
	RptNested   []*NestedWithRequired          `protobuf:"bytes,2,rep,name=rpt_nested,json=rptNested" json:"rpt_nested,omitempty"`
	StrToNested map[string]*NestedWithRequired `protobuf:"bytes,3,rep,name=str_to_nested,json=strToNested" json:"str_to_nested,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Types that are assignable to Union:
	//
	//	*IndirectRequired_OneofNested
	Union isIndirectRequired_Union `protobuf_oneof:"union"`
}

func (x *IndirectRequired) Reset() {
	*x = IndirectRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndirectRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndirectRequired) ProtoMessage() {}

func (x *IndirectRequired) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndirectRequired.ProtoReflect.Descriptor instead.
func (*IndirectRequired) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{9}
}

func (x *IndirectRequired) GetOptNested() *NestedWithRequired {
	if x != nil {
		return x.OptNested
	}
	return nil
}

func (x *IndirectRequired) GetRptNested() []*NestedWithRequired {
	if x != nil {
		return x.RptNested
	}
	return nil
}

func (x *IndirectRequired) GetStrToNested() map[string]*NestedWithRequired {
	if x != nil {
		return x.StrToNested
	}
	return nil
}

func (m *IndirectRequired) GetUnion() isIndirectRequired_Union {
	if m != nil {
		return m.Union
	}
	return nil
}

func (x *IndirectRequired) GetOneofNested() *NestedWithRequired {
	if x, ok := x.GetUnion().(*IndirectRequired_OneofNested); ok {
		return x.OneofNested
	}
	return nil
}

type isIndirectRequired_Union interface {
	isIndirectRequired_Union()
}

type IndirectRequired_OneofNested struct {
	OneofNested *NestedWithRequired `protobuf:"bytes,4,opt,name=oneof_nested,json=oneofNested,oneof"`
}

func (*IndirectRequired_OneofNested) isIndirectRequired_Union() {}

type Extensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptBool   *bool   `protobuf:"varint,101,opt,name=opt_bool,json=optBool" json:"opt_bool,omitempty"`
	OptInt32  *int32  `protobuf:"varint,2,opt,name=opt_int32,json=optInt32" json:"opt_int32,omitempty"`
}

func (x *Extensions) Reset() {
	*x = Extensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Extensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Extensions) ProtoMessage() {}

func (x *Extensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Extensions.ProtoReflect.Descriptor instead.
func (*Extensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{10}
}

func (x *Extensions) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

func (x *Extensions) GetOptBool() bool {
	if x != nil && x.OptBool != nil {
		return *x.OptBool
	}
	return false
}

func (x *Extensions) GetOptInt32() int32 {
	if x != nil && x.OptInt32 != nil {
		return *x.OptInt32
	}
	return 0
}

type ExtensionsContainer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExtensionsContainer) Reset() {
	*x = ExtensionsContainer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtensionsContainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionsContainer) ProtoMessage() {}

func (x *ExtensionsContainer) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionsContainer.ProtoReflect.Descriptor instead.
func (*ExtensionsContainer) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{11}
}

type MessageSet struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *MessageSet) Reset() {
	*x = MessageSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageSet) ProtoMessage() {}

func (x *MessageSet) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageSet.ProtoReflect.Descriptor instead.
func (*MessageSet) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{12}
}

type MessageSetExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *MessageSetExtension) Reset() {
	*x = MessageSetExtension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageSetExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageSetExtension) ProtoMessage() {}

func (x *MessageSetExtension) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageSetExtension.ProtoReflect.Descriptor instead.
func (*MessageSetExtension) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{13}
}

func (x *MessageSetExtension) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

type FakeMessageSet struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *FakeMessageSet) Reset() {
	*x = FakeMessageSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FakeMessageSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FakeMessageSet) ProtoMessage() {}

func (x *FakeMessageSet) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FakeMessageSet.ProtoReflect.Descriptor instead.
func (*FakeMessageSet) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{14}
}

type FakeMessageSetExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString *string `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
}

func (x *FakeMessageSetExtension) Reset() {
	*x = FakeMessageSetExtension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FakeMessageSetExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FakeMessageSetExtension) ProtoMessage() {}

func (x *FakeMessageSetExtension) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FakeMessageSetExtension.ProtoReflect.Descriptor instead.
func (*FakeMessageSetExtension) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{15}
}

func (x *FakeMessageSetExtension) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

// Message contains well-known type fields.
type KnownTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptBool      *wrapperspb.BoolValue   `protobuf:"bytes,1,opt,name=opt_bool,json=optBool" json:"opt_bool,omitempty"`
	OptInt32     *wrapperspb.Int32Value  `protobuf:"bytes,2,opt,name=opt_int32,json=optInt32" json:"opt_int32,omitempty"`
	OptInt64     *wrapperspb.Int64Value  `protobuf:"bytes,3,opt,name=opt_int64,json=optInt64" json:"opt_int64,omitempty"`
	OptUint32    *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=opt_uint32,json=optUint32" json:"opt_uint32,omitempty"`
	OptUint64    *wrapperspb.UInt64Value `protobuf:"bytes,5,opt,name=opt_uint64,json=optUint64" json:"opt_uint64,omitempty"`
	OptFloat     *wrapperspb.FloatValue  `protobuf:"bytes,6,opt,name=opt_float,json=optFloat" json:"opt_float,omitempty"`
	OptDouble    *wrapperspb.DoubleValue `protobuf:"bytes,7,opt,name=opt_double,json=optDouble" json:"opt_double,omitempty"`
	OptString    *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptBytes     *wrapperspb.BytesValue  `protobuf:"bytes,9,opt,name=opt_bytes,json=optBytes" json:"opt_bytes,omitempty"`
	OptDuration  *durationpb.Duration    `protobuf:"bytes,20,opt,name=opt_duration,json=optDuration" json:"opt_duration,omitempty"`
	OptTimestamp *timestamppb.Timestamp  `protobuf:"bytes,21,opt,name=opt_timestamp,json=optTimestamp" json:"opt_timestamp,omitempty"`
	OptStruct    *structpb.Struct        `protobuf:"bytes,25,opt,name=opt_struct,json=optStruct" json:"opt_struct,omitempty"`
	OptList      *structpb.ListValue     `protobuf:"bytes,26,opt,name=opt_list,json=optList" json:"opt_list,omitempty"`
	OptValue     *structpb.Value         `protobuf:"bytes,27,opt,name=opt_value,json=optValue" json:"opt_value,omitempty"`
	OptNull      *structpb.NullValue     `protobuf:"varint,28,opt,name=opt_null,json=optNull,enum=google.protobuf.NullValue" json:"opt_null,omitempty"`
	OptEmpty     *emptypb.Empty          `protobuf:"bytes,30,opt,name=opt_empty,json=optEmpty" json:"opt_empty,omitempty"`
	OptAny       *anypb.Any              `protobuf:"bytes,32,opt,name=opt_any,json=optAny" json:"opt_any,omitempty"`
	OptFieldmask *fieldmaskpb.FieldMask  `protobuf:"bytes,40,opt,name=opt_fieldmask,json=optFieldmask" json:"opt_fieldmask,omitempty"`
}

func (x *KnownTypes) Reset() {
	*x = KnownTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnownTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnownTypes) ProtoMessage() {}

func (x *KnownTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnownTypes.ProtoReflect.Descriptor instead.
func (*KnownTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{16}
}

func (x *KnownTypes) GetOptBool() *wrapperspb.BoolValue {
	if x != nil {
		return x.OptBool
	}
	return nil
}

func (x *KnownTypes) GetOptInt32() *wrapperspb.Int32Value {
	if x != nil {
		return x.OptInt32
	}
	return nil
}

func (x *KnownTypes) GetOptInt64() *wrapperspb.Int64Value {
	if x != nil {
		return x.OptInt64
	}
	return nil
}

func (x *KnownTypes) GetOptUint32() *wrapperspb.UInt32Value {
	if x != nil {
		return x.OptUint32
	}
	return nil
}

func (x *KnownTypes) GetOptUint64() *wrapperspb.UInt64Value {
	if x != nil {
		return x.OptUint64
	}
	return nil
}

func (x *KnownTypes) GetOptFloat() *wrapperspb.FloatValue {
	if x != nil {
		return x.OptFloat
	}
	return nil
}

func (x *KnownTypes) GetOptDouble() *wrapperspb.DoubleValue {
	if x != nil {
		return x.OptDouble
	}
	return nil
}

func (x *KnownTypes) GetOptString() *wrapperspb.StringValue {
	if x != nil {
		return x.OptString
	}
	return nil
}

func (x *KnownTypes) GetOptBytes() *wrapperspb.BytesValue {
	if x != nil {
		return x.OptBytes
	}
	return nil
}

func (x *KnownTypes) GetOptDuration() *durationpb.Duration {
	if x != nil {
		return x.OptDuration
	}
	return nil
}

func (x *KnownTypes) GetOptTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OptTimestamp
	}
	return nil
}

func (x *KnownTypes) GetOptStruct() *structpb.Struct {
	if x != nil {
		return x.OptStruct
	}
	return nil
}

func (x *KnownTypes) GetOptList() *structpb.ListValue {
	if x != nil {
		return x.OptList
	}
	return nil
}

func (x *KnownTypes) GetOptValue() *structpb.Value {
	if x != nil {
		return x.OptValue
	}
	return nil
}

func (x *KnownTypes) GetOptNull() structpb.NullValue {
	if x != nil && x.OptNull != nil {
		return *x.OptNull
	}
	return structpb.NullValue(0)
}

func (x *KnownTypes) GetOptEmpty() *emptypb.Empty {
	if x != nil {
		return x.OptEmpty
	}
	return nil
}

func (x *KnownTypes) GetOptAny() *anypb.Any {
	if x != nil {
		return x.OptAny
	}
	return nil
}

func (x *KnownTypes) GetOptFieldmask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.OptFieldmask
	}
	return nil
}

type Nests_OptGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptString      *string                        `protobuf:"bytes,1,opt,name=opt_string,json=optString" json:"opt_string,omitempty"`
	OptNested      *Nested                        `protobuf:"bytes,2,opt,name=opt_nested,json=optNested" json:"opt_nested,omitempty"`
	Optnestedgroup *Nests_OptGroup_OptNestedGroup `protobuf:"group,3,opt,name=OptNestedGroup,json=optnestedgroup" json:"optnestedgroup,omitempty"`
}

func (x *Nests_OptGroup) Reset() {
	*x = Nests_OptGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests_OptGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests_OptGroup) ProtoMessage() {}

func (x *Nests_OptGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests_OptGroup.ProtoReflect.Descriptor instead.
func (*Nests_OptGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Nests_OptGroup) GetOptString() string {
	if x != nil && x.OptString != nil {
		return *x.OptString
	}
	return ""
}

func (x *Nests_OptGroup) GetOptNested() *Nested {
	if x != nil {
		return x.OptNested
	}
	return nil
}

func (x *Nests_OptGroup) GetOptnestedgroup() *Nests_OptGroup_OptNestedGroup {
	if x != nil {
		return x.Optnestedgroup
	}
	return nil
}

type Nests_RptGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RptString []string `protobuf:"bytes,1,rep,name=rpt_string,json=rptString" json:"rpt_string,omitempty"`
}

func (x *Nests_RptGroup) Reset() {
	*x = Nests_RptGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests_RptGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests_RptGroup) ProtoMessage() {}

func (x *Nests_RptGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests_RptGroup.ProtoReflect.Descriptor instead.
func (*Nests_RptGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{5, 1}
}

func (x *Nests_RptGroup) GetRptString() []string {
	if x != nil {
		return x.RptString
	}
	return nil
}

type Nests_OptGroup_OptNestedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptFixed32 *uint32 `protobuf:"fixed32,1,opt,name=opt_fixed32,json=optFixed32" json:"opt_fixed32,omitempty"`
}

func (x *Nests_OptGroup_OptNestedGroup) Reset() {
	*x = Nests_OptGroup_OptNestedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nests_OptGroup_OptNestedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nests_OptGroup_OptNestedGroup) ProtoMessage() {}

func (x *Nests_OptGroup_OptNestedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_textpb2_test_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nests_OptGroup_OptNestedGroup.ProtoReflect.Descriptor instead.
func (*Nests_OptGroup_OptNestedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_textpb2_test_proto_rawDescGZIP(), []int{5, 0, 0}
}

func (x *Nests_OptGroup_OptNestedGroup) GetOptFixed32() uint32 {
	if x != nil && x.OptFixed32 != nil {
		return *x.OptFixed32
	}
	return 0
}

var file_internal_testprotos_textpb2_test_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         21,
		Name:          "pb2.opt_ext_bool",
		Tag:           "varint,21,opt,name=opt_ext_bool",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         22,
		Name:          "pb2.opt_ext_string",
		Tag:           "bytes,22,opt,name=opt_ext_string",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Enum)(nil),
		Field:         23,
		Name:          "pb2.opt_ext_enum",
		Tag:           "varint,23,opt,name=opt_ext_enum,enum=pb2.Enum",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Nested)(nil),
		Field:         24,
		Name:          "pb2.opt_ext_nested",
		Tag:           "bytes,24,opt,name=opt_ext_nested",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*PartialRequired)(nil),
		Field:         25,
		Name:          "pb2.opt_ext_partial",
		Tag:           "bytes,25,opt,name=opt_ext_partial",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         31,
		Name:          "pb2.rpt_ext_fixed32",
		Tag:           "fixed32,31,rep,name=rpt_ext_fixed32",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]Enum)(nil),
		Field:         32,
		Name:          "pb2.rpt_ext_enum",
		Tag:           "varint,32,rep,name=rpt_ext_enum,enum=pb2.Enum",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]*Nested)(nil),
		Field:         33,
		Name:          "pb2.rpt_ext_nested",
		Tag:           "bytes,33,rep,name=rpt_ext_nested",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*FakeMessageSetExtension)(nil),
		Field:         50,
		Name:          "pb2.message_set_extension",
		Tag:           "bytes,50,opt,name=message_set_extension",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         51,
		Name:          "pb2.ExtensionsContainer.opt_ext_bool",
		Tag:           "varint,51,opt,name=opt_ext_bool",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         52,
		Name:          "pb2.ExtensionsContainer.opt_ext_string",
		Tag:           "bytes,52,opt,name=opt_ext_string",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Enum)(nil),
		Field:         53,
		Name:          "pb2.ExtensionsContainer.opt_ext_enum",
		Tag:           "varint,53,opt,name=opt_ext_enum,enum=pb2.Enum",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*Nested)(nil),
		Field:         54,
		Name:          "pb2.ExtensionsContainer.opt_ext_nested",
		Tag:           "bytes,54,opt,name=opt_ext_nested",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: (*PartialRequired)(nil),
		Field:         55,
		Name:          "pb2.ExtensionsContainer.opt_ext_partial",
		Tag:           "bytes,55,opt,name=opt_ext_partial",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         61,
		Name:          "pb2.ExtensionsContainer.rpt_ext_string",
		Tag:           "bytes,61,rep,name=rpt_ext_string",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]Enum)(nil),
		Field:         62,
		Name:          "pb2.ExtensionsContainer.rpt_ext_enum",
		Tag:           "varint,62,rep,name=rpt_ext_enum,enum=pb2.Enum",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*Extensions)(nil),
		ExtensionType: ([]*Nested)(nil),
		Field:         63,
		Name:          "pb2.ExtensionsContainer.rpt_ext_nested",
		Tag:           "bytes,63,rep,name=rpt_ext_nested",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*MessageSetExtension)(nil),
		Field:         10,
		Name:          "pb2.MessageSetExtension.message_set_extension",
		Tag:           "bytes,10,opt,name=message_set_extension",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*MessageSetExtension)(nil),
		Field:         20,
		Name:          "pb2.MessageSetExtension.not_message_set_extension",
		Tag:           "bytes,20,opt,name=not_message_set_extension",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*MessageSet)(nil),
		ExtensionType: (*Nested)(nil),
		Field:         30,
		Name:          "pb2.MessageSetExtension.ext_nested",
		Tag:           "bytes,30,opt,name=ext_nested",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
	{
		ExtendedType:  (*FakeMessageSet)(nil),
		ExtensionType: (*FakeMessageSetExtension)(nil),
		Field:         10,
		Name:          "pb2.FakeMessageSetExtension.message_set_extension",
		Tag:           "bytes,10,opt,name=message_set_extension",
		Filename:      "internal/testprotos/textpb2/test.proto",
	},
}

// Extension fields to Extensions.
var (
	// optional bool opt_ext_bool = 21;
	E_OptExtBool = &file_internal_testprotos_textpb2_test_proto_extTypes[0]
	// optional string opt_ext_string = 22;
	E_OptExtString = &file_internal_testprotos_textpb2_test_proto_extTypes[1]
	// optional pb2.Enum opt_ext_enum = 23;
	E_OptExtEnum = &file_internal_testprotos_textpb2_test_proto_extTypes[2]
	// optional pb2.Nested opt_ext_nested = 24;
	E_OptExtNested = &file_internal_testprotos_textpb2_test_proto_extTypes[3]
	// optional pb2.PartialRequired opt_ext_partial = 25;
	E_OptExtPartial = &file_internal_testprotos_textpb2_test_proto_extTypes[4]
	// repeated fixed32 rpt_ext_fixed32 = 31;
	E_RptExtFixed32 = &file_internal_testprotos_textpb2_test_proto_extTypes[5]
	// repeated pb2.Enum rpt_ext_enum = 32;
	E_RptExtEnum = &file_internal_testprotos_textpb2_test_proto_extTypes[6]
	// repeated pb2.Nested rpt_ext_nested = 33;
	E_RptExtNested = &file_internal_testprotos_textpb2_test_proto_extTypes[7]
	// optional bool opt_ext_bool = 51;
	E_ExtensionsContainer_OptExtBool = &file_internal_testprotos_textpb2_test_proto_extTypes[9]
	// optional string opt_ext_string = 52;
	E_ExtensionsContainer_OptExtString = &file_internal_testprotos_textpb2_test_proto_extTypes[10]
	// optional pb2.Enum opt_ext_enum = 53;
	E_ExtensionsContainer_OptExtEnum = &file_internal_testprotos_textpb2_test_proto_extTypes[11]
	// optional pb2.Nested opt_ext_nested = 54;
	E_ExtensionsContainer_OptExtNested = &file_internal_testprotos_textpb2_test_proto_extTypes[12]
	// optional pb2.PartialRequired opt_ext_partial = 55;
	E_ExtensionsContainer_OptExtPartial = &file_internal_testprotos_textpb2_test_proto_extTypes[13]
	// repeated string rpt_ext_string = 61;
	E_ExtensionsContainer_RptExtString = &file_internal_testprotos_textpb2_test_proto_extTypes[14]
	// repeated pb2.Enum rpt_ext_enum = 62;
	E_ExtensionsContainer_RptExtEnum = &file_internal_testprotos_textpb2_test_proto_extTypes[15]
	// repeated pb2.Nested rpt_ext_nested = 63;
	E_ExtensionsContainer_RptExtNested = &file_internal_testprotos_textpb2_test_proto_extTypes[16]
)

// Extension fields to MessageSet.
var (
	// optional pb2.FakeMessageSetExtension message_set_extension = 50;
	E_MessageSetExtension = &file_internal_testprotos_textpb2_test_proto_extTypes[8]
	// optional pb2.MessageSetExtension message_set_extension = 10;
	E_MessageSetExtension_MessageSetExtension = &file_internal_testprotos_textpb2_test_proto_extTypes[17]
	// optional pb2.MessageSetExtension not_message_set_extension = 20;
	E_MessageSetExtension_NotMessageSetExtension = &file_internal_testprotos_textpb2_test_proto_extTypes[18]
	// optional pb2.Nested ext_nested = 30;
	E_MessageSetExtension_ExtNested = &file_internal_testprotos_textpb2_test_proto_extTypes[19]
)

// Extension fields to FakeMessageSet.
var (
	// optional pb2.FakeMessageSetExtension message_set_extension = 10;
	E_FakeMessageSetExtension_MessageSetExtension = &file_internal_testprotos_textpb2_test_proto_extTypes[20]
)

var File_internal_testprotos_textpb2_test_proto protoreflect.FileDescriptor

var file_internal_testprotos_textpb2_test_proto_rawDesc = []byte{
	0x0a, 0x26, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x70, 0x62, 0x32, 0x2f, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x70, 0x62, 0x32, 0x1a, 0x19, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61,
	0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x03, 0x0a, 0x07, 0x53, 0x63, 0x61, 0x6c, 0x61,
	0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x6f, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70,
	0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f,
	0x70, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6f, 0x70, 0x74,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x11, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x07, 0x20, 0x01, 0x28, 0x12, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x18, 0x09, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x0b, 0x6f, 0x70, 0x74,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x10, 0x52, 0x0b,
	0x6f, 0x70, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1b, 0x0a, 0x09, 0x6f,
	0x70, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08,
	0x6f, 0x70, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6f, 0x70,
	0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x22, 0xfb, 0x01, 0x0a, 0x05, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x12, 0x24, 0x0a,
	0x08, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x09, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x08, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x07, 0x72, 0x70, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x3d, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x3d, 0x0a, 0x0f, 0x72, 0x70, 0x74, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x72, 0x70, 0x74, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x22, 0x28, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x4e, 0x4f, 0x10, 0x01, 0x12, 0x07,
	0x0a, 0x03, 0x44, 0x4f, 0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x49, 0x45, 0x5a, 0x10,
	0x0a, 0x22, 0x94, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x72, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x08, 0x52,
	0x07, 0x72, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x70, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x72, 0x70, 0x74,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x72, 0x70, 0x74, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x70, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x09, 0x72, 0x70, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x70, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x08, 0x72, 0x70, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x70, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x01, 0x52, 0x09, 0x72, 0x70, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x70, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x08,
	0x72, 0x70, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x22, 0x8f, 0x02, 0x0a, 0x04, 0x4d, 0x61, 0x70,
	0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74,
	0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x61,
	0x70, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x54, 0x6f, 0x53, 0x74, 0x72, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x54, 0x6f, 0x53, 0x74, 0x72, 0x12, 0x3e,
	0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x74, 0x6f, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x73,
	0x2e, 0x53, 0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x1a, 0x3d,
	0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x54, 0x6f, 0x53, 0x74, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4b, 0x0a,
	0x10, 0x53, 0x74, 0x72, 0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x53, 0x0a, 0x06, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x22,
	0xd3, 0x03, 0x0a, 0x05, 0x4e, 0x65, 0x73, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x0a, 0x6f, 0x70, 0x74,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0a, 0x32, 0x13, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x73, 0x2e, 0x4f, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x08, 0x6f, 0x70,
	0x74, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x6e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x72, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x70, 0x74, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0a, 0x32, 0x13, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x73,
	0x2e, 0x52, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x08, 0x72, 0x70, 0x74, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x1a, 0xd4, 0x01, 0x0a, 0x08, 0x4f, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x2a, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x52, 0x09, 0x6f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x4a, 0x0a, 0x0e, 0x6f,
	0x70, 0x74, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0a, 0x32, 0x22, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x73, 0x2e,
	0x4f, 0x70, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x4f, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x6e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x31, 0x0a, 0x0e, 0x4f, 0x70, 0x74, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x74,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0a,
	0x6f, 0x70, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x1a, 0x29, 0x0a, 0x08, 0x52, 0x70,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x70, 0x74, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x22, 0xd9, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x71, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x10, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x02, 0x28, 0x01, 0x52, 0x09, 0x72, 0x65, 0x71, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x04,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x24, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x02, 0x28,
	0x0e, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x72, 0x65, 0x71, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x22, 0x4f, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x22, 0x33, 0x0a, 0x12, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0xee, 0x02, 0x0a, 0x10, 0x49, 0x6e, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x0a,
	0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x0a, 0x72, 0x70, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x09, 0x72, 0x70, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x4a, 0x0a, 0x0d,
	0x73, 0x74, 0x72, 0x5f, 0x74, 0x6f, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x54, 0x6f,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x74, 0x72,
	0x54, 0x6f, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x1a, 0x57, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x54, 0x6f, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x62,
	0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42,
	0x07, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x22, 0x69, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f,
	0x6c, 0x18, 0x65, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c,
	0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x2a, 0x04, 0x08,
	0x14, 0x10, 0x65, 0x22, 0x89, 0x04, 0x0a, 0x13, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x32, 0x31, 0x0a, 0x0c, 0x6f,
	0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x0f, 0x2e, 0x70, 0x62,
	0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x32, 0x35,
	0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0x3c, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x70,
	0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x75, 0x6d, 0x32, 0x42, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x36, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70,
	0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x45, 0x78,
	0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x32, 0x4d, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x5f, 0x65,
	0x78, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x37, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x32, 0x35, 0x0a, 0x0e, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78,
	0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0x3c, 0x0a,
	0x0c, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x2e,
	0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x3e,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x0a, 0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x32, 0x42, 0x0a, 0x0e, 0x72,
	0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x0f, 0x2e,
	0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x3f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x52, 0x0c, 0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x22,
	0x1a, 0x0a, 0x0a, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x2a, 0x08, 0x08,
	0x04, 0x10, 0xff, 0xff, 0xff, 0xff, 0x07, 0x3a, 0x02, 0x08, 0x01, 0x22, 0xb6, 0x02, 0x0a, 0x13,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x32, 0x5d, 0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e, 0x70, 0x62,
	0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x32, 0x64, 0x0a, 0x19, 0x6e, 0x6f, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0f,
	0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x16, 0x6e, 0x6f, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x3b, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70,
	0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x09, 0x65, 0x78, 0x74, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x22, 0x1a, 0x0a, 0x0e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x2a, 0x08, 0x08, 0x04, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02,
	0x22, 0x9f, 0x01, 0x0a, 0x17, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0x65, 0x0a, 0x15, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x13, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x9e, 0x08, 0x0a, 0x0a, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x35, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x07, 0x6f, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x3b, 0x0a, 0x0a,
	0x6f, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x6f, 0x70, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x3b, 0x0a, 0x0a, 0x6f, 0x70, 0x74,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6f, 0x70, 0x74,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x3b, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x09, 0x6f, 0x70, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x3b, 0x0a,
	0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70,
	0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6f, 0x70, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x36, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x09, 0x6f, 0x70, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x6f,
	0x70, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x33, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f,
	0x70, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x6e,
	0x75, 0x6c, 0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x4e, 0x75, 0x6c, 0x6c, 0x12, 0x33,
	0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x2d, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x5f, 0x61, 0x6e, 0x79, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x41,
	0x6e, 0x79, 0x12, 0x3f, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x6d,
	0x61, 0x73, 0x6b, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x6d,
	0x61, 0x73, 0x6b, 0x2a, 0x21, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x4f,
	0x4e, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x12, 0x07, 0x0a,
	0x03, 0x54, 0x45, 0x4e, 0x10, 0x0a, 0x3a, 0x31, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78,
	0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6f,
	0x70, 0x74, 0x45, 0x78, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x35, 0x0a, 0x0e, 0x6f, 0x70, 0x74,
	0x5f, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x0f, 0x2e, 0x70, 0x62,
	0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x3a, 0x3c, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x42,
	0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x3a, 0x4d, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61,
	0x72, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70,
	0x62, 0x32, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x45, 0x78, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61,
	0x6c, 0x3a, 0x37, 0x0a, 0x0f, 0x72, 0x70, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0d, 0x72, 0x70, 0x74,
	0x45, 0x78, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x3c, 0x0a, 0x0c, 0x72, 0x70,
	0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x72, 0x70,
	0x74, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x42, 0x0a, 0x0e, 0x72, 0x70, 0x74, 0x5f,
	0x65, 0x78, 0x74, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x52, 0x0c,
	0x72, 0x70, 0x74, 0x45, 0x78, 0x74, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x3a, 0x61, 0x0a, 0x15,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x62, 0x32, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65,
	0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x38, 0x5a, 0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x70, 0x62, 0x32,
}

var (
	file_internal_testprotos_textpb2_test_proto_rawDescOnce sync.Once
	file_internal_testprotos_textpb2_test_proto_rawDescData = file_internal_testprotos_textpb2_test_proto_rawDesc
)

func file_internal_testprotos_textpb2_test_proto_rawDescGZIP() []byte {
	file_internal_testprotos_textpb2_test_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_textpb2_test_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_textpb2_test_proto_rawDescData)
	})
	return file_internal_testprotos_textpb2_test_proto_rawDescData
}

var file_internal_testprotos_textpb2_test_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_internal_testprotos_textpb2_test_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_internal_testprotos_textpb2_test_proto_goTypes = []interface{}{
	(Enum)(0),                             // 0: pb2.Enum
	(Enums_NestedEnum)(0),                 // 1: pb2.Enums.NestedEnum
	(*Scalars)(nil),                       // 2: pb2.Scalars
	(*Enums)(nil),                         // 3: pb2.Enums
	(*Repeats)(nil),                       // 4: pb2.Repeats
	(*Maps)(nil),                          // 5: pb2.Maps
	(*Nested)(nil),                        // 6: pb2.Nested
	(*Nests)(nil),                         // 7: pb2.Nests
	(*Requireds)(nil),                     // 8: pb2.Requireds
	(*PartialRequired)(nil),               // 9: pb2.PartialRequired
	(*NestedWithRequired)(nil),            // 10: pb2.NestedWithRequired
	(*IndirectRequired)(nil),              // 11: pb2.IndirectRequired
	(*Extensions)(nil),                    // 12: pb2.Extensions
	(*ExtensionsContainer)(nil),           // 13: pb2.ExtensionsContainer
	(*MessageSet)(nil),                    // 14: pb2.MessageSet
	(*MessageSetExtension)(nil),           // 15: pb2.MessageSetExtension
	(*FakeMessageSet)(nil),                // 16: pb2.FakeMessageSet
	(*FakeMessageSetExtension)(nil),       // 17: pb2.FakeMessageSetExtension
	(*KnownTypes)(nil),                    // 18: pb2.KnownTypes
	nil,                                   // 19: pb2.Maps.Int32ToStrEntry
	nil,                                   // 20: pb2.Maps.StrToNestedEntry
	(*Nests_OptGroup)(nil),                // 21: pb2.Nests.OptGroup
	(*Nests_RptGroup)(nil),                // 22: pb2.Nests.RptGroup
	(*Nests_OptGroup_OptNestedGroup)(nil), // 23: pb2.Nests.OptGroup.OptNestedGroup
	nil,                                   // 24: pb2.IndirectRequired.StrToNestedEntry
	(*wrapperspb.BoolValue)(nil),          // 25: google.protobuf.BoolValue
	(*wrapperspb.Int32Value)(nil),         // 26: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),         // 27: google.protobuf.Int64Value
	(*wrapperspb.UInt32Value)(nil),        // 28: google.protobuf.UInt32Value
	(*wrapperspb.UInt64Value)(nil),        // 29: google.protobuf.UInt64Value
	(*wrapperspb.FloatValue)(nil),         // 30: google.protobuf.FloatValue
	(*wrapperspb.DoubleValue)(nil),        // 31: google.protobuf.DoubleValue
	(*wrapperspb.StringValue)(nil),        // 32: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),         // 33: google.protobuf.BytesValue
	(*durationpb.Duration)(nil),           // 34: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),         // 35: google.protobuf.Timestamp
	(*structpb.Struct)(nil),               // 36: google.protobuf.Struct
	(*structpb.ListValue)(nil),            // 37: google.protobuf.ListValue
	(*structpb.Value)(nil),                // 38: google.protobuf.Value
	(structpb.NullValue)(0),               // 39: google.protobuf.NullValue
	(*emptypb.Empty)(nil),                 // 40: google.protobuf.Empty
	(*anypb.Any)(nil),                     // 41: google.protobuf.Any
	(*fieldmaskpb.FieldMask)(nil),         // 42: google.protobuf.FieldMask
}
var file_internal_testprotos_textpb2_test_proto_depIdxs = []int32{
	0,  // 0: pb2.Enums.opt_enum:type_name -> pb2.Enum
	0,  // 1: pb2.Enums.rpt_enum:type_name -> pb2.Enum
	1,  // 2: pb2.Enums.opt_nested_enum:type_name -> pb2.Enums.NestedEnum
	1,  // 3: pb2.Enums.rpt_nested_enum:type_name -> pb2.Enums.NestedEnum
	19, // 4: pb2.Maps.int32_to_str:type_name -> pb2.Maps.Int32ToStrEntry
	20, // 5: pb2.Maps.str_to_nested:type_name -> pb2.Maps.StrToNestedEntry
	6,  // 6: pb2.Nested.opt_nested:type_name -> pb2.Nested
	6,  // 7: pb2.Nests.opt_nested:type_name -> pb2.Nested
	21, // 8: pb2.Nests.optgroup:type_name -> pb2.Nests.OptGroup
	6,  // 9: pb2.Nests.rpt_nested:type_name -> pb2.Nested
	22, // 10: pb2.Nests.rptgroup:type_name -> pb2.Nests.RptGroup
	0,  // 11: pb2.Requireds.req_enum:type_name -> pb2.Enum
	6,  // 12: pb2.Requireds.req_nested:type_name -> pb2.Nested
	10, // 13: pb2.IndirectRequired.opt_nested:type_name -> pb2.NestedWithRequired
	10, // 14: pb2.IndirectRequired.rpt_nested:type_name -> pb2.NestedWithRequired
	24, // 15: pb2.IndirectRequired.str_to_nested:type_name -> pb2.IndirectRequired.StrToNestedEntry
	10, // 16: pb2.IndirectRequired.oneof_nested:type_name -> pb2.NestedWithRequired
	25, // 17: pb2.KnownTypes.opt_bool:type_name -> google.protobuf.BoolValue
	26, // 18: pb2.KnownTypes.opt_int32:type_name -> google.protobuf.Int32Value
	27, // 19: pb2.KnownTypes.opt_int64:type_name -> google.protobuf.Int64Value
	28, // 20: pb2.KnownTypes.opt_uint32:type_name -> google.protobuf.UInt32Value
	29, // 21: pb2.KnownTypes.opt_uint64:type_name -> google.protobuf.UInt64Value
	30, // 22: pb2.KnownTypes.opt_float:type_name -> google.protobuf.FloatValue
	31, // 23: pb2.KnownTypes.opt_double:type_name -> google.protobuf.DoubleValue
	32, // 24: pb2.KnownTypes.opt_string:type_name -> google.protobuf.StringValue
	33, // 25: pb2.KnownTypes.opt_bytes:type_name -> google.protobuf.BytesValue
	34, // 26: pb2.KnownTypes.opt_duration:type_name -> google.protobuf.Duration
	35, // 27: pb2.KnownTypes.opt_timestamp:type_name -> google.protobuf.Timestamp
	36, // 28: pb2.KnownTypes.opt_struct:type_name -> google.protobuf.Struct
	37, // 29: pb2.KnownTypes.opt_list:type_name -> google.protobuf.ListValue
	38, // 30: pb2.KnownTypes.opt_value:type_name -> google.protobuf.Value
	39, // 31: pb2.KnownTypes.opt_null:type_name -> google.protobuf.NullValue
	40, // 32: pb2.KnownTypes.opt_empty:type_name -> google.protobuf.Empty
	41, // 33: pb2.KnownTypes.opt_any:type_name -> google.protobuf.Any
	42, // 34: pb2.KnownTypes.opt_fieldmask:type_name -> google.protobuf.FieldMask
	6,  // 35: pb2.Maps.StrToNestedEntry.value:type_name -> pb2.Nested
	6,  // 36: pb2.Nests.OptGroup.opt_nested:type_name -> pb2.Nested
	23, // 37: pb2.Nests.OptGroup.optnestedgroup:type_name -> pb2.Nests.OptGroup.OptNestedGroup
	10, // 38: pb2.IndirectRequired.StrToNestedEntry.value:type_name -> pb2.NestedWithRequired
	12, // 39: pb2.opt_ext_bool:extendee -> pb2.Extensions
	12, // 40: pb2.opt_ext_string:extendee -> pb2.Extensions
	12, // 41: pb2.opt_ext_enum:extendee -> pb2.Extensions
	12, // 42: pb2.opt_ext_nested:extendee -> pb2.Extensions
	12, // 43: pb2.opt_ext_partial:extendee -> pb2.Extensions
	12, // 44: pb2.rpt_ext_fixed32:extendee -> pb2.Extensions
	12, // 45: pb2.rpt_ext_enum:extendee -> pb2.Extensions
	12, // 46: pb2.rpt_ext_nested:extendee -> pb2.Extensions
	14, // 47: pb2.message_set_extension:extendee -> pb2.MessageSet
	12, // 48: pb2.ExtensionsContainer.opt_ext_bool:extendee -> pb2.Extensions
	12, // 49: pb2.ExtensionsContainer.opt_ext_string:extendee -> pb2.Extensions
	12, // 50: pb2.ExtensionsContainer.opt_ext_enum:extendee -> pb2.Extensions
	12, // 51: pb2.ExtensionsContainer.opt_ext_nested:extendee -> pb2.Extensions
	12, // 52: pb2.ExtensionsContainer.opt_ext_partial:extendee -> pb2.Extensions
	12, // 53: pb2.ExtensionsContainer.rpt_ext_string:extendee -> pb2.Extensions
	12, // 54: pb2.ExtensionsContainer.rpt_ext_enum:extendee -> pb2.Extensions
	12, // 55: pb2.ExtensionsContainer.rpt_ext_nested:extendee -> pb2.Extensions
	14, // 56: pb2.MessageSetExtension.message_set_extension:extendee -> pb2.MessageSet
	14, // 57: pb2.MessageSetExtension.not_message_set_extension:extendee -> pb2.MessageSet
	14, // 58: pb2.MessageSetExtension.ext_nested:extendee -> pb2.MessageSet
	16, // 59: pb2.FakeMessageSetExtension.message_set_extension:extendee -> pb2.FakeMessageSet
	0,  // 60: pb2.opt_ext_enum:type_name -> pb2.Enum
	6,  // 61: pb2.opt_ext_nested:type_name -> pb2.Nested
	9,  // 62: pb2.opt_ext_partial:type_name -> pb2.PartialRequired
	0,  // 63: pb2.rpt_ext_enum:type_name -> pb2.Enum
	6,  // 64: pb2.rpt_ext_nested:type_name -> pb2.Nested
	17, // 65: pb2.message_set_extension:type_name -> pb2.FakeMessageSetExtension
	0,  // 66: pb2.ExtensionsContainer.opt_ext_enum:type_name -> pb2.Enum
	6,  // 67: pb2.ExtensionsContainer.opt_ext_nested:type_name -> pb2.Nested
	9,  // 68: pb2.ExtensionsContainer.opt_ext_partial:type_name -> pb2.PartialRequired
	0,  // 69: pb2.ExtensionsContainer.rpt_ext_enum:type_name -> pb2.Enum
	6,  // 70: pb2.ExtensionsContainer.rpt_ext_nested:type_name -> pb2.Nested
	15, // 71: pb2.MessageSetExtension.message_set_extension:type_name -> pb2.MessageSetExtension
	15, // 72: pb2.MessageSetExtension.not_message_set_extension:type_name -> pb2.MessageSetExtension
	6,  // 73: pb2.MessageSetExtension.ext_nested:type_name -> pb2.Nested
	17, // 74: pb2.FakeMessageSetExtension.message_set_extension:type_name -> pb2.FakeMessageSetExtension
	75, // [75:75] is the sub-list for method output_type
	75, // [75:75] is the sub-list for method input_type
	60, // [60:75] is the sub-list for extension type_name
	39, // [39:60] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_internal_testprotos_textpb2_test_proto_init() }
func file_internal_testprotos_textpb2_test_proto_init() {
	if File_internal_testprotos_textpb2_test_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_textpb2_test_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scalars); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enums); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Repeats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Maps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nested); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Requireds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartialRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NestedWithRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndirectRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Extensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtensionsContainer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageSetExtension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FakeMessageSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FakeMessageSetExtension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnownTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests_OptGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests_RptGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_textpb2_test_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nests_OptGroup_OptNestedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_internal_testprotos_textpb2_test_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*IndirectRequired_OneofNested)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_textpb2_test_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   23,
			NumExtensions: 21,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_textpb2_test_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_textpb2_test_proto_depIdxs,
		EnumInfos:         file_internal_testprotos_textpb2_test_proto_enumTypes,
		MessageInfos:      file_internal_testprotos_textpb2_test_proto_msgTypes,
		ExtensionInfos:    file_internal_testprotos_textpb2_test_proto_extTypes,
	}.Build()
	File_internal_testprotos_textpb2_test_proto = out.File
	file_internal_testprotos_textpb2_test_proto_rawDesc = nil
	file_internal_testprotos_textpb2_test_proto_goTypes = nil
	file_internal_testprotos_textpb2_test_proto_depIdxs = nil
}
