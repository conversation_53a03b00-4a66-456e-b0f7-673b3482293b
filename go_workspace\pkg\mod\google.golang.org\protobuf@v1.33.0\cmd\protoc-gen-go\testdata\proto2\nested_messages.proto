// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

syntax = "proto2";

package goproto.protoc.proto2;

option go_package = "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/proto2";

message Layer1 {
  message Layer2 {
    message Layer3 {}
    optional Layer3 l3 = 1;
  }
  optional Layer2 l2 = 1;
  optional Layer2.Layer3 l3 = 2;
}
