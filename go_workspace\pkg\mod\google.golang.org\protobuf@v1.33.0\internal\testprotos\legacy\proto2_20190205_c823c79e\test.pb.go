// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto2_20190205_c823c79e/test.proto

package proto2_20190205_c823c79e

import (
	fmt "fmt"
	math "math"

	proto "google.golang.org/protobuf/internal/protolegacy"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SiblingEnum int32

const (
	SiblingEnum_ALPHA   SiblingEnum = 0
	SiblingEnum_BRAVO   SiblingEnum = 10
	SiblingEnum_CHARLIE SiblingEnum = 200
)

var SiblingEnum_name = map[int32]string{
	0:   "ALPHA",
	10:  "BRAVO",
	200: "CHARLIE",
}

var SiblingEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   10,
	"CHARLIE": 200,
}

func (x SiblingEnum) Enum() *SiblingEnum {
	p := new(SiblingEnum)
	*p = x
	return p
}

func (x SiblingEnum) String() string {
	return proto.EnumName(SiblingEnum_name, int32(x))
}

func (x *SiblingEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SiblingEnum_value, data, "SiblingEnum")
	if err != nil {
		return err
	}
	*x = SiblingEnum(value)
	return nil
}

func (SiblingEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{0}
}

type Message_ChildEnum int32

const (
	Message_ALPHA   Message_ChildEnum = 0
	Message_BRAVO   Message_ChildEnum = 1
	Message_CHARLIE Message_ChildEnum = 2
)

var Message_ChildEnum_name = map[int32]string{
	0: "ALPHA",
	1: "BRAVO",
	2: "CHARLIE",
}

var Message_ChildEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   1,
	"CHARLIE": 2,
}

func (x Message_ChildEnum) Enum() *Message_ChildEnum {
	p := new(Message_ChildEnum)
	*p = x
	return p
}

func (x Message_ChildEnum) String() string {
	return proto.EnumName(Message_ChildEnum_name, int32(x))
}

func (x *Message_ChildEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Message_ChildEnum_value, data, "Message_ChildEnum")
	if err != nil {
		return err
	}
	*x = Message_ChildEnum(value)
	return nil
}

func (Message_ChildEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 0}
}

type SiblingMessage struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	F4                   *Message `protobuf:"bytes,4,opt,name=f4" json:"f4,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SiblingMessage) Reset()         { *m = SiblingMessage{} }
func (m *SiblingMessage) String() string { return proto.CompactTextString(m) }
func (*SiblingMessage) ProtoMessage()    {}
func (*SiblingMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{0}
}

func (m *SiblingMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SiblingMessage.Unmarshal(m, b)
}
func (m *SiblingMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SiblingMessage.Marshal(b, m, deterministic)
}
func (m *SiblingMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SiblingMessage.Merge(m, src)
}
func (m *SiblingMessage) XXX_Size() int {
	return xxx_messageInfo_SiblingMessage.Size(m)
}
func (m *SiblingMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_SiblingMessage.DiscardUnknown(m)
}

var xxx_messageInfo_SiblingMessage proto.InternalMessageInfo

func (m *SiblingMessage) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *SiblingMessage) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *SiblingMessage) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func (m *SiblingMessage) GetF4() *Message {
	if m != nil {
		return m.F4
	}
	return nil
}

type Message struct {
	Namedgroup *Message_NamedGroup `protobuf:"group,1,opt,name=NamedGroup,json=namedgroup" json:"namedgroup,omitempty"`
	// Optional fields.
	OptionalBool           *bool                  `protobuf:"varint,100,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalInt32          *int32                 `protobuf:"varint,101,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalSint32         *int32                 `protobuf:"zigzag32,102,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalUint32         *uint32                `protobuf:"varint,103,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalInt64          *int64                 `protobuf:"varint,104,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalSint64         *int64                 `protobuf:"zigzag64,105,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalUint64         *uint64                `protobuf:"varint,106,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalFixed32        *uint32                `protobuf:"fixed32,107,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalSfixed32       *int32                 `protobuf:"fixed32,108,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalFloat          *float32               `protobuf:"fixed32,109,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalFixed64        *uint64                `protobuf:"fixed64,110,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed64       *int64                 `protobuf:"fixed64,111,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalDouble         *float64               `protobuf:"fixed64,112,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalString         *string                `protobuf:"bytes,113,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                 `protobuf:"bytes,114,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	OptionalChildEnum      *Message_ChildEnum     `protobuf:"varint,115,opt,name=optional_child_enum,json=optionalChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum" json:"optional_child_enum,omitempty"`
	OptionalChildMessage   *Message_ChildMessage  `protobuf:"bytes,116,opt,name=optional_child_message,json=optionalChildMessage" json:"optional_child_message,omitempty"`
	OptionalNamedGroup     *Message_NamedGroup    `protobuf:"bytes,117,opt,name=optional_named_group,json=optionalNamedGroup" json:"optional_named_group,omitempty"`
	OptionalSiblingEnum    *SiblingEnum           `protobuf:"varint,118,opt,name=optional_sibling_enum,json=optionalSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum" json:"optional_sibling_enum,omitempty"`
	OptionalSiblingMessage *SiblingMessage        `protobuf:"bytes,119,opt,name=optional_sibling_message,json=optionalSiblingMessage" json:"optional_sibling_message,omitempty"`
	Optionalgroup          *Message_OptionalGroup `protobuf:"group,120,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	// Optional default fields.
	DefaultedBool        *bool              `protobuf:"varint,200,opt,name=defaulted_bool,json=defaultedBool,def=1" json:"defaulted_bool,omitempty"`
	DefaultedInt32       *int32             `protobuf:"varint,201,opt,name=defaulted_int32,json=defaultedInt32,def=-12345" json:"defaulted_int32,omitempty"`
	DefaultedSint32      *int32             `protobuf:"zigzag32,202,opt,name=defaulted_sint32,json=defaultedSint32,def=-3200" json:"defaulted_sint32,omitempty"`
	DefaultedUint32      *uint32            `protobuf:"varint,203,opt,name=defaulted_uint32,json=defaultedUint32,def=3200" json:"defaulted_uint32,omitempty"`
	DefaultedInt64       *int64             `protobuf:"varint,204,opt,name=defaulted_int64,json=defaultedInt64,def=-123456789" json:"defaulted_int64,omitempty"`
	DefaultedSint64      *int64             `protobuf:"zigzag64,205,opt,name=defaulted_sint64,json=defaultedSint64,def=-6400" json:"defaulted_sint64,omitempty"`
	DefaultedUint64      *uint64            `protobuf:"varint,206,opt,name=defaulted_uint64,json=defaultedUint64,def=6400" json:"defaulted_uint64,omitempty"`
	DefaultedFixed32     *uint32            `protobuf:"fixed32,207,opt,name=defaulted_fixed32,json=defaultedFixed32,def=320000" json:"defaulted_fixed32,omitempty"`
	DefaultedSfixed32    *int32             `protobuf:"fixed32,208,opt,name=defaulted_sfixed32,json=defaultedSfixed32,def=-320000" json:"defaulted_sfixed32,omitempty"`
	DefaultedFloat       *float32           `protobuf:"fixed32,209,opt,name=defaulted_float,json=defaultedFloat,def=3.14159" json:"defaulted_float,omitempty"`
	DefaultedFixed64     *uint64            `protobuf:"fixed64,210,opt,name=defaulted_fixed64,json=defaultedFixed64,def=640000" json:"defaulted_fixed64,omitempty"`
	DefaultedSfixed64    *int64             `protobuf:"fixed64,211,opt,name=defaulted_sfixed64,json=defaultedSfixed64,def=-640000" json:"defaulted_sfixed64,omitempty"`
	DefaultedDouble      *float64           `protobuf:"fixed64,212,opt,name=defaulted_double,json=defaultedDouble,def=3.14159265359" json:"defaulted_double,omitempty"`
	DefaultedString      *string            `protobuf:"bytes,213,opt,name=defaulted_string,json=defaultedString,def=hello, \"world!\"\n" json:"defaulted_string,omitempty"`
	DefaultedBytes       []byte             `protobuf:"bytes,214,opt,name=defaulted_bytes,json=defaultedBytes,def=dead\\336\\255\\276\\357beef" json:"defaulted_bytes,omitempty"`
	DefaultedChildEnum   *Message_ChildEnum `protobuf:"varint,215,opt,name=defaulted_child_enum,json=defaultedChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum,def=0" json:"defaulted_child_enum,omitempty"`
	DefaultedSiblingEnum *SiblingEnum       `protobuf:"varint,216,opt,name=defaulted_sibling_enum,json=defaultedSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum,def=0" json:"defaulted_sibling_enum,omitempty"`
	// Required fields.
	RequiredBool           *bool                  `protobuf:"varint,300,req,name=required_bool,json=requiredBool" json:"required_bool,omitempty"`
	RequiredInt32          *int32                 `protobuf:"varint,301,req,name=required_int32,json=requiredInt32" json:"required_int32,omitempty"`
	RequiredSint32         *int32                 `protobuf:"zigzag32,302,req,name=required_sint32,json=requiredSint32" json:"required_sint32,omitempty"`
	RequiredUint32         *uint32                `protobuf:"varint,303,req,name=required_uint32,json=requiredUint32" json:"required_uint32,omitempty"`
	RequiredInt64          *int64                 `protobuf:"varint,304,req,name=required_int64,json=requiredInt64" json:"required_int64,omitempty"`
	RequiredSint64         *int64                 `protobuf:"zigzag64,305,req,name=required_sint64,json=requiredSint64" json:"required_sint64,omitempty"`
	RequiredUint64         *uint64                `protobuf:"varint,306,req,name=required_uint64,json=requiredUint64" json:"required_uint64,omitempty"`
	RequiredFixed32        *uint32                `protobuf:"fixed32,307,req,name=required_fixed32,json=requiredFixed32" json:"required_fixed32,omitempty"`
	RequiredSfixed32       *int32                 `protobuf:"fixed32,308,req,name=required_sfixed32,json=requiredSfixed32" json:"required_sfixed32,omitempty"`
	RequiredFloat          *float32               `protobuf:"fixed32,309,req,name=required_float,json=requiredFloat" json:"required_float,omitempty"`
	RequiredFixed64        *uint64                `protobuf:"fixed64,310,req,name=required_fixed64,json=requiredFixed64" json:"required_fixed64,omitempty"`
	RequiredSfixed64       *int64                 `protobuf:"fixed64,311,req,name=required_sfixed64,json=requiredSfixed64" json:"required_sfixed64,omitempty"`
	RequiredDouble         *float64               `protobuf:"fixed64,312,req,name=required_double,json=requiredDouble" json:"required_double,omitempty"`
	RequiredString         *string                `protobuf:"bytes,313,req,name=required_string,json=requiredString" json:"required_string,omitempty"`
	RequiredBytes          []byte                 `protobuf:"bytes,314,req,name=required_bytes,json=requiredBytes" json:"required_bytes,omitempty"`
	RequiredChildEnum      *Message_ChildEnum     `protobuf:"varint,315,req,name=required_child_enum,json=requiredChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum" json:"required_child_enum,omitempty"`
	RequiredChildMessage   *Message_ChildMessage  `protobuf:"bytes,316,req,name=required_child_message,json=requiredChildMessage" json:"required_child_message,omitempty"`
	RequiredNamedGroup     *Message_NamedGroup    `protobuf:"bytes,317,req,name=required_named_group,json=requiredNamedGroup" json:"required_named_group,omitempty"`
	RequiredSiblingEnum    *SiblingEnum           `protobuf:"varint,318,req,name=required_sibling_enum,json=requiredSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum" json:"required_sibling_enum,omitempty"`
	RequiredSiblingMessage *SiblingMessage        `protobuf:"bytes,319,req,name=required_sibling_message,json=requiredSiblingMessage" json:"required_sibling_message,omitempty"`
	Requiredgroup          *Message_RequiredGroup `protobuf:"group,320,req,name=RequiredGroup,json=requiredgroup" json:"requiredgroup,omitempty"`
	// Required default fields.
	RequiredDefaultedBool        *bool              `protobuf:"varint,400,req,name=required_defaulted_bool,json=requiredDefaultedBool,def=1" json:"required_defaulted_bool,omitempty"`
	RequiredDefaultedInt32       *int32             `protobuf:"varint,401,req,name=required_defaulted_int32,json=requiredDefaultedInt32,def=-12345" json:"required_defaulted_int32,omitempty"`
	RequiredDefaultedSint32      *int32             `protobuf:"zigzag32,402,req,name=required_defaulted_sint32,json=requiredDefaultedSint32,def=-3200" json:"required_defaulted_sint32,omitempty"`
	RequiredDefaultedUint32      *uint32            `protobuf:"varint,403,req,name=required_defaulted_uint32,json=requiredDefaultedUint32,def=3200" json:"required_defaulted_uint32,omitempty"`
	RequiredDefaultedInt64       *int64             `protobuf:"varint,404,req,name=required_defaulted_int64,json=requiredDefaultedInt64,def=-123456789" json:"required_defaulted_int64,omitempty"`
	RequiredDefaultedSint64      *int64             `protobuf:"zigzag64,405,req,name=required_defaulted_sint64,json=requiredDefaultedSint64,def=-6400" json:"required_defaulted_sint64,omitempty"`
	RequiredDefaultedUint64      *uint64            `protobuf:"varint,406,req,name=required_defaulted_uint64,json=requiredDefaultedUint64,def=6400" json:"required_defaulted_uint64,omitempty"`
	RequiredDefaultedFixed32     *uint32            `protobuf:"fixed32,407,req,name=required_defaulted_fixed32,json=requiredDefaultedFixed32,def=320000" json:"required_defaulted_fixed32,omitempty"`
	RequiredDefaultedSfixed32    *int32             `protobuf:"fixed32,408,req,name=required_defaulted_sfixed32,json=requiredDefaultedSfixed32,def=-320000" json:"required_defaulted_sfixed32,omitempty"`
	RequiredDefaultedFloat       *float32           `protobuf:"fixed32,409,req,name=required_defaulted_float,json=requiredDefaultedFloat,def=3.14159" json:"required_defaulted_float,omitempty"`
	RequiredDefaultedFixed64     *uint64            `protobuf:"fixed64,410,req,name=required_defaulted_fixed64,json=requiredDefaultedFixed64,def=640000" json:"required_defaulted_fixed64,omitempty"`
	RequiredDefaultedSfixed64    *int64             `protobuf:"fixed64,411,req,name=required_defaulted_sfixed64,json=requiredDefaultedSfixed64,def=-640000" json:"required_defaulted_sfixed64,omitempty"`
	RequiredDefaultedDouble      *float64           `protobuf:"fixed64,412,req,name=required_defaulted_double,json=requiredDefaultedDouble,def=3.14159265359" json:"required_defaulted_double,omitempty"`
	RequiredDefaultedString      *string            `protobuf:"bytes,413,req,name=required_defaulted_string,json=requiredDefaultedString,def=hello, \"world!\"\n" json:"required_defaulted_string,omitempty"`
	RequiredDefaultedBytes       []byte             `protobuf:"bytes,414,req,name=required_defaulted_bytes,json=requiredDefaultedBytes,def=dead\\336\\255\\276\\357beef" json:"required_defaulted_bytes,omitempty"`
	RequiredDefaultedChildEnum   *Message_ChildEnum `protobuf:"varint,415,req,name=required_defaulted_child_enum,json=requiredDefaultedChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum,def=0" json:"required_defaulted_child_enum,omitempty"`
	RequiredDefaultedSiblingEnum *SiblingEnum       `protobuf:"varint,416,req,name=required_defaulted_sibling_enum,json=requiredDefaultedSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum,def=0" json:"required_defaulted_sibling_enum,omitempty"`
	// Repeated fields.
	RepeatedBool           []bool                   `protobuf:"varint,500,rep,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedInt32          []int32                  `protobuf:"varint,501,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedSint32         []int32                  `protobuf:"zigzag32,502,rep,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedUint32         []uint32                 `protobuf:"varint,503,rep,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedInt64          []int64                  `protobuf:"varint,504,rep,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedSint64         []int64                  `protobuf:"zigzag64,505,rep,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedUint64         []uint64                 `protobuf:"varint,506,rep,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedFixed32        []uint32                 `protobuf:"fixed32,507,rep,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedSfixed32       []int32                  `protobuf:"fixed32,508,rep,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedFloat          []float32                `protobuf:"fixed32,509,rep,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedFixed64        []uint64                 `protobuf:"fixed64,510,rep,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed64       []int64                  `protobuf:"fixed64,511,rep,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedDouble         []float64                `protobuf:"fixed64,512,rep,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedString         []string                 `protobuf:"bytes,513,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                 `protobuf:"bytes,514,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	RepeatedChildEnum      []Message_ChildEnum      `protobuf:"varint,515,rep,name=repeated_child_enum,json=repeatedChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum" json:"repeated_child_enum,omitempty"`
	RepeatedChildMessage   []*Message_ChildMessage  `protobuf:"bytes,516,rep,name=repeated_child_message,json=repeatedChildMessage" json:"repeated_child_message,omitempty"`
	RepeatedNamedGroup     []*Message_NamedGroup    `protobuf:"bytes,517,rep,name=repeated_named_group,json=repeatedNamedGroup" json:"repeated_named_group,omitempty"`
	RepeatedSiblingEnum    []SiblingEnum            `protobuf:"varint,518,rep,name=repeated_sibling_enum,json=repeatedSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum" json:"repeated_sibling_enum,omitempty"`
	RepeatedSiblingMessage []*SiblingMessage        `protobuf:"bytes,519,rep,name=repeated_sibling_message,json=repeatedSiblingMessage" json:"repeated_sibling_message,omitempty"`
	Repeatedgroup          []*Message_RepeatedGroup `protobuf:"group,520,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
	// Map fields.
	MapBoolBool           map[bool]bool                  `protobuf:"bytes,600,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolInt32          map[bool]int32                 `protobuf:"bytes,601,rep,name=map_bool_int32,json=mapBoolInt32" json:"map_bool_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolSint32         map[bool]int32                 `protobuf:"bytes,602,rep,name=map_bool_sint32,json=mapBoolSint32" json:"map_bool_sint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapBoolUint32         map[bool]uint32                `protobuf:"bytes,603,rep,name=map_bool_uint32,json=mapBoolUint32" json:"map_bool_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolInt64          map[bool]int64                 `protobuf:"bytes,604,rep,name=map_bool_int64,json=mapBoolInt64" json:"map_bool_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolSint64         map[bool]int64                 `protobuf:"bytes,605,rep,name=map_bool_sint64,json=mapBoolSint64" json:"map_bool_sint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapBoolUint64         map[bool]uint64                `protobuf:"bytes,606,rep,name=map_bool_uint64,json=mapBoolUint64" json:"map_bool_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolFixed32        map[bool]uint32                `protobuf:"bytes,607,rep,name=map_bool_fixed32,json=mapBoolFixed32" json:"map_bool_fixed32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolSfixed32       map[bool]int32                 `protobuf:"bytes,608,rep,name=map_bool_sfixed32,json=mapBoolSfixed32" json:"map_bool_sfixed32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolFloat          map[bool]float32               `protobuf:"bytes,609,rep,name=map_bool_float,json=mapBoolFloat" json:"map_bool_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolFixed64        map[bool]uint64                `protobuf:"bytes,610,rep,name=map_bool_fixed64,json=mapBoolFixed64" json:"map_bool_fixed64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolSfixed64       map[bool]int64                 `protobuf:"bytes,611,rep,name=map_bool_sfixed64,json=mapBoolSfixed64" json:"map_bool_sfixed64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolDouble         map[bool]float64               `protobuf:"bytes,612,rep,name=map_bool_double,json=mapBoolDouble" json:"map_bool_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolString         map[bool]string                `protobuf:"bytes,613,rep,name=map_bool_string,json=mapBoolString" json:"map_bool_string,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolBytes          map[bool][]byte                `protobuf:"bytes,614,rep,name=map_bool_bytes,json=mapBoolBytes" json:"map_bool_bytes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolChildEnum      map[bool]Message_ChildEnum     `protobuf:"bytes,615,rep,name=map_bool_child_enum,json=mapBoolChildEnum" json:"map_bool_child_enum,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=google.golang.org.proto2_20190205.Message_ChildEnum"`
	MapBoolChildMessage   map[bool]*Message_ChildMessage `protobuf:"bytes,616,rep,name=map_bool_child_message,json=mapBoolChildMessage" json:"map_bool_child_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolNamedGroup     map[bool]*Message_NamedGroup   `protobuf:"bytes,617,rep,name=map_bool_named_group,json=mapBoolNamedGroup" json:"map_bool_named_group,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolSiblingEnum    map[bool]SiblingEnum           `protobuf:"bytes,618,rep,name=map_bool_sibling_enum,json=mapBoolSiblingEnum" json:"map_bool_sibling_enum,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=google.golang.org.proto2_20190205.SiblingEnum"`
	MapBoolSiblingMessage map[bool]*SiblingMessage       `protobuf:"bytes,619,rep,name=map_bool_sibling_message,json=mapBoolSiblingMessage" json:"map_bool_sibling_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapInt32Bool          map[int32]bool                 `protobuf:"bytes,620,rep,name=map_int32_bool,json=mapInt32Bool" json:"map_int32_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Bool         map[int32]bool                 `protobuf:"bytes,621,rep,name=map_sint32_bool,json=mapSint32Bool" json:"map_sint32_bool,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Bool         map[uint32]bool                `protobuf:"bytes,622,rep,name=map_uint32_bool,json=mapUint32Bool" json:"map_uint32_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Bool          map[int64]bool                 `protobuf:"bytes,623,rep,name=map_int64_bool,json=mapInt64Bool" json:"map_int64_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint64Bool         map[int64]bool                 `protobuf:"bytes,624,rep,name=map_sint64_bool,json=mapSint64Bool" json:"map_sint64_bool,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Bool         map[uint64]bool                `protobuf:"bytes,625,rep,name=map_uint64_bool,json=mapUint64Bool" json:"map_uint64_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapFixed32Bool        map[uint32]bool                `protobuf:"bytes,626,rep,name=map_fixed32_bool,json=mapFixed32Bool" json:"map_fixed32_bool,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringBool         map[string]bool                `protobuf:"bytes,627,rep,name=map_string_bool,json=mapStringBool" json:"map_string_bool,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// Oneof fields.
	//
	// Types that are valid to be assigned to OneofUnion:
	//	*Message_OneofBool
	//	*Message_OneofInt32
	//	*Message_OneofSint32
	//	*Message_OneofUint32
	//	*Message_OneofInt64
	//	*Message_OneofSint64
	//	*Message_OneofUint64
	//	*Message_OneofFixed32
	//	*Message_OneofSfixed32
	//	*Message_OneofFloat
	//	*Message_OneofFixed64
	//	*Message_OneofSfixed64
	//	*Message_OneofDouble
	//	*Message_OneofString
	//	*Message_OneofBytes
	//	*Message_OneofChildEnum
	//	*Message_OneofChildMessage
	//	*Message_OneofNamedGroup
	//	*Message_OneofSiblingEnum
	//	*Message_OneofSiblingMessage
	//	*Message_Oneofgroup
	//	*Message_OneofString1
	//	*Message_OneofString2
	//	*Message_OneofString3
	OneofUnion isMessage_OneofUnion `protobuf_oneof:"oneof_union"`
	// Oneof default fields.
	//
	// Types that are valid to be assigned to OneofDefaultedUnion:
	//	*Message_OneofDefaultedBool
	//	*Message_OneofDefaultedInt32
	//	*Message_OneofDefaultedSint32
	//	*Message_OneofDefaultedUint32
	//	*Message_OneofDefaultedInt64
	//	*Message_OneofDefaultedSint64
	//	*Message_OneofDefaultedUint64
	//	*Message_OneofDefaultedFixed32
	//	*Message_OneofDefaultedSfixed32
	//	*Message_OneofDefaultedFloat
	//	*Message_OneofDefaultedFixed64
	//	*Message_OneofDefaultedSfixed64
	//	*Message_OneofDefaultedDouble
	//	*Message_OneofDefaultedString
	//	*Message_OneofDefaultedBytes
	//	*Message_OneofDefaultedChildEnum
	//	*Message_OneofDefaultedSiblingEnum
	OneofDefaultedUnion          isMessage_OneofDefaultedUnion `protobuf_oneof:"oneof_defaulted_union"`
	XXX_NoUnkeyedLiteral         struct{}                      `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *Message) Reset()         { *m = Message{} }
func (m *Message) String() string { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()    {}
func (*Message) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1}
}

var extRange_Message = []proto.ExtensionRange{
	{Start: 10000, End: 536870911},
}

func (*Message) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_Message
}

func (m *Message) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message.Unmarshal(m, b)
}
func (m *Message) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message.Marshal(b, m, deterministic)
}
func (m *Message) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message.Merge(m, src)
}
func (m *Message) XXX_Size() int {
	return xxx_messageInfo_Message.Size(m)
}
func (m *Message) XXX_DiscardUnknown() {
	xxx_messageInfo_Message.DiscardUnknown(m)
}

var xxx_messageInfo_Message proto.InternalMessageInfo

const Default_Message_DefaultedBool bool = true
const Default_Message_DefaultedInt32 int32 = -12345
const Default_Message_DefaultedSint32 int32 = -3200
const Default_Message_DefaultedUint32 uint32 = 3200
const Default_Message_DefaultedInt64 int64 = -123456789
const Default_Message_DefaultedSint64 int64 = -6400
const Default_Message_DefaultedUint64 uint64 = 6400
const Default_Message_DefaultedFixed32 uint32 = 320000
const Default_Message_DefaultedSfixed32 int32 = -320000
const Default_Message_DefaultedFloat float32 = 3.14159
const Default_Message_DefaultedFixed64 uint64 = 640000
const Default_Message_DefaultedSfixed64 int64 = -640000
const Default_Message_DefaultedDouble float64 = 3.14159265359
const Default_Message_DefaultedString string = "hello, \"world!\"\n"

var Default_Message_DefaultedBytes []byte = []byte("deadޭ\xbe\xefbeef")

const Default_Message_DefaultedChildEnum Message_ChildEnum = Message_ALPHA
const Default_Message_DefaultedSiblingEnum SiblingEnum = SiblingEnum_ALPHA
const Default_Message_RequiredDefaultedBool bool = true
const Default_Message_RequiredDefaultedInt32 int32 = -12345
const Default_Message_RequiredDefaultedSint32 int32 = -3200
const Default_Message_RequiredDefaultedUint32 uint32 = 3200
const Default_Message_RequiredDefaultedInt64 int64 = -123456789
const Default_Message_RequiredDefaultedSint64 int64 = -6400
const Default_Message_RequiredDefaultedUint64 uint64 = 6400
const Default_Message_RequiredDefaultedFixed32 uint32 = 320000
const Default_Message_RequiredDefaultedSfixed32 int32 = -320000
const Default_Message_RequiredDefaultedFloat float32 = 3.14159
const Default_Message_RequiredDefaultedFixed64 uint64 = 640000
const Default_Message_RequiredDefaultedSfixed64 int64 = -640000
const Default_Message_RequiredDefaultedDouble float64 = 3.14159265359
const Default_Message_RequiredDefaultedString string = "hello, \"world!\"\n"

var Default_Message_RequiredDefaultedBytes []byte = []byte("deadޭ\xbe\xefbeef")

const Default_Message_RequiredDefaultedChildEnum Message_ChildEnum = Message_ALPHA
const Default_Message_RequiredDefaultedSiblingEnum SiblingEnum = SiblingEnum_ALPHA
const Default_Message_OneofDefaultedBool bool = true
const Default_Message_OneofDefaultedInt32 int32 = -12345
const Default_Message_OneofDefaultedSint32 int32 = -3200
const Default_Message_OneofDefaultedUint32 uint32 = 3200
const Default_Message_OneofDefaultedInt64 int64 = -123456789
const Default_Message_OneofDefaultedSint64 int64 = -6400
const Default_Message_OneofDefaultedUint64 uint64 = 6400
const Default_Message_OneofDefaultedFixed32 uint32 = 320000
const Default_Message_OneofDefaultedSfixed32 int32 = -320000
const Default_Message_OneofDefaultedFloat float32 = 3.14159
const Default_Message_OneofDefaultedFixed64 uint64 = 640000
const Default_Message_OneofDefaultedSfixed64 int64 = -640000
const Default_Message_OneofDefaultedDouble float64 = 3.14159265359
const Default_Message_OneofDefaultedString string = "hello, \"world!\"\n"

var Default_Message_OneofDefaultedBytes []byte = []byte("deadޭ\xbe\xefbeef")

const Default_Message_OneofDefaultedChildEnum Message_ChildEnum = Message_ALPHA
const Default_Message_OneofDefaultedSiblingEnum SiblingEnum = SiblingEnum_ALPHA

func (m *Message) GetNamedgroup() *Message_NamedGroup {
	if m != nil {
		return m.Namedgroup
	}
	return nil
}

func (m *Message) GetOptionalBool() bool {
	if m != nil && m.OptionalBool != nil {
		return *m.OptionalBool
	}
	return false
}

func (m *Message) GetOptionalInt32() int32 {
	if m != nil && m.OptionalInt32 != nil {
		return *m.OptionalInt32
	}
	return 0
}

func (m *Message) GetOptionalSint32() int32 {
	if m != nil && m.OptionalSint32 != nil {
		return *m.OptionalSint32
	}
	return 0
}

func (m *Message) GetOptionalUint32() uint32 {
	if m != nil && m.OptionalUint32 != nil {
		return *m.OptionalUint32
	}
	return 0
}

func (m *Message) GetOptionalInt64() int64 {
	if m != nil && m.OptionalInt64 != nil {
		return *m.OptionalInt64
	}
	return 0
}

func (m *Message) GetOptionalSint64() int64 {
	if m != nil && m.OptionalSint64 != nil {
		return *m.OptionalSint64
	}
	return 0
}

func (m *Message) GetOptionalUint64() uint64 {
	if m != nil && m.OptionalUint64 != nil {
		return *m.OptionalUint64
	}
	return 0
}

func (m *Message) GetOptionalFixed32() uint32 {
	if m != nil && m.OptionalFixed32 != nil {
		return *m.OptionalFixed32
	}
	return 0
}

func (m *Message) GetOptionalSfixed32() int32 {
	if m != nil && m.OptionalSfixed32 != nil {
		return *m.OptionalSfixed32
	}
	return 0
}

func (m *Message) GetOptionalFloat() float32 {
	if m != nil && m.OptionalFloat != nil {
		return *m.OptionalFloat
	}
	return 0
}

func (m *Message) GetOptionalFixed64() uint64 {
	if m != nil && m.OptionalFixed64 != nil {
		return *m.OptionalFixed64
	}
	return 0
}

func (m *Message) GetOptionalSfixed64() int64 {
	if m != nil && m.OptionalSfixed64 != nil {
		return *m.OptionalSfixed64
	}
	return 0
}

func (m *Message) GetOptionalDouble() float64 {
	if m != nil && m.OptionalDouble != nil {
		return *m.OptionalDouble
	}
	return 0
}

func (m *Message) GetOptionalString() string {
	if m != nil && m.OptionalString != nil {
		return *m.OptionalString
	}
	return ""
}

func (m *Message) GetOptionalBytes() []byte {
	if m != nil {
		return m.OptionalBytes
	}
	return nil
}

func (m *Message) GetOptionalChildEnum() Message_ChildEnum {
	if m != nil && m.OptionalChildEnum != nil {
		return *m.OptionalChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOptionalChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.OptionalChildMessage
	}
	return nil
}

func (m *Message) GetOptionalNamedGroup() *Message_NamedGroup {
	if m != nil {
		return m.OptionalNamedGroup
	}
	return nil
}

func (m *Message) GetOptionalSiblingEnum() SiblingEnum {
	if m != nil && m.OptionalSiblingEnum != nil {
		return *m.OptionalSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOptionalSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.OptionalSiblingMessage
	}
	return nil
}

func (m *Message) GetOptionalgroup() *Message_OptionalGroup {
	if m != nil {
		return m.Optionalgroup
	}
	return nil
}

func (m *Message) GetDefaultedBool() bool {
	if m != nil && m.DefaultedBool != nil {
		return *m.DefaultedBool
	}
	return Default_Message_DefaultedBool
}

func (m *Message) GetDefaultedInt32() int32 {
	if m != nil && m.DefaultedInt32 != nil {
		return *m.DefaultedInt32
	}
	return Default_Message_DefaultedInt32
}

func (m *Message) GetDefaultedSint32() int32 {
	if m != nil && m.DefaultedSint32 != nil {
		return *m.DefaultedSint32
	}
	return Default_Message_DefaultedSint32
}

func (m *Message) GetDefaultedUint32() uint32 {
	if m != nil && m.DefaultedUint32 != nil {
		return *m.DefaultedUint32
	}
	return Default_Message_DefaultedUint32
}

func (m *Message) GetDefaultedInt64() int64 {
	if m != nil && m.DefaultedInt64 != nil {
		return *m.DefaultedInt64
	}
	return Default_Message_DefaultedInt64
}

func (m *Message) GetDefaultedSint64() int64 {
	if m != nil && m.DefaultedSint64 != nil {
		return *m.DefaultedSint64
	}
	return Default_Message_DefaultedSint64
}

func (m *Message) GetDefaultedUint64() uint64 {
	if m != nil && m.DefaultedUint64 != nil {
		return *m.DefaultedUint64
	}
	return Default_Message_DefaultedUint64
}

func (m *Message) GetDefaultedFixed32() uint32 {
	if m != nil && m.DefaultedFixed32 != nil {
		return *m.DefaultedFixed32
	}
	return Default_Message_DefaultedFixed32
}

func (m *Message) GetDefaultedSfixed32() int32 {
	if m != nil && m.DefaultedSfixed32 != nil {
		return *m.DefaultedSfixed32
	}
	return Default_Message_DefaultedSfixed32
}

func (m *Message) GetDefaultedFloat() float32 {
	if m != nil && m.DefaultedFloat != nil {
		return *m.DefaultedFloat
	}
	return Default_Message_DefaultedFloat
}

func (m *Message) GetDefaultedFixed64() uint64 {
	if m != nil && m.DefaultedFixed64 != nil {
		return *m.DefaultedFixed64
	}
	return Default_Message_DefaultedFixed64
}

func (m *Message) GetDefaultedSfixed64() int64 {
	if m != nil && m.DefaultedSfixed64 != nil {
		return *m.DefaultedSfixed64
	}
	return Default_Message_DefaultedSfixed64
}

func (m *Message) GetDefaultedDouble() float64 {
	if m != nil && m.DefaultedDouble != nil {
		return *m.DefaultedDouble
	}
	return Default_Message_DefaultedDouble
}

func (m *Message) GetDefaultedString() string {
	if m != nil && m.DefaultedString != nil {
		return *m.DefaultedString
	}
	return Default_Message_DefaultedString
}

func (m *Message) GetDefaultedBytes() []byte {
	if m != nil && m.DefaultedBytes != nil {
		return m.DefaultedBytes
	}
	return append([]byte(nil), Default_Message_DefaultedBytes...)
}

func (m *Message) GetDefaultedChildEnum() Message_ChildEnum {
	if m != nil && m.DefaultedChildEnum != nil {
		return *m.DefaultedChildEnum
	}
	return Default_Message_DefaultedChildEnum
}

func (m *Message) GetDefaultedSiblingEnum() SiblingEnum {
	if m != nil && m.DefaultedSiblingEnum != nil {
		return *m.DefaultedSiblingEnum
	}
	return Default_Message_DefaultedSiblingEnum
}

func (m *Message) GetRequiredBool() bool {
	if m != nil && m.RequiredBool != nil {
		return *m.RequiredBool
	}
	return false
}

func (m *Message) GetRequiredInt32() int32 {
	if m != nil && m.RequiredInt32 != nil {
		return *m.RequiredInt32
	}
	return 0
}

func (m *Message) GetRequiredSint32() int32 {
	if m != nil && m.RequiredSint32 != nil {
		return *m.RequiredSint32
	}
	return 0
}

func (m *Message) GetRequiredUint32() uint32 {
	if m != nil && m.RequiredUint32 != nil {
		return *m.RequiredUint32
	}
	return 0
}

func (m *Message) GetRequiredInt64() int64 {
	if m != nil && m.RequiredInt64 != nil {
		return *m.RequiredInt64
	}
	return 0
}

func (m *Message) GetRequiredSint64() int64 {
	if m != nil && m.RequiredSint64 != nil {
		return *m.RequiredSint64
	}
	return 0
}

func (m *Message) GetRequiredUint64() uint64 {
	if m != nil && m.RequiredUint64 != nil {
		return *m.RequiredUint64
	}
	return 0
}

func (m *Message) GetRequiredFixed32() uint32 {
	if m != nil && m.RequiredFixed32 != nil {
		return *m.RequiredFixed32
	}
	return 0
}

func (m *Message) GetRequiredSfixed32() int32 {
	if m != nil && m.RequiredSfixed32 != nil {
		return *m.RequiredSfixed32
	}
	return 0
}

func (m *Message) GetRequiredFloat() float32 {
	if m != nil && m.RequiredFloat != nil {
		return *m.RequiredFloat
	}
	return 0
}

func (m *Message) GetRequiredFixed64() uint64 {
	if m != nil && m.RequiredFixed64 != nil {
		return *m.RequiredFixed64
	}
	return 0
}

func (m *Message) GetRequiredSfixed64() int64 {
	if m != nil && m.RequiredSfixed64 != nil {
		return *m.RequiredSfixed64
	}
	return 0
}

func (m *Message) GetRequiredDouble() float64 {
	if m != nil && m.RequiredDouble != nil {
		return *m.RequiredDouble
	}
	return 0
}

func (m *Message) GetRequiredString() string {
	if m != nil && m.RequiredString != nil {
		return *m.RequiredString
	}
	return ""
}

func (m *Message) GetRequiredBytes() []byte {
	if m != nil {
		return m.RequiredBytes
	}
	return nil
}

func (m *Message) GetRequiredChildEnum() Message_ChildEnum {
	if m != nil && m.RequiredChildEnum != nil {
		return *m.RequiredChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetRequiredChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.RequiredChildMessage
	}
	return nil
}

func (m *Message) GetRequiredNamedGroup() *Message_NamedGroup {
	if m != nil {
		return m.RequiredNamedGroup
	}
	return nil
}

func (m *Message) GetRequiredSiblingEnum() SiblingEnum {
	if m != nil && m.RequiredSiblingEnum != nil {
		return *m.RequiredSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetRequiredSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.RequiredSiblingMessage
	}
	return nil
}

func (m *Message) GetRequiredgroup() *Message_RequiredGroup {
	if m != nil {
		return m.Requiredgroup
	}
	return nil
}

func (m *Message) GetRequiredDefaultedBool() bool {
	if m != nil && m.RequiredDefaultedBool != nil {
		return *m.RequiredDefaultedBool
	}
	return Default_Message_RequiredDefaultedBool
}

func (m *Message) GetRequiredDefaultedInt32() int32 {
	if m != nil && m.RequiredDefaultedInt32 != nil {
		return *m.RequiredDefaultedInt32
	}
	return Default_Message_RequiredDefaultedInt32
}

func (m *Message) GetRequiredDefaultedSint32() int32 {
	if m != nil && m.RequiredDefaultedSint32 != nil {
		return *m.RequiredDefaultedSint32
	}
	return Default_Message_RequiredDefaultedSint32
}

func (m *Message) GetRequiredDefaultedUint32() uint32 {
	if m != nil && m.RequiredDefaultedUint32 != nil {
		return *m.RequiredDefaultedUint32
	}
	return Default_Message_RequiredDefaultedUint32
}

func (m *Message) GetRequiredDefaultedInt64() int64 {
	if m != nil && m.RequiredDefaultedInt64 != nil {
		return *m.RequiredDefaultedInt64
	}
	return Default_Message_RequiredDefaultedInt64
}

func (m *Message) GetRequiredDefaultedSint64() int64 {
	if m != nil && m.RequiredDefaultedSint64 != nil {
		return *m.RequiredDefaultedSint64
	}
	return Default_Message_RequiredDefaultedSint64
}

func (m *Message) GetRequiredDefaultedUint64() uint64 {
	if m != nil && m.RequiredDefaultedUint64 != nil {
		return *m.RequiredDefaultedUint64
	}
	return Default_Message_RequiredDefaultedUint64
}

func (m *Message) GetRequiredDefaultedFixed32() uint32 {
	if m != nil && m.RequiredDefaultedFixed32 != nil {
		return *m.RequiredDefaultedFixed32
	}
	return Default_Message_RequiredDefaultedFixed32
}

func (m *Message) GetRequiredDefaultedSfixed32() int32 {
	if m != nil && m.RequiredDefaultedSfixed32 != nil {
		return *m.RequiredDefaultedSfixed32
	}
	return Default_Message_RequiredDefaultedSfixed32
}

func (m *Message) GetRequiredDefaultedFloat() float32 {
	if m != nil && m.RequiredDefaultedFloat != nil {
		return *m.RequiredDefaultedFloat
	}
	return Default_Message_RequiredDefaultedFloat
}

func (m *Message) GetRequiredDefaultedFixed64() uint64 {
	if m != nil && m.RequiredDefaultedFixed64 != nil {
		return *m.RequiredDefaultedFixed64
	}
	return Default_Message_RequiredDefaultedFixed64
}

func (m *Message) GetRequiredDefaultedSfixed64() int64 {
	if m != nil && m.RequiredDefaultedSfixed64 != nil {
		return *m.RequiredDefaultedSfixed64
	}
	return Default_Message_RequiredDefaultedSfixed64
}

func (m *Message) GetRequiredDefaultedDouble() float64 {
	if m != nil && m.RequiredDefaultedDouble != nil {
		return *m.RequiredDefaultedDouble
	}
	return Default_Message_RequiredDefaultedDouble
}

func (m *Message) GetRequiredDefaultedString() string {
	if m != nil && m.RequiredDefaultedString != nil {
		return *m.RequiredDefaultedString
	}
	return Default_Message_RequiredDefaultedString
}

func (m *Message) GetRequiredDefaultedBytes() []byte {
	if m != nil && m.RequiredDefaultedBytes != nil {
		return m.RequiredDefaultedBytes
	}
	return append([]byte(nil), Default_Message_RequiredDefaultedBytes...)
}

func (m *Message) GetRequiredDefaultedChildEnum() Message_ChildEnum {
	if m != nil && m.RequiredDefaultedChildEnum != nil {
		return *m.RequiredDefaultedChildEnum
	}
	return Default_Message_RequiredDefaultedChildEnum
}

func (m *Message) GetRequiredDefaultedSiblingEnum() SiblingEnum {
	if m != nil && m.RequiredDefaultedSiblingEnum != nil {
		return *m.RequiredDefaultedSiblingEnum
	}
	return Default_Message_RequiredDefaultedSiblingEnum
}

func (m *Message) GetRepeatedBool() []bool {
	if m != nil {
		return m.RepeatedBool
	}
	return nil
}

func (m *Message) GetRepeatedInt32() []int32 {
	if m != nil {
		return m.RepeatedInt32
	}
	return nil
}

func (m *Message) GetRepeatedSint32() []int32 {
	if m != nil {
		return m.RepeatedSint32
	}
	return nil
}

func (m *Message) GetRepeatedUint32() []uint32 {
	if m != nil {
		return m.RepeatedUint32
	}
	return nil
}

func (m *Message) GetRepeatedInt64() []int64 {
	if m != nil {
		return m.RepeatedInt64
	}
	return nil
}

func (m *Message) GetRepeatedSint64() []int64 {
	if m != nil {
		return m.RepeatedSint64
	}
	return nil
}

func (m *Message) GetRepeatedUint64() []uint64 {
	if m != nil {
		return m.RepeatedUint64
	}
	return nil
}

func (m *Message) GetRepeatedFixed32() []uint32 {
	if m != nil {
		return m.RepeatedFixed32
	}
	return nil
}

func (m *Message) GetRepeatedSfixed32() []int32 {
	if m != nil {
		return m.RepeatedSfixed32
	}
	return nil
}

func (m *Message) GetRepeatedFloat() []float32 {
	if m != nil {
		return m.RepeatedFloat
	}
	return nil
}

func (m *Message) GetRepeatedFixed64() []uint64 {
	if m != nil {
		return m.RepeatedFixed64
	}
	return nil
}

func (m *Message) GetRepeatedSfixed64() []int64 {
	if m != nil {
		return m.RepeatedSfixed64
	}
	return nil
}

func (m *Message) GetRepeatedDouble() []float64 {
	if m != nil {
		return m.RepeatedDouble
	}
	return nil
}

func (m *Message) GetRepeatedString() []string {
	if m != nil {
		return m.RepeatedString
	}
	return nil
}

func (m *Message) GetRepeatedBytes() [][]byte {
	if m != nil {
		return m.RepeatedBytes
	}
	return nil
}

func (m *Message) GetRepeatedChildEnum() []Message_ChildEnum {
	if m != nil {
		return m.RepeatedChildEnum
	}
	return nil
}

func (m *Message) GetRepeatedChildMessage() []*Message_ChildMessage {
	if m != nil {
		return m.RepeatedChildMessage
	}
	return nil
}

func (m *Message) GetRepeatedNamedGroup() []*Message_NamedGroup {
	if m != nil {
		return m.RepeatedNamedGroup
	}
	return nil
}

func (m *Message) GetRepeatedSiblingEnum() []SiblingEnum {
	if m != nil {
		return m.RepeatedSiblingEnum
	}
	return nil
}

func (m *Message) GetRepeatedSiblingMessage() []*SiblingMessage {
	if m != nil {
		return m.RepeatedSiblingMessage
	}
	return nil
}

func (m *Message) GetRepeatedgroup() []*Message_RepeatedGroup {
	if m != nil {
		return m.Repeatedgroup
	}
	return nil
}

func (m *Message) GetMapBoolBool() map[bool]bool {
	if m != nil {
		return m.MapBoolBool
	}
	return nil
}

func (m *Message) GetMapBoolInt32() map[bool]int32 {
	if m != nil {
		return m.MapBoolInt32
	}
	return nil
}

func (m *Message) GetMapBoolSint32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSint32
	}
	return nil
}

func (m *Message) GetMapBoolUint32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolUint32
	}
	return nil
}

func (m *Message) GetMapBoolInt64() map[bool]int64 {
	if m != nil {
		return m.MapBoolInt64
	}
	return nil
}

func (m *Message) GetMapBoolSint64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSint64
	}
	return nil
}

func (m *Message) GetMapBoolUint64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolUint64
	}
	return nil
}

func (m *Message) GetMapBoolFixed32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolFixed32
	}
	return nil
}

func (m *Message) GetMapBoolSfixed32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSfixed32
	}
	return nil
}

func (m *Message) GetMapBoolFloat() map[bool]float32 {
	if m != nil {
		return m.MapBoolFloat
	}
	return nil
}

func (m *Message) GetMapBoolFixed64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolFixed64
	}
	return nil
}

func (m *Message) GetMapBoolSfixed64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSfixed64
	}
	return nil
}

func (m *Message) GetMapBoolDouble() map[bool]float64 {
	if m != nil {
		return m.MapBoolDouble
	}
	return nil
}

func (m *Message) GetMapBoolString() map[bool]string {
	if m != nil {
		return m.MapBoolString
	}
	return nil
}

func (m *Message) GetMapBoolBytes() map[bool][]byte {
	if m != nil {
		return m.MapBoolBytes
	}
	return nil
}

func (m *Message) GetMapBoolChildEnum() map[bool]Message_ChildEnum {
	if m != nil {
		return m.MapBoolChildEnum
	}
	return nil
}

func (m *Message) GetMapBoolChildMessage() map[bool]*Message_ChildMessage {
	if m != nil {
		return m.MapBoolChildMessage
	}
	return nil
}

func (m *Message) GetMapBoolNamedGroup() map[bool]*Message_NamedGroup {
	if m != nil {
		return m.MapBoolNamedGroup
	}
	return nil
}

func (m *Message) GetMapBoolSiblingEnum() map[bool]SiblingEnum {
	if m != nil {
		return m.MapBoolSiblingEnum
	}
	return nil
}

func (m *Message) GetMapBoolSiblingMessage() map[bool]*SiblingMessage {
	if m != nil {
		return m.MapBoolSiblingMessage
	}
	return nil
}

func (m *Message) GetMapInt32Bool() map[int32]bool {
	if m != nil {
		return m.MapInt32Bool
	}
	return nil
}

func (m *Message) GetMapSint32Bool() map[int32]bool {
	if m != nil {
		return m.MapSint32Bool
	}
	return nil
}

func (m *Message) GetMapUint32Bool() map[uint32]bool {
	if m != nil {
		return m.MapUint32Bool
	}
	return nil
}

func (m *Message) GetMapInt64Bool() map[int64]bool {
	if m != nil {
		return m.MapInt64Bool
	}
	return nil
}

func (m *Message) GetMapSint64Bool() map[int64]bool {
	if m != nil {
		return m.MapSint64Bool
	}
	return nil
}

func (m *Message) GetMapUint64Bool() map[uint64]bool {
	if m != nil {
		return m.MapUint64Bool
	}
	return nil
}

func (m *Message) GetMapFixed32Bool() map[uint32]bool {
	if m != nil {
		return m.MapFixed32Bool
	}
	return nil
}

func (m *Message) GetMapStringBool() map[string]bool {
	if m != nil {
		return m.MapStringBool
	}
	return nil
}

type isMessage_OneofUnion interface {
	isMessage_OneofUnion()
}

type Message_OneofBool struct {
	OneofBool bool `protobuf:"varint,700,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type Message_OneofInt32 struct {
	OneofInt32 int32 `protobuf:"varint,701,opt,name=oneof_int32,json=oneofInt32,oneof"`
}

type Message_OneofSint32 struct {
	OneofSint32 int32 `protobuf:"zigzag32,702,opt,name=oneof_sint32,json=oneofSint32,oneof"`
}

type Message_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,703,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type Message_OneofInt64 struct {
	OneofInt64 int64 `protobuf:"varint,704,opt,name=oneof_int64,json=oneofInt64,oneof"`
}

type Message_OneofSint64 struct {
	OneofSint64 int64 `protobuf:"zigzag64,705,opt,name=oneof_sint64,json=oneofSint64,oneof"`
}

type Message_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,706,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type Message_OneofFixed32 struct {
	OneofFixed32 uint32 `protobuf:"fixed32,707,opt,name=oneof_fixed32,json=oneofFixed32,oneof"`
}

type Message_OneofSfixed32 struct {
	OneofSfixed32 int32 `protobuf:"fixed32,708,opt,name=oneof_sfixed32,json=oneofSfixed32,oneof"`
}

type Message_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,709,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type Message_OneofFixed64 struct {
	OneofFixed64 uint64 `protobuf:"fixed64,710,opt,name=oneof_fixed64,json=oneofFixed64,oneof"`
}

type Message_OneofSfixed64 struct {
	OneofSfixed64 int64 `protobuf:"fixed64,711,opt,name=oneof_sfixed64,json=oneofSfixed64,oneof"`
}

type Message_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,712,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type Message_OneofString struct {
	OneofString string `protobuf:"bytes,713,opt,name=oneof_string,json=oneofString,oneof"`
}

type Message_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,714,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type Message_OneofChildEnum struct {
	OneofChildEnum Message_ChildEnum `protobuf:"varint,715,opt,name=oneof_child_enum,json=oneofChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum,oneof"`
}

type Message_OneofChildMessage struct {
	OneofChildMessage *Message_ChildMessage `protobuf:"bytes,716,opt,name=oneof_child_message,json=oneofChildMessage,oneof"`
}

type Message_OneofNamedGroup struct {
	OneofNamedGroup *Message_NamedGroup `protobuf:"bytes,717,opt,name=oneof_named_group,json=oneofNamedGroup,oneof"`
}

type Message_OneofSiblingEnum struct {
	OneofSiblingEnum SiblingEnum `protobuf:"varint,718,opt,name=oneof_sibling_enum,json=oneofSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum,oneof"`
}

type Message_OneofSiblingMessage struct {
	OneofSiblingMessage *SiblingMessage `protobuf:"bytes,719,opt,name=oneof_sibling_message,json=oneofSiblingMessage,oneof"`
}

type Message_Oneofgroup struct {
	Oneofgroup *Message_OneofGroup `protobuf:"group,720,opt,name=OneofGroup,json=oneofgroup,oneof"`
}

type Message_OneofString1 struct {
	OneofString1 string `protobuf:"bytes,721,opt,name=oneof_string1,json=oneofString1,oneof"`
}

type Message_OneofString2 struct {
	OneofString2 string `protobuf:"bytes,722,opt,name=oneof_string2,json=oneofString2,oneof"`
}

type Message_OneofString3 struct {
	OneofString3 string `protobuf:"bytes,723,opt,name=oneof_string3,json=oneofString3,oneof"`
}

func (*Message_OneofBool) isMessage_OneofUnion() {}

func (*Message_OneofInt32) isMessage_OneofUnion() {}

func (*Message_OneofSint32) isMessage_OneofUnion() {}

func (*Message_OneofUint32) isMessage_OneofUnion() {}

func (*Message_OneofInt64) isMessage_OneofUnion() {}

func (*Message_OneofSint64) isMessage_OneofUnion() {}

func (*Message_OneofUint64) isMessage_OneofUnion() {}

func (*Message_OneofFixed32) isMessage_OneofUnion() {}

func (*Message_OneofSfixed32) isMessage_OneofUnion() {}

func (*Message_OneofFloat) isMessage_OneofUnion() {}

func (*Message_OneofFixed64) isMessage_OneofUnion() {}

func (*Message_OneofSfixed64) isMessage_OneofUnion() {}

func (*Message_OneofDouble) isMessage_OneofUnion() {}

func (*Message_OneofString) isMessage_OneofUnion() {}

func (*Message_OneofBytes) isMessage_OneofUnion() {}

func (*Message_OneofChildEnum) isMessage_OneofUnion() {}

func (*Message_OneofChildMessage) isMessage_OneofUnion() {}

func (*Message_OneofNamedGroup) isMessage_OneofUnion() {}

func (*Message_OneofSiblingEnum) isMessage_OneofUnion() {}

func (*Message_OneofSiblingMessage) isMessage_OneofUnion() {}

func (*Message_Oneofgroup) isMessage_OneofUnion() {}

func (*Message_OneofString1) isMessage_OneofUnion() {}

func (*Message_OneofString2) isMessage_OneofUnion() {}

func (*Message_OneofString3) isMessage_OneofUnion() {}

func (m *Message) GetOneofUnion() isMessage_OneofUnion {
	if m != nil {
		return m.OneofUnion
	}
	return nil
}

func (m *Message) GetOneofBool() bool {
	if x, ok := m.GetOneofUnion().(*Message_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (m *Message) GetOneofInt32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt32); ok {
		return x.OneofInt32
	}
	return 0
}

func (m *Message) GetOneofSint32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint32); ok {
		return x.OneofSint32
	}
	return 0
}

func (m *Message) GetOneofUint32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (m *Message) GetOneofInt64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt64); ok {
		return x.OneofInt64
	}
	return 0
}

func (m *Message) GetOneofSint64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint64); ok {
		return x.OneofSint64
	}
	return 0
}

func (m *Message) GetOneofUint64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (m *Message) GetOneofFixed32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed32); ok {
		return x.OneofFixed32
	}
	return 0
}

func (m *Message) GetOneofSfixed32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed32); ok {
		return x.OneofSfixed32
	}
	return 0
}

func (m *Message) GetOneofFloat() float32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (m *Message) GetOneofFixed64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed64); ok {
		return x.OneofFixed64
	}
	return 0
}

func (m *Message) GetOneofSfixed64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed64); ok {
		return x.OneofSfixed64
	}
	return 0
}

func (m *Message) GetOneofDouble() float64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (m *Message) GetOneofString() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (m *Message) GetOneofBytes() []byte {
	if x, ok := m.GetOneofUnion().(*Message_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (m *Message) GetOneofChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildEnum); ok {
		return x.OneofChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOneofChildMessage() *Message_ChildMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildMessage); ok {
		return x.OneofChildMessage
	}
	return nil
}

func (m *Message) GetOneofNamedGroup() *Message_NamedGroup {
	if x, ok := m.GetOneofUnion().(*Message_OneofNamedGroup); ok {
		return x.OneofNamedGroup
	}
	return nil
}

func (m *Message) GetOneofSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingEnum); ok {
		return x.OneofSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOneofSiblingMessage() *SiblingMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingMessage); ok {
		return x.OneofSiblingMessage
	}
	return nil
}

func (m *Message) GetOneofgroup() *Message_OneofGroup {
	if x, ok := m.GetOneofUnion().(*Message_Oneofgroup); ok {
		return x.Oneofgroup
	}
	return nil
}

func (m *Message) GetOneofString1() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString1); ok {
		return x.OneofString1
	}
	return ""
}

func (m *Message) GetOneofString2() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString2); ok {
		return x.OneofString2
	}
	return ""
}

func (m *Message) GetOneofString3() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString3); ok {
		return x.OneofString3
	}
	return ""
}

type isMessage_OneofDefaultedUnion interface {
	isMessage_OneofDefaultedUnion()
}

type Message_OneofDefaultedBool struct {
	OneofDefaultedBool bool `protobuf:"varint,800,opt,name=oneof_defaulted_bool,json=oneofDefaultedBool,oneof,def=1"`
}

type Message_OneofDefaultedInt32 struct {
	OneofDefaultedInt32 int32 `protobuf:"varint,801,opt,name=oneof_defaulted_int32,json=oneofDefaultedInt32,oneof,def=-12345"`
}

type Message_OneofDefaultedSint32 struct {
	OneofDefaultedSint32 int32 `protobuf:"zigzag32,802,opt,name=oneof_defaulted_sint32,json=oneofDefaultedSint32,oneof,def=-3200"`
}

type Message_OneofDefaultedUint32 struct {
	OneofDefaultedUint32 uint32 `protobuf:"varint,803,opt,name=oneof_defaulted_uint32,json=oneofDefaultedUint32,oneof,def=3200"`
}

type Message_OneofDefaultedInt64 struct {
	OneofDefaultedInt64 int64 `protobuf:"varint,804,opt,name=oneof_defaulted_int64,json=oneofDefaultedInt64,oneof,def=-123456789"`
}

type Message_OneofDefaultedSint64 struct {
	OneofDefaultedSint64 int64 `protobuf:"zigzag64,805,opt,name=oneof_defaulted_sint64,json=oneofDefaultedSint64,oneof,def=-6400"`
}

type Message_OneofDefaultedUint64 struct {
	OneofDefaultedUint64 uint64 `protobuf:"varint,806,opt,name=oneof_defaulted_uint64,json=oneofDefaultedUint64,oneof,def=6400"`
}

type Message_OneofDefaultedFixed32 struct {
	OneofDefaultedFixed32 uint32 `protobuf:"fixed32,807,opt,name=oneof_defaulted_fixed32,json=oneofDefaultedFixed32,oneof,def=320000"`
}

type Message_OneofDefaultedSfixed32 struct {
	OneofDefaultedSfixed32 int32 `protobuf:"fixed32,808,opt,name=oneof_defaulted_sfixed32,json=oneofDefaultedSfixed32,oneof,def=-320000"`
}

type Message_OneofDefaultedFloat struct {
	OneofDefaultedFloat float32 `protobuf:"fixed32,809,opt,name=oneof_defaulted_float,json=oneofDefaultedFloat,oneof,def=3.14159"`
}

type Message_OneofDefaultedFixed64 struct {
	OneofDefaultedFixed64 uint64 `protobuf:"fixed64,810,opt,name=oneof_defaulted_fixed64,json=oneofDefaultedFixed64,oneof,def=640000"`
}

type Message_OneofDefaultedSfixed64 struct {
	OneofDefaultedSfixed64 int64 `protobuf:"fixed64,811,opt,name=oneof_defaulted_sfixed64,json=oneofDefaultedSfixed64,oneof,def=-640000"`
}

type Message_OneofDefaultedDouble struct {
	OneofDefaultedDouble float64 `protobuf:"fixed64,812,opt,name=oneof_defaulted_double,json=oneofDefaultedDouble,oneof,def=3.14159265359"`
}

type Message_OneofDefaultedString struct {
	OneofDefaultedString string `protobuf:"bytes,813,opt,name=oneof_defaulted_string,json=oneofDefaultedString,oneof,def=hello, \"world!\"\n"`
}

type Message_OneofDefaultedBytes struct {
	OneofDefaultedBytes []byte `protobuf:"bytes,814,opt,name=oneof_defaulted_bytes,json=oneofDefaultedBytes,oneof,def=dead\\336\\255\\276\\357beef"`
}

type Message_OneofDefaultedChildEnum struct {
	OneofDefaultedChildEnum Message_ChildEnum `protobuf:"varint,815,opt,name=oneof_defaulted_child_enum,json=oneofDefaultedChildEnum,enum=google.golang.org.proto2_20190205.Message_ChildEnum,oneof,def=0"`
}

type Message_OneofDefaultedSiblingEnum struct {
	OneofDefaultedSiblingEnum SiblingEnum `protobuf:"varint,816,opt,name=oneof_defaulted_sibling_enum,json=oneofDefaultedSiblingEnum,enum=google.golang.org.proto2_20190205.SiblingEnum,oneof,def=0"`
}

func (*Message_OneofDefaultedBool) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedInt32) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedSint32) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedUint32) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedInt64) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedSint64) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedUint64) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedFixed32) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedSfixed32) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedFloat) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedFixed64) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedSfixed64) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedDouble) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedString) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedBytes) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedChildEnum) isMessage_OneofDefaultedUnion() {}

func (*Message_OneofDefaultedSiblingEnum) isMessage_OneofDefaultedUnion() {}

func (m *Message) GetOneofDefaultedUnion() isMessage_OneofDefaultedUnion {
	if m != nil {
		return m.OneofDefaultedUnion
	}
	return nil
}

func (m *Message) GetOneofDefaultedBool() bool {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedBool); ok {
		return x.OneofDefaultedBool
	}
	return Default_Message_OneofDefaultedBool
}

func (m *Message) GetOneofDefaultedInt32() int32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedInt32); ok {
		return x.OneofDefaultedInt32
	}
	return Default_Message_OneofDefaultedInt32
}

func (m *Message) GetOneofDefaultedSint32() int32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSint32); ok {
		return x.OneofDefaultedSint32
	}
	return Default_Message_OneofDefaultedSint32
}

func (m *Message) GetOneofDefaultedUint32() uint32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedUint32); ok {
		return x.OneofDefaultedUint32
	}
	return Default_Message_OneofDefaultedUint32
}

func (m *Message) GetOneofDefaultedInt64() int64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedInt64); ok {
		return x.OneofDefaultedInt64
	}
	return Default_Message_OneofDefaultedInt64
}

func (m *Message) GetOneofDefaultedSint64() int64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSint64); ok {
		return x.OneofDefaultedSint64
	}
	return Default_Message_OneofDefaultedSint64
}

func (m *Message) GetOneofDefaultedUint64() uint64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedUint64); ok {
		return x.OneofDefaultedUint64
	}
	return Default_Message_OneofDefaultedUint64
}

func (m *Message) GetOneofDefaultedFixed32() uint32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedFixed32); ok {
		return x.OneofDefaultedFixed32
	}
	return Default_Message_OneofDefaultedFixed32
}

func (m *Message) GetOneofDefaultedSfixed32() int32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSfixed32); ok {
		return x.OneofDefaultedSfixed32
	}
	return Default_Message_OneofDefaultedSfixed32
}

func (m *Message) GetOneofDefaultedFloat() float32 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedFloat); ok {
		return x.OneofDefaultedFloat
	}
	return Default_Message_OneofDefaultedFloat
}

func (m *Message) GetOneofDefaultedFixed64() uint64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedFixed64); ok {
		return x.OneofDefaultedFixed64
	}
	return Default_Message_OneofDefaultedFixed64
}

func (m *Message) GetOneofDefaultedSfixed64() int64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSfixed64); ok {
		return x.OneofDefaultedSfixed64
	}
	return Default_Message_OneofDefaultedSfixed64
}

func (m *Message) GetOneofDefaultedDouble() float64 {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedDouble); ok {
		return x.OneofDefaultedDouble
	}
	return Default_Message_OneofDefaultedDouble
}

func (m *Message) GetOneofDefaultedString() string {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedString); ok {
		return x.OneofDefaultedString
	}
	return Default_Message_OneofDefaultedString
}

func (m *Message) GetOneofDefaultedBytes() []byte {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedBytes); ok {
		return x.OneofDefaultedBytes
	}
	return append([]byte(nil), Default_Message_OneofDefaultedBytes...)
}

func (m *Message) GetOneofDefaultedChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedChildEnum); ok {
		return x.OneofDefaultedChildEnum
	}
	return Default_Message_OneofDefaultedChildEnum
}

func (m *Message) GetOneofDefaultedSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofDefaultedUnion().(*Message_OneofDefaultedSiblingEnum); ok {
		return x.OneofDefaultedSiblingEnum
	}
	return Default_Message_OneofDefaultedSiblingEnum
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*Message) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*Message_OneofBool)(nil),
		(*Message_OneofInt32)(nil),
		(*Message_OneofSint32)(nil),
		(*Message_OneofUint32)(nil),
		(*Message_OneofInt64)(nil),
		(*Message_OneofSint64)(nil),
		(*Message_OneofUint64)(nil),
		(*Message_OneofFixed32)(nil),
		(*Message_OneofSfixed32)(nil),
		(*Message_OneofFloat)(nil),
		(*Message_OneofFixed64)(nil),
		(*Message_OneofSfixed64)(nil),
		(*Message_OneofDouble)(nil),
		(*Message_OneofString)(nil),
		(*Message_OneofBytes)(nil),
		(*Message_OneofChildEnum)(nil),
		(*Message_OneofChildMessage)(nil),
		(*Message_OneofNamedGroup)(nil),
		(*Message_OneofSiblingEnum)(nil),
		(*Message_OneofSiblingMessage)(nil),
		(*Message_Oneofgroup)(nil),
		(*Message_OneofString1)(nil),
		(*Message_OneofString2)(nil),
		(*Message_OneofString3)(nil),
		(*Message_OneofDefaultedBool)(nil),
		(*Message_OneofDefaultedInt32)(nil),
		(*Message_OneofDefaultedSint32)(nil),
		(*Message_OneofDefaultedUint32)(nil),
		(*Message_OneofDefaultedInt64)(nil),
		(*Message_OneofDefaultedSint64)(nil),
		(*Message_OneofDefaultedUint64)(nil),
		(*Message_OneofDefaultedFixed32)(nil),
		(*Message_OneofDefaultedSfixed32)(nil),
		(*Message_OneofDefaultedFloat)(nil),
		(*Message_OneofDefaultedFixed64)(nil),
		(*Message_OneofDefaultedSfixed64)(nil),
		(*Message_OneofDefaultedDouble)(nil),
		(*Message_OneofDefaultedString)(nil),
		(*Message_OneofDefaultedBytes)(nil),
		(*Message_OneofDefaultedChildEnum)(nil),
		(*Message_OneofDefaultedSiblingEnum)(nil),
	}
}

var E_Message_ExtensionOptionalBool = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*bool)(nil),
	Field:         10000,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_bool",
	Tag:           "varint,10000,opt,name=extension_optional_bool",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalInt32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         10001,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_int32",
	Tag:           "varint,10001,opt,name=extension_optional_int32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalSint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         10002,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_sint32",
	Tag:           "zigzag32,10002,opt,name=extension_optional_sint32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalUint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         10003,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_uint32",
	Tag:           "varint,10003,opt,name=extension_optional_uint32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalInt64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         10004,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_int64",
	Tag:           "varint,10004,opt,name=extension_optional_int64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalSint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         10005,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_sint64",
	Tag:           "zigzag64,10005,opt,name=extension_optional_sint64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalUint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         10006,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_uint64",
	Tag:           "varint,10006,opt,name=extension_optional_uint64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalFixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         10007,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_fixed32",
	Tag:           "fixed32,10007,opt,name=extension_optional_fixed32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalSfixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         10008,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_sfixed32",
	Tag:           "fixed32,10008,opt,name=extension_optional_sfixed32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalFloat = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float32)(nil),
	Field:         10009,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_float",
	Tag:           "fixed32,10009,opt,name=extension_optional_float",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalFixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         10010,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_fixed64",
	Tag:           "fixed64,10010,opt,name=extension_optional_fixed64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalSfixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         10011,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_sfixed64",
	Tag:           "fixed64,10011,opt,name=extension_optional_sfixed64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalDouble = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float64)(nil),
	Field:         10012,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_double",
	Tag:           "fixed64,10012,opt,name=extension_optional_double",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalString = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*string)(nil),
	Field:         10013,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_string",
	Tag:           "bytes,10013,opt,name=extension_optional_string",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalBytes = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]byte)(nil),
	Field:         10014,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_bytes",
	Tag:           "bytes,10014,opt,name=extension_optional_bytes",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalChildEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ChildEnum)(nil),
	Field:         10015,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_child_enum",
	Tag:           "varint,10015,opt,name=extension_optional_child_enum,enum=google.golang.org.proto2_20190205.Message_ChildEnum",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalChildMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ChildMessage)(nil),
	Field:         10016,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_child_message",
	Tag:           "bytes,10016,opt,name=extension_optional_child_message",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalNamedGroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_NamedGroup)(nil),
	Field:         10017,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_named_group",
	Tag:           "bytes,10017,opt,name=extension_optional_named_group",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalSiblingEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*SiblingEnum)(nil),
	Field:         10018,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_sibling_enum",
	Tag:           "varint,10018,opt,name=extension_optional_sibling_enum,enum=google.golang.org.proto2_20190205.SiblingEnum",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionOptionalSiblingMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*SiblingMessage)(nil),
	Field:         10019,
	Name:          "google.golang.org.proto2_20190205.Message.extension_optional_sibling_message",
	Tag:           "bytes,10019,opt,name=extension_optional_sibling_message",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_Extensionoptionalgroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ExtensionOptionalGroup)(nil),
	Field:         10020,
	Name:          "google.golang.org.proto2_20190205.Message.extensionoptionalgroup",
	Tag:           "group,10020,opt,name=ExtensionOptionalGroup",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedBool = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*bool)(nil),
	Field:         20000,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_bool",
	Tag:           "varint,20000,opt,name=extension_defaulted_bool,def=1",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedInt32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         20001,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_int32",
	Tag:           "varint,20001,opt,name=extension_defaulted_int32,def=-12345",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedSint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         20002,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_sint32",
	Tag:           "zigzag32,20002,opt,name=extension_defaulted_sint32,def=-3200",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedUint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         20003,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_uint32",
	Tag:           "varint,20003,opt,name=extension_defaulted_uint32,def=3200",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedInt64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         20004,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_int64",
	Tag:           "varint,20004,opt,name=extension_defaulted_int64,def=-123456789",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedSint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         20005,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_sint64",
	Tag:           "zigzag64,20005,opt,name=extension_defaulted_sint64,def=-6400",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedUint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         20006,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_uint64",
	Tag:           "varint,20006,opt,name=extension_defaulted_uint64,def=6400",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedFixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint32)(nil),
	Field:         20007,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_fixed32",
	Tag:           "fixed32,20007,opt,name=extension_defaulted_fixed32,def=320000",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedSfixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int32)(nil),
	Field:         20008,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_sfixed32",
	Tag:           "fixed32,20008,opt,name=extension_defaulted_sfixed32,def=-320000",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedFloat = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float32)(nil),
	Field:         20009,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_float",
	Tag:           "fixed32,20009,opt,name=extension_defaulted_float,def=3.14159",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedFixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*uint64)(nil),
	Field:         20010,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_fixed64",
	Tag:           "fixed64,20010,opt,name=extension_defaulted_fixed64,def=640000",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedSfixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*int64)(nil),
	Field:         20011,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_sfixed64",
	Tag:           "fixed64,20011,opt,name=extension_defaulted_sfixed64,def=-640000",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedDouble = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*float64)(nil),
	Field:         20012,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_double",
	Tag:           "fixed64,20012,opt,name=extension_defaulted_double,def=3.14159265359",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedString = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*string)(nil),
	Field:         20013,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_string",
	Tag:           "bytes,20013,opt,name=extension_defaulted_string,def=hello, \"world!\"\n",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedBytes = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]byte)(nil),
	Field:         20014,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_bytes",
	Tag:           "bytes,20014,opt,name=extension_defaulted_bytes,def=dead\\336\\255\\276\\357beef",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedChildEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*Message_ChildEnum)(nil),
	Field:         20015,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_child_enum",
	Tag:           "varint,20015,opt,name=extension_defaulted_child_enum,enum=google.golang.org.proto2_20190205.Message_ChildEnum,def=0",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionDefaultedSiblingEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: (*SiblingEnum)(nil),
	Field:         20016,
	Name:          "google.golang.org.proto2_20190205.Message.extension_defaulted_sibling_enum",
	Tag:           "varint,20016,opt,name=extension_defaulted_sibling_enum,enum=google.golang.org.proto2_20190205.SiblingEnum,def=0",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedBool = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]bool)(nil),
	Field:         30000,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_bool",
	Tag:           "varint,30000,rep,name=extension_repeated_bool",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedInt32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int32)(nil),
	Field:         30001,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_int32",
	Tag:           "varint,30001,rep,name=extension_repeated_int32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedSint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int32)(nil),
	Field:         30002,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_sint32",
	Tag:           "zigzag32,30002,rep,name=extension_repeated_sint32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedUint32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint32)(nil),
	Field:         30003,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_uint32",
	Tag:           "varint,30003,rep,name=extension_repeated_uint32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedInt64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int64)(nil),
	Field:         30004,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_int64",
	Tag:           "varint,30004,rep,name=extension_repeated_int64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedSint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int64)(nil),
	Field:         30005,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_sint64",
	Tag:           "zigzag64,30005,rep,name=extension_repeated_sint64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedUint64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint64)(nil),
	Field:         30006,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_uint64",
	Tag:           "varint,30006,rep,name=extension_repeated_uint64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedFixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint32)(nil),
	Field:         30007,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_fixed32",
	Tag:           "fixed32,30007,rep,name=extension_repeated_fixed32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedSfixed32 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int32)(nil),
	Field:         30008,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_sfixed32",
	Tag:           "fixed32,30008,rep,name=extension_repeated_sfixed32",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedFloat = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]float32)(nil),
	Field:         30009,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_float",
	Tag:           "fixed32,30009,rep,name=extension_repeated_float",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedFixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]uint64)(nil),
	Field:         30010,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_fixed64",
	Tag:           "fixed64,30010,rep,name=extension_repeated_fixed64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedSfixed64 = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]int64)(nil),
	Field:         30011,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_sfixed64",
	Tag:           "fixed64,30011,rep,name=extension_repeated_sfixed64",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedDouble = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]float64)(nil),
	Field:         30012,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_double",
	Tag:           "fixed64,30012,rep,name=extension_repeated_double",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedString = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]string)(nil),
	Field:         30013,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_string",
	Tag:           "bytes,30013,rep,name=extension_repeated_string",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedBytes = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([][]byte)(nil),
	Field:         30014,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_bytes",
	Tag:           "bytes,30014,rep,name=extension_repeated_bytes",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedChildEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]Message_ChildEnum)(nil),
	Field:         30015,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_child_enum",
	Tag:           "varint,30015,rep,name=extension_repeated_child_enum,enum=google.golang.org.proto2_20190205.Message_ChildEnum",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedChildMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*Message_ChildMessage)(nil),
	Field:         30016,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_child_message",
	Tag:           "bytes,30016,rep,name=extension_repeated_child_message",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedNamedGroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*Message_NamedGroup)(nil),
	Field:         30017,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_named_group",
	Tag:           "bytes,30017,rep,name=extension_repeated_named_group",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedSiblingEnum = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]SiblingEnum)(nil),
	Field:         30018,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_sibling_enum",
	Tag:           "varint,30018,rep,name=extension_repeated_sibling_enum,enum=google.golang.org.proto2_20190205.SiblingEnum",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_ExtensionRepeatedSiblingMessage = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*SiblingMessage)(nil),
	Field:         30019,
	Name:          "google.golang.org.proto2_20190205.Message.extension_repeated_sibling_message",
	Tag:           "bytes,30019,rep,name=extension_repeated_sibling_message",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

var E_Message_Extensionrepeatedgroup = &proto.ExtensionDesc{
	ExtendedType:  (*Message)(nil),
	ExtensionType: ([]*Message_ExtensionRepeatedGroup)(nil),
	Field:         30020,
	Name:          "google.golang.org.proto2_20190205.Message.extensionrepeatedgroup",
	Tag:           "group,30020,rep,name=ExtensionRepeatedGroup",
	Filename:      "proto2_20190205_c823c79e/test.proto",
}

type Message_ChildMessage struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	F4                   *Message `protobuf:"bytes,4,opt,name=f4" json:"f4,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_ChildMessage) Reset()         { *m = Message_ChildMessage{} }
func (m *Message_ChildMessage) String() string { return proto.CompactTextString(m) }
func (*Message_ChildMessage) ProtoMessage()    {}
func (*Message_ChildMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 0}
}

func (m *Message_ChildMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_ChildMessage.Unmarshal(m, b)
}
func (m *Message_ChildMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_ChildMessage.Marshal(b, m, deterministic)
}
func (m *Message_ChildMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_ChildMessage.Merge(m, src)
}
func (m *Message_ChildMessage) XXX_Size() int {
	return xxx_messageInfo_Message_ChildMessage.Size(m)
}
func (m *Message_ChildMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_ChildMessage.DiscardUnknown(m)
}

var xxx_messageInfo_Message_ChildMessage proto.InternalMessageInfo

func (m *Message_ChildMessage) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_ChildMessage) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_ChildMessage) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func (m *Message_ChildMessage) GetF4() *Message {
	if m != nil {
		return m.F4
	}
	return nil
}

type Message_NamedGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	F4                   *Message `protobuf:"bytes,4,opt,name=f4" json:"f4,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_NamedGroup) Reset()         { *m = Message_NamedGroup{} }
func (m *Message_NamedGroup) String() string { return proto.CompactTextString(m) }
func (*Message_NamedGroup) ProtoMessage()    {}
func (*Message_NamedGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 1}
}

func (m *Message_NamedGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_NamedGroup.Unmarshal(m, b)
}
func (m *Message_NamedGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_NamedGroup.Marshal(b, m, deterministic)
}
func (m *Message_NamedGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_NamedGroup.Merge(m, src)
}
func (m *Message_NamedGroup) XXX_Size() int {
	return xxx_messageInfo_Message_NamedGroup.Size(m)
}
func (m *Message_NamedGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_NamedGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_NamedGroup proto.InternalMessageInfo

func (m *Message_NamedGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_NamedGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_NamedGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func (m *Message_NamedGroup) GetF4() *Message {
	if m != nil {
		return m.F4
	}
	return nil
}

type Message_OptionalGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_OptionalGroup) Reset()         { *m = Message_OptionalGroup{} }
func (m *Message_OptionalGroup) String() string { return proto.CompactTextString(m) }
func (*Message_OptionalGroup) ProtoMessage()    {}
func (*Message_OptionalGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 2}
}

func (m *Message_OptionalGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_OptionalGroup.Unmarshal(m, b)
}
func (m *Message_OptionalGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_OptionalGroup.Marshal(b, m, deterministic)
}
func (m *Message_OptionalGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_OptionalGroup.Merge(m, src)
}
func (m *Message_OptionalGroup) XXX_Size() int {
	return xxx_messageInfo_Message_OptionalGroup.Size(m)
}
func (m *Message_OptionalGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_OptionalGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_OptionalGroup proto.InternalMessageInfo

func (m *Message_OptionalGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_OptionalGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_OptionalGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_RequiredGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_RequiredGroup) Reset()         { *m = Message_RequiredGroup{} }
func (m *Message_RequiredGroup) String() string { return proto.CompactTextString(m) }
func (*Message_RequiredGroup) ProtoMessage()    {}
func (*Message_RequiredGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 3}
}

func (m *Message_RequiredGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_RequiredGroup.Unmarshal(m, b)
}
func (m *Message_RequiredGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_RequiredGroup.Marshal(b, m, deterministic)
}
func (m *Message_RequiredGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_RequiredGroup.Merge(m, src)
}
func (m *Message_RequiredGroup) XXX_Size() int {
	return xxx_messageInfo_Message_RequiredGroup.Size(m)
}
func (m *Message_RequiredGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_RequiredGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_RequiredGroup proto.InternalMessageInfo

func (m *Message_RequiredGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_RequiredGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_RequiredGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_RepeatedGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_RepeatedGroup) Reset()         { *m = Message_RepeatedGroup{} }
func (m *Message_RepeatedGroup) String() string { return proto.CompactTextString(m) }
func (*Message_RepeatedGroup) ProtoMessage()    {}
func (*Message_RepeatedGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 4}
}

func (m *Message_RepeatedGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_RepeatedGroup.Unmarshal(m, b)
}
func (m *Message_RepeatedGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_RepeatedGroup.Marshal(b, m, deterministic)
}
func (m *Message_RepeatedGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_RepeatedGroup.Merge(m, src)
}
func (m *Message_RepeatedGroup) XXX_Size() int {
	return xxx_messageInfo_Message_RepeatedGroup.Size(m)
}
func (m *Message_RepeatedGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_RepeatedGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_RepeatedGroup proto.InternalMessageInfo

func (m *Message_RepeatedGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_RepeatedGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_RepeatedGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_OneofGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_OneofGroup) Reset()         { *m = Message_OneofGroup{} }
func (m *Message_OneofGroup) String() string { return proto.CompactTextString(m) }
func (*Message_OneofGroup) ProtoMessage()    {}
func (*Message_OneofGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 33}
}

func (m *Message_OneofGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_OneofGroup.Unmarshal(m, b)
}
func (m *Message_OneofGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_OneofGroup.Marshal(b, m, deterministic)
}
func (m *Message_OneofGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_OneofGroup.Merge(m, src)
}
func (m *Message_OneofGroup) XXX_Size() int {
	return xxx_messageInfo_Message_OneofGroup.Size(m)
}
func (m *Message_OneofGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_OneofGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_OneofGroup proto.InternalMessageInfo

func (m *Message_OneofGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_OneofGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_OneofGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_ExtensionOptionalGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_ExtensionOptionalGroup) Reset()         { *m = Message_ExtensionOptionalGroup{} }
func (m *Message_ExtensionOptionalGroup) String() string { return proto.CompactTextString(m) }
func (*Message_ExtensionOptionalGroup) ProtoMessage()    {}
func (*Message_ExtensionOptionalGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 34}
}

func (m *Message_ExtensionOptionalGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_ExtensionOptionalGroup.Unmarshal(m, b)
}
func (m *Message_ExtensionOptionalGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_ExtensionOptionalGroup.Marshal(b, m, deterministic)
}
func (m *Message_ExtensionOptionalGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_ExtensionOptionalGroup.Merge(m, src)
}
func (m *Message_ExtensionOptionalGroup) XXX_Size() int {
	return xxx_messageInfo_Message_ExtensionOptionalGroup.Size(m)
}
func (m *Message_ExtensionOptionalGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_ExtensionOptionalGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_ExtensionOptionalGroup proto.InternalMessageInfo

func (m *Message_ExtensionOptionalGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_ExtensionOptionalGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_ExtensionOptionalGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message_ExtensionRepeatedGroup struct {
	F1                   *string  `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2                   *string  `protobuf:"bytes,2,req,name=f2" json:"f2,omitempty"`
	F3                   []string `protobuf:"bytes,3,rep,name=f3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_ExtensionRepeatedGroup) Reset()         { *m = Message_ExtensionRepeatedGroup{} }
func (m *Message_ExtensionRepeatedGroup) String() string { return proto.CompactTextString(m) }
func (*Message_ExtensionRepeatedGroup) ProtoMessage()    {}
func (*Message_ExtensionRepeatedGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3a17e2c63b2b3424, []int{1, 35}
}

func (m *Message_ExtensionRepeatedGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_ExtensionRepeatedGroup.Unmarshal(m, b)
}
func (m *Message_ExtensionRepeatedGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_ExtensionRepeatedGroup.Marshal(b, m, deterministic)
}
func (m *Message_ExtensionRepeatedGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_ExtensionRepeatedGroup.Merge(m, src)
}
func (m *Message_ExtensionRepeatedGroup) XXX_Size() int {
	return xxx_messageInfo_Message_ExtensionRepeatedGroup.Size(m)
}
func (m *Message_ExtensionRepeatedGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_ExtensionRepeatedGroup.DiscardUnknown(m)
}

var xxx_messageInfo_Message_ExtensionRepeatedGroup proto.InternalMessageInfo

func (m *Message_ExtensionRepeatedGroup) GetF1() string {
	if m != nil && m.F1 != nil {
		return *m.F1
	}
	return ""
}

func (m *Message_ExtensionRepeatedGroup) GetF2() string {
	if m != nil && m.F2 != nil {
		return *m.F2
	}
	return ""
}

func (m *Message_ExtensionRepeatedGroup) GetF3() []string {
	if m != nil {
		return m.F3
	}
	return nil
}

func init() {
	proto.RegisterEnum("google.golang.org.proto2_20190205.SiblingEnum", SiblingEnum_name, SiblingEnum_value)
	proto.RegisterEnum("google.golang.org.proto2_20190205.Message_ChildEnum", Message_ChildEnum_name, Message_ChildEnum_value)
	proto.RegisterType((*SiblingMessage)(nil), "google.golang.org.proto2_20190205.SiblingMessage")
	proto.RegisterExtension(E_Message_ExtensionOptionalBool)
	proto.RegisterExtension(E_Message_ExtensionOptionalInt32)
	proto.RegisterExtension(E_Message_ExtensionOptionalSint32)
	proto.RegisterExtension(E_Message_ExtensionOptionalUint32)
	proto.RegisterExtension(E_Message_ExtensionOptionalInt64)
	proto.RegisterExtension(E_Message_ExtensionOptionalSint64)
	proto.RegisterExtension(E_Message_ExtensionOptionalUint64)
	proto.RegisterExtension(E_Message_ExtensionOptionalFixed32)
	proto.RegisterExtension(E_Message_ExtensionOptionalSfixed32)
	proto.RegisterExtension(E_Message_ExtensionOptionalFloat)
	proto.RegisterExtension(E_Message_ExtensionOptionalFixed64)
	proto.RegisterExtension(E_Message_ExtensionOptionalSfixed64)
	proto.RegisterExtension(E_Message_ExtensionOptionalDouble)
	proto.RegisterExtension(E_Message_ExtensionOptionalString)
	proto.RegisterExtension(E_Message_ExtensionOptionalBytes)
	proto.RegisterExtension(E_Message_ExtensionOptionalChildEnum)
	proto.RegisterExtension(E_Message_ExtensionOptionalChildMessage)
	proto.RegisterExtension(E_Message_ExtensionOptionalNamedGroup)
	proto.RegisterExtension(E_Message_ExtensionOptionalSiblingEnum)
	proto.RegisterExtension(E_Message_ExtensionOptionalSiblingMessage)
	proto.RegisterExtension(E_Message_Extensionoptionalgroup)
	proto.RegisterExtension(E_Message_ExtensionDefaultedBool)
	proto.RegisterExtension(E_Message_ExtensionDefaultedInt32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSint32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedUint32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedInt64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSint64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedUint64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedFixed32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSfixed32)
	proto.RegisterExtension(E_Message_ExtensionDefaultedFloat)
	proto.RegisterExtension(E_Message_ExtensionDefaultedFixed64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSfixed64)
	proto.RegisterExtension(E_Message_ExtensionDefaultedDouble)
	proto.RegisterExtension(E_Message_ExtensionDefaultedString)
	proto.RegisterExtension(E_Message_ExtensionDefaultedBytes)
	proto.RegisterExtension(E_Message_ExtensionDefaultedChildEnum)
	proto.RegisterExtension(E_Message_ExtensionDefaultedSiblingEnum)
	proto.RegisterExtension(E_Message_ExtensionRepeatedBool)
	proto.RegisterExtension(E_Message_ExtensionRepeatedInt32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSint32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedUint32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedInt64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSint64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedUint64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedFixed32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSfixed32)
	proto.RegisterExtension(E_Message_ExtensionRepeatedFloat)
	proto.RegisterExtension(E_Message_ExtensionRepeatedFixed64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSfixed64)
	proto.RegisterExtension(E_Message_ExtensionRepeatedDouble)
	proto.RegisterExtension(E_Message_ExtensionRepeatedString)
	proto.RegisterExtension(E_Message_ExtensionRepeatedBytes)
	proto.RegisterExtension(E_Message_ExtensionRepeatedChildEnum)
	proto.RegisterExtension(E_Message_ExtensionRepeatedChildMessage)
	proto.RegisterExtension(E_Message_ExtensionRepeatedNamedGroup)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSiblingEnum)
	proto.RegisterExtension(E_Message_ExtensionRepeatedSiblingMessage)
	proto.RegisterExtension(E_Message_Extensionrepeatedgroup)
	proto.RegisterType((*Message)(nil), "google.golang.org.proto2_20190205.Message")
	proto.RegisterMapType((map[bool]bool)(nil), "google.golang.org.proto2_20190205.Message.MapBoolBoolEntry")
	proto.RegisterMapType((map[bool][]byte)(nil), "google.golang.org.proto2_20190205.Message.MapBoolBytesEntry")
	proto.RegisterMapType((map[bool]Message_ChildEnum)(nil), "google.golang.org.proto2_20190205.Message.MapBoolChildEnumEntry")
	proto.RegisterMapType((map[bool]*Message_ChildMessage)(nil), "google.golang.org.proto2_20190205.Message.MapBoolChildMessageEntry")
	proto.RegisterMapType((map[bool]float64)(nil), "google.golang.org.proto2_20190205.Message.MapBoolDoubleEntry")
	proto.RegisterMapType((map[bool]uint32)(nil), "google.golang.org.proto2_20190205.Message.MapBoolFixed32Entry")
	proto.RegisterMapType((map[bool]uint64)(nil), "google.golang.org.proto2_20190205.Message.MapBoolFixed64Entry")
	proto.RegisterMapType((map[bool]float32)(nil), "google.golang.org.proto2_20190205.Message.MapBoolFloatEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto2_20190205.Message.MapBoolInt32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto2_20190205.Message.MapBoolInt64Entry")
	proto.RegisterMapType((map[bool]*Message_NamedGroup)(nil), "google.golang.org.proto2_20190205.Message.MapBoolNamedGroupEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto2_20190205.Message.MapBoolSfixed32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto2_20190205.Message.MapBoolSfixed64Entry")
	proto.RegisterMapType((map[bool]SiblingEnum)(nil), "google.golang.org.proto2_20190205.Message.MapBoolSiblingEnumEntry")
	proto.RegisterMapType((map[bool]*SiblingMessage)(nil), "google.golang.org.proto2_20190205.Message.MapBoolSiblingMessageEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto2_20190205.Message.MapBoolSint32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto2_20190205.Message.MapBoolSint64Entry")
	proto.RegisterMapType((map[bool]string)(nil), "google.golang.org.proto2_20190205.Message.MapBoolStringEntry")
	proto.RegisterMapType((map[bool]uint32)(nil), "google.golang.org.proto2_20190205.Message.MapBoolUint32Entry")
	proto.RegisterMapType((map[bool]uint64)(nil), "google.golang.org.proto2_20190205.Message.MapBoolUint64Entry")
	proto.RegisterMapType((map[uint32]bool)(nil), "google.golang.org.proto2_20190205.Message.MapFixed32BoolEntry")
	proto.RegisterMapType((map[int32]bool)(nil), "google.golang.org.proto2_20190205.Message.MapInt32BoolEntry")
	proto.RegisterMapType((map[int64]bool)(nil), "google.golang.org.proto2_20190205.Message.MapInt64BoolEntry")
	proto.RegisterMapType((map[int32]bool)(nil), "google.golang.org.proto2_20190205.Message.MapSint32BoolEntry")
	proto.RegisterMapType((map[int64]bool)(nil), "google.golang.org.proto2_20190205.Message.MapSint64BoolEntry")
	proto.RegisterMapType((map[string]bool)(nil), "google.golang.org.proto2_20190205.Message.MapStringBoolEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "google.golang.org.proto2_20190205.Message.MapUint32BoolEntry")
	proto.RegisterMapType((map[uint64]bool)(nil), "google.golang.org.proto2_20190205.Message.MapUint64BoolEntry")
	proto.RegisterType((*Message_ChildMessage)(nil), "google.golang.org.proto2_20190205.Message.ChildMessage")
	proto.RegisterType((*Message_NamedGroup)(nil), "google.golang.org.proto2_20190205.Message.NamedGroup")
	proto.RegisterType((*Message_OptionalGroup)(nil), "google.golang.org.proto2_20190205.Message.OptionalGroup")
	proto.RegisterType((*Message_RequiredGroup)(nil), "google.golang.org.proto2_20190205.Message.RequiredGroup")
	proto.RegisterType((*Message_RepeatedGroup)(nil), "google.golang.org.proto2_20190205.Message.RepeatedGroup")
	proto.RegisterType((*Message_OneofGroup)(nil), "google.golang.org.proto2_20190205.Message.OneofGroup")
	proto.RegisterType((*Message_ExtensionOptionalGroup)(nil), "google.golang.org.proto2_20190205.Message.ExtensionOptionalGroup")
	proto.RegisterType((*Message_ExtensionRepeatedGroup)(nil), "google.golang.org.proto2_20190205.Message.ExtensionRepeatedGroup")
}

func init() {
	proto.RegisterFile("proto2_20190205_c823c79e/test.proto", fileDescriptor_3a17e2c63b2b3424)
}

var fileDescriptor_3a17e2c63b2b3424 = []byte{
	// 4469 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5c, 0x67, 0x70, 0x24, 0xc7,
	0x75, 0xc6, 0xec, 0x62, 0x17, 0x87, 0x3e, 0x2c, 0xb0, 0x98, 0xbb, 0x03, 0xe6, 0x40, 0xd2, 0x5c,
	0x41, 0xb2, 0xbc, 0xa6, 0x79, 0x38, 0x60, 0xd0, 0xe8, 0xe3, 0xad, 0x19, 0x04, 0x90, 0x47, 0x2d,
	0x65, 0x71, 0xa9, 0x1a, 0xd6, 0xb9, 0x5c, 0x2e, 0x96, 0x61, 0xdc, 0x61, 0x81, 0x03, 0xb9, 0x01,
	0x04, 0x76, 0x49, 0x9e, 0x25, 0x17, 0xcf, 0x72, 0xfc, 0x49, 0xe5, 0xb4, 0x92, 0x28, 0x66, 0x89,
	0x51, 0x39, 0x31, 0x29, 0xd8, 0xa6, 0x72, 0x72, 0x90, 0x93, 0x9c, 0x93, 0x9c, 0x73, 0x0e, 0xd5,
	0xfd, 0xba, 0xa7, 0xbb, 0x67, 0x7a, 0x16, 0xe8, 0x59, 0x15, 0x7f, 0xb0, 0x8a, 0xd7, 0xfb, 0xfa,
	0x7d, 0xfd, 0xbe, 0xed, 0xf7, 0xde, 0x87, 0x9e, 0xe9, 0x45, 0x2f, 0xdd, 0xde, 0x69, 0x77, 0xda,
	0xfe, 0xaa, 0x3f, 0xbf, 0x70, 0x72, 0xde, 0x9f, 0x5f, 0x5a, 0x3d, 0x7b, 0x85, 0xbf, 0x78, 0xf6,
	0xc4, 0xc9, 0xfa, 0xf1, 0x4e, 0x7d, 0xb7, 0x33, 0xc7, 0x3e, 0x75, 0x5f, 0xb2, 0xd9, 0x6e, 0x6f,
	0x36, 0xea, 0x73, 0x9b, 0xed, 0xc6, 0x5a, 0x6b, 0x73, 0xae, 0xbd, 0xb3, 0x39, 0x17, 0x99, 0x36,
	0xfb, 0x3a, 0x34, 0x7e, 0xf3, 0xd6, 0x99, 0xc6, 0x56, 0x6b, 0xf3, 0xc6, 0xfa, 0xee, 0xee, 0xda,
	0x66, 0xdd, 0x1d, 0x47, 0x99, 0x8d, 0x05, 0xcf, 0x29, 0x39, 0xe5, 0xd1, 0x20, 0xb3, 0xb1, 0xc0,
	0xfe, 0xed, 0x7b, 0x99, 0x52, 0x86, 0xfd, 0xdb, 0x67, 0xff, 0x5e, 0xf4, 0xb2, 0xa5, 0x2c, 0xfb,
	0xf7, 0xa2, 0x5b, 0x41, 0x99, 0x0d, 0xec, 0x0d, 0x97, 0x9c, 0xf2, 0x41, 0xff, 0xb2, 0xb9, 0x3d,
	0x11, 0xe7, 0x38, 0x4e, 0x90, 0xd9, 0xc0, 0xb3, 0xdf, 0x79, 0xd4, 0x41, 0x23, 0x02, 0xf8, 0x34,
	0x42, 0xad, 0xb5, 0x66, 0x7d, 0x7d, 0x73, 0xa7, 0xdd, 0xdd, 0x66, 0x0b, 0x40, 0xfe, 0xd2, 0xfe,
	0x1d, 0xce, 0xd5, 0xe8, 0xe4, 0x57, 0xd2, 0xc9, 0x81, 0xe2, 0xc8, 0x7d, 0x29, 0x2a, 0xb4, 0xb7,
	0x3b, 0x5b, 0xed, 0xd6, 0x5a, 0x63, 0xf5, 0x4c, 0xbb, 0xdd, 0xf0, 0xd6, 0x4b, 0x4e, 0xf9, 0x40,
	0x30, 0x26, 0x06, 0x57, 0xda, 0xed, 0x86, 0xfb, 0xfd, 0x68, 0x3c, 0x34, 0xda, 0x6a, 0x75, 0x16,
	0x7d, 0xaf, 0x5e, 0x72, 0xca, 0xb9, 0x20, 0x9c, 0x7a, 0x03, 0x1d, 0x74, 0x7f, 0x00, 0x4d, 0x84,
	0x66, 0xbb, 0x60, 0xb7, 0x51, 0x72, 0xca, 0x93, 0x41, 0x38, 0xfb, 0xe6, 0xad, 0x98, 0x61, 0x17,
	0x0c, 0x37, 0x4b, 0x4e, 0xb9, 0x20, 0x0d, 0x4f, 0x83, 0x61, 0x04, 0x98, 0x60, 0xef, 0x5c, 0xc9,
	0x29, 0x67, 0x35, 0x60, 0x82, 0x63, 0xc0, 0x04, 0x7b, 0x5b, 0x25, 0xa7, 0xec, 0xea, 0xc0, 0x11,
	0xc3, 0x2e, 0x18, 0xde, 0x5a, 0x72, 0xca, 0xc3, 0x3a, 0x30, 0xc1, 0xee, 0x0f, 0xa2, 0x62, 0x68,
	0xb8, 0xb1, 0x75, 0x57, 0x7d, 0x7d, 0xd1, 0xf7, 0x6e, 0x2b, 0x39, 0xe5, 0x91, 0x20, 0x74, 0x70,
	0x3d, 0x0c, 0xbb, 0x3f, 0x84, 0x26, 0x25, 0xb8, 0xb0, 0x6d, 0x94, 0x9c, 0xf2, 0x44, 0x10, 0xfa,
	0xb8, 0x99, 0x8f, 0x6b, 0x01, 0x6d, 0x34, 0xda, 0x6b, 0x1d, 0xaf, 0x59, 0x72, 0xca, 0x19, 0x19,
	0xd0, 0xf5, 0x74, 0x30, 0x0e, 0x4f, 0xb0, 0xd7, 0x2a, 0x39, 0xe5, 0x7c, 0x04, 0x9e, 0x60, 0x03,
	0x3c, 0xc1, 0x5e, 0xbb, 0xe4, 0x94, 0x8b, 0x51, 0xf8, 0x48, 0xfc, 0xeb, 0xed, 0xee, 0x99, 0x46,
	0xdd, 0xdb, 0x2e, 0x39, 0x65, 0x47, 0xc6, 0x7f, 0x1d, 0x1b, 0xd5, 0x19, 0xed, 0xec, 0x6c, 0xb5,
	0x36, 0xbd, 0xdb, 0xd9, 0x9e, 0x97, 0x8c, 0xb2, 0x51, 0x2d, 0xa0, 0x33, 0xe7, 0x3b, 0xf5, 0x5d,
	0x6f, 0xa7, 0xe4, 0x94, 0xc7, 0x64, 0x40, 0x2b, 0x74, 0xd0, 0x5d, 0x47, 0x87, 0x42, 0xb3, 0xb3,
	0xe7, 0xb6, 0x1a, 0xeb, 0xab, 0xf5, 0x56, 0xb7, 0xe9, 0xed, 0x96, 0x9c, 0xf2, 0xb8, 0x8f, 0x2d,
	0xb6, 0xf1, 0xb5, 0x74, 0xf2, 0xa9, 0x56, 0xb7, 0x19, 0x84, 0x61, 0x87, 0x43, 0x6e, 0x13, 0x4d,
	0x45, 0x50, 0x9a, 0x30, 0xcd, 0xeb, 0xb0, 0x04, 0x3c, 0x61, 0x0b, 0x24, 0xb2, 0xf1, 0xb0, 0x86,
	0x25, 0x52, 0x72, 0x13, 0x85, 0xe3, 0xab, 0x2c, 0xa5, 0x56, 0x21, 0x39, 0xbb, 0x0c, 0x2c, 0x65,
	0x72, 0xba, 0xc2, 0xa5, 0x1c, 0x73, 0xcf, 0xa0, 0x23, 0xca, 0xfe, 0x66, 0xf5, 0x08, 0xf8, 0xbb,
	0x83, 0xf1, 0x37, 0xb7, 0x0f, 0x24, 0x5e, 0xc6, 0x18, 0x73, 0x87, 0x64, 0x56, 0x84, 0x83, 0xee,
	0x6d, 0xc8, 0x8b, 0x61, 0x08, 0xf6, 0xee, 0x64, 0x01, 0x2d, 0xec, 0x1f, 0x46, 0xf0, 0x36, 0x15,
	0x41, 0x12, 0xcc, 0xfd, 0x84, 0xac, 0x3a, 0x40, 0xd9, 0x5d, 0xac, 0x9e, 0x5d, 0x61, 0x41, 0xd9,
	0x4d, 0x7c, 0x3e, 0xb0, 0xa6, 0xbb, 0x73, 0x2f, 0x47, 0xe3, 0xeb, 0xf5, 0x8d, 0xb5, 0x6e, 0xa3,
	0x53, 0x5f, 0x87, 0xb2, 0xf6, 0x02, 0xad, 0x98, 0x07, 0x2a, 0xc3, 0x9d, 0x9d, 0x6e, 0x3d, 0x28,
	0x84, 0x1f, 0xb2, 0xf2, 0x36, 0x8f, 0x26, 0xa4, 0x35, 0x94, 0xa3, 0x2f, 0x50, 0xf3, 0x5c, 0x25,
	0x7f, 0x6c, 0xc1, 0x5f, 0xc4, 0x4b, 0x81, 0xf4, 0x06, 0x95, 0x6e, 0x01, 0x15, 0xe5, 0x0c, 0x5e,
	0xea, 0xbe, 0x48, 0xa7, 0x4c, 0x56, 0x72, 0xc7, 0x16, 0xfd, 0xf9, 0xf9, 0x40, 0x7a, 0xe4, 0x35,
	0x6f, 0x5e, 0x9d, 0xc2, 0x8b, 0xde, 0x97, 0xe8, 0x94, 0x42, 0x65, 0x38, 0x32, 0x83, 0x17, 0x3f,
	0x1c, 0x59, 0x16, 0xc1, 0xde, 0x97, 0xe9, 0x84, 0x6c, 0x05, 0xc1, 0xb2, 0xc8, 0x89, 0x2b, 0x4e,
	0xea, 0x4b, 0x23, 0x38, 0xbe, 0x34, 0x82, 0xbd, 0xaf, 0xd0, 0x69, 0x6e, 0x25, 0x77, 0x8c, 0xe0,
	0xd8, 0xd2, 0x08, 0x8e, 0x2f, 0x8d, 0x60, 0xef, 0xab, 0x74, 0xca, 0x70, 0x65, 0x38, 0x32, 0x83,
	0x97, 0x47, 0x8c, 0x26, 0xe5, 0x0c, 0x51, 0xf3, 0xbe, 0x46, 0xa7, 0x8c, 0x54, 0xf2, 0x34, 0x9a,
	0xf9, 0xf9, 0x40, 0xfa, 0x14, 0x95, 0xf2, 0x04, 0x72, 0x95, 0xa5, 0x89, 0x69, 0x5f, 0xa7, 0xd3,
	0x26, 0x2a, 0x23, 0xc7, 0xf8, 0x3c, 0xe9, 0x39, 0xac, 0x9a, 0x0b, 0x2a, 0x13, 0x50, 0x36, 0xbf,
	0x41, 0x67, 0x65, 0x2a, 0x23, 0x8b, 0x73, 0x0b, 0x78, 0x61, 0x49, 0xa5, 0x01, 0x2a, 0x68, 0x7c,
	0x85, 0x04, 0x7b, 0xdf, 0xa4, 0x93, 0xf2, 0x95, 0x3c, 0x0d, 0x2a, 0xbe, 0x42, 0x82, 0x4d, 0x2b,
	0x24, 0xd8, 0xfb, 0x16, 0x9d, 0x56, 0xac, 0x8c, 0x1c, 0xe3, 0xf3, 0xa2, 0x2b, 0x24, 0xd8, 0x3d,
	0xa9, 0x52, 0xc8, 0x2b, 0xeb, 0xaf, 0xd1, 0x69, 0x4e, 0xa5, 0xc0, 0x97, 0xe8, 0x93, 0xa5, 0xc5,
	0xa5, 0x93, 0x0a, 0x97, 0xbc, 0xd4, 0x5e, 0xa9, 0x7d, 0x61, 0x50, 0x6b, 0x7f, 0x9d, 0x09, 0x8c,
	0x4a, 0xf1, 0x5c, 0xbd, 0xd1, 0x68, 0x5f, 0x5e, 0x9a, 0xbd, 0xb3, 0xbd, 0xd3, 0x58, 0x7f, 0xc9,
	0x2c, 0x52, 0xbf, 0x3b, 0xa8, 0xbf, 0x2b, 0x2a, 0x35, 0x50, 0x80, 0x7f, 0x83, 0x4e, 0x1e, 0xab,
	0x78, 0xeb, 0xf5, 0xb5, 0xf5, 0x5b, 0x16, 0x17, 0xc9, 0x2d, 0xfe, 0xd2, 0xd2, 0x2d, 0xfe, 0x09,
	0x72, 0xcb, 0xe2, 0xd2, 0x89, 0x33, 0xf5, 0xfa, 0x86, 0xc2, 0x15, 0x14, 0xe7, 0x16, 0x3a, 0x2c,
	0x7d, 0x28, 0xd5, 0xf9, 0x37, 0x9d, 0xf4, 0xe5, 0xb9, 0x92, 0x5b, 0x7e, 0xf5, 0x6b, 0xaa, 0xcb,
	0x81, 0xe4, 0x53, 0x96, 0xe9, 0x06, 0x9a, 0x52, 0xb7, 0xa8, 0x52, 0xcf, 0xbe, 0xed, 0xa4, 0x29,
	0x68, 0x02, 0xeb, 0xb0, 0xb2, 0xb1, 0x65, 0x61, 0x7b, 0x19, 0x2a, 0xec, 0xd4, 0x6f, 0xef, 0x6e,
	0xed, 0x88, 0x52, 0xf0, 0x18, 0x55, 0x6b, 0x07, 0x82, 0x31, 0x31, 0xca, 0x6a, 0xc0, 0xcb, 0xd1,
	0x78, 0x68, 0x05, 0xc9, 0xf9, 0x38, 0x35, 0xcb, 0x05, 0xe1, 0x64, 0xc8, 0xfc, 0x32, 0x9a, 0x08,
	0xed, 0x78, 0xe2, 0x3f, 0x41, 0x0d, 0x27, 0x83, 0x70, 0x3e, 0x4f, 0x78, 0xd5, 0x92, 0xe7, 0xfb,
	0x93, 0xd4, 0xb2, 0x20, 0x2d, 0x79, 0xa2, 0x47, 0xb0, 0x09, 0xf6, 0x9e, 0xa2, 0x86, 0x59, 0x0d,
	0x9b, 0xe0, 0x18, 0x36, 0xc1, 0xde, 0x07, 0xa9, 0xa1, 0xab, 0x63, 0x47, 0x2c, 0x79, 0x42, 0x7f,
	0x88, 0x5a, 0x0e, 0xeb, 0xd8, 0x04, 0xbb, 0x97, 0xa1, 0x62, 0x68, 0x29, 0x32, 0xf2, 0xc3, 0xd4,
	0x74, 0x24, 0x08, 0x5d, 0x88, 0xfc, 0xbd, 0x1c, 0x4d, 0x4a, 0x7c, 0x61, 0xfc, 0x11, 0x6a, 0x3c,
	0x11, 0x84, 0x5e, 0xc2, 0xa4, 0x55, 0xa3, 0x82, 0x9c, 0xfd, 0x28, 0x35, 0xcd, 0xc8, 0xa8, 0x20,
	0x53, 0x63, 0x2b, 0x20, 0xd8, 0xfb, 0x18, 0xb5, 0xcc, 0x47, 0x56, 0x40, 0xb0, 0x61, 0x05, 0x04,
	0x7b, 0x1f, 0xa7, 0xc6, 0xc5, 0xe8, 0x0a, 0x22, 0x2c, 0xf0, 0x9c, 0xfc, 0x04, 0xb5, 0x75, 0x24,
	0x0b, 0x3c, 0x07, 0x35, 0x66, 0x21, 0x05, 0x3f, 0x09, 0x9a, 0x5e, 0x32, 0x0b, 0xf9, 0xa6, 0x46,
	0x05, 0xe9, 0xf6, 0x29, 0x6a, 0x38, 0x26, 0xa3, 0x82, 0x9c, 0xaa, 0xa3, 0x43, 0xa1, 0x9d, 0x92,
	0x52, 0x9f, 0xa6, 0xc6, 0xa9, 0x15, 0x8f, 0xf0, 0x28, 0x53, 0xa9, 0x85, 0xa6, 0x22, 0x30, 0xa2,
	0x67, 0x3f, 0x4d, 0x91, 0x06, 0x91, 0x3c, 0x1a, 0x98, 0x68, 0xdc, 0xe7, 0x50, 0x38, 0xae, 0x49,
	0x9e, 0x67, 0x00, 0x2d, 0xad, 0xe6, 0x11, 0x3e, 0x15, 0xcd, 0x73, 0x16, 0x1d, 0x51, 0x36, 0xbb,
	0x52, 0x23, 0x9e, 0x05, 0x0a, 0xad, 0x45, 0x8f, 0x4c, 0x11, 0x59, 0x1b, 0x1a, 0xc8, 0x8b, 0x81,
	0x08, 0x02, 0x9f, 0x83, 0x90, 0xd2, 0xa8, 0x9e, 0x08, 0x94, 0x20, 0x6f, 0x55, 0x56, 0x22, 0x60,
	0xed, 0x79, 0x0a, 0x61, 0x27, 0x7b, 0x02, 0xee, 0x80, 0xcb, 0x1e, 0xcd, 0x9f, 0x7b, 0x15, 0x9a,
	0x96, 0x1b, 0x5e, 0xd7, 0x3f, 0xf7, 0x64, 0x69, 0xd1, 0xe3, 0xfa, 0x27, 0x64, 0xf6, 0x3a, 0x4d,
	0x07, 0x2d, 0x2b, 0x6c, 0x44, 0x05, 0xd1, 0x1b, 0xe8, 0x7c, 0x29, 0x88, 0xa6, 0x62, 0x1e, 0xa0,
	0x3c, 0xae, 0xa0, 0xa3, 0x06, 0x17, 0xbc, 0x50, 0xbe, 0x91, 0xfa, 0x08, 0x15, 0xd2, 0x74, 0xcc,
	0x05, 0x2f, 0x9c, 0xcb, 0x46, 0x1f, 0xbc, 0x84, 0xbe, 0x89, 0xfa, 0x10, 0x92, 0x29, 0xee, 0x82,
	0x57, 0xd4, 0x53, 0x49, 0x91, 0x10, 0xec, 0xbd, 0x99, 0x7a, 0xd0, 0x35, 0x94, 0x31, 0x1a, 0x82,
	0xfb, 0x44, 0x43, 0xb0, 0xf7, 0x16, 0xea, 0x27, 0x14, 0x55, 0xe6, 0x68, 0x08, 0xee, 0x13, 0x0d,
	0xc1, 0xde, 0x5b, 0xa9, 0x0f, 0xa1, 0xb2, 0xcc, 0xd1, 0x10, 0xec, 0x9e, 0x42, 0x33, 0x06, 0x17,
	0xa2, 0x00, 0xbf, 0x8d, 0xfa, 0x90, 0xb2, 0xcb, 0x8b, 0x79, 0x11, 0xe5, 0xbb, 0x8a, 0x2e, 0x32,
	0x45, 0x23, 0xfc, 0xbc, 0x9d, 0xfa, 0x51, 0x74, 0xd8, 0xd1, 0x78, 0x44, 0xa2, 0xb4, 0xaf, 0x18,
	0xe9, 0x85, 0x22, 0xff, 0x0e, 0xea, 0x46, 0x11, 0x66, 0x71, 0x6e, 0xa1, 0xec, 0xf7, 0x09, 0x8a,
	0x60, 0xef, 0x9d, 0xd4, 0x8b, 0x54, 0x6a, 0x09, 0x41, 0x11, 0xdc, 0x37, 0x28, 0x82, 0xbd, 0x77,
	0x51, 0x3f, 0x8a, 0x74, 0x4b, 0x0a, 0x8a, 0x60, 0xf7, 0x55, 0xc6, 0x2f, 0x8a, 0xf7, 0x8d, 0x1e,
	0xf5, 0x13, 0xd3, 0x72, 0xf1, 0x6f, 0x8c, 0xf7, 0x93, 0x1b, 0xcd, 0x1b, 0x07, 0x3a, 0xcb, 0xbb,
	0xa9, 0x2f, 0x93, 0xb8, 0x33, 0xec, 0x21, 0x68, 0x3a, 0x37, 0x1b, 0xf9, 0x86, 0xf6, 0xf3, 0x1e,
	0xea, 0xad, 0x9f, 0xda, 0x8b, 0x7f, 0x01, 0xd0, 0xa1, 0xee, 0x46, 0x97, 0x18, 0x9c, 0x2a, 0xbd,
	0xea, 0xbd, 0xd9, 0xf4, 0xbd, 0x4a, 0x48, 0xb2, 0x99, 0x18, 0xb8, 0xec, 0x5d, 0x3f, 0x8d, 0x2e,
	0x35, 0x66, 0x97, 0x52, 0xeb, 0xef, 0xcd, 0xa6, 0xa9, 0xf5, 0x02, 0xfc, 0x62, 0x43, 0x4e, 0x46,
	0x74, 0xe1, 0x76, 0x7d, 0x2d, 0x2c, 0x91, 0xff, 0x9c, 0x2d, 0x65, 0x41, 0x17, 0xc2, 0xa8, 0xd4,
	0x85, 0xdc, 0x0a, 0x2a, 0xd0, 0xbf, 0x50, 0x33, 0xa6, 0x0b, 0x61, 0x58, 0xd1, 0x85, 0xdc, 0x8e,
	0x97, 0xbb, 0x7f, 0xa5, 0x86, 0x4c, 0x17, 0xc2, 0xb8, 0xaa, 0x0b, 0xb9, 0x25, 0x2f, 0x6a, 0xff,
	0x46, 0x2d, 0x0b, 0xd2, 0x52, 0xd5, 0x85, 0x12, 0x9b, 0x60, 0xef, 0xdf, 0xa9, 0x61, 0x56, 0xc3,
	0x16, 0x3a, 0x47, 0xc1, 0x26, 0xd8, 0xfb, 0x0f, 0x6a, 0xe8, 0xea, 0xd8, 0x11, 0x4b, 0x5e, 0x82,
	0xfe, 0x93, 0x5a, 0x0e, 0xeb, 0xd8, 0x42, 0x17, 0x72, 0x4b, 0x51, 0x21, 0xfe, 0x8b, 0x9a, 0x32,
	0x5d, 0x08, 0x1f, 0x68, 0xba, 0x50, 0xe0, 0x0b, 0xe3, 0xff, 0xa6, 0xc6, 0x4c, 0x17, 0xf2, 0x15,
	0x68, 0xba, 0x50, 0x78, 0x66, 0x25, 0xe3, 0x7f, 0xa8, 0x69, 0x46, 0x46, 0xa5, 0xe8, 0x42, 0x75,
	0x05, 0x04, 0x7b, 0xff, 0x4b, 0x2d, 0xf3, 0x91, 0x15, 0x08, 0x5d, 0xa8, 0xad, 0x80, 0x60, 0xef,
	0xff, 0xa8, 0x71, 0x31, 0xba, 0x82, 0x08, 0x0b, 0x3c, 0xbf, 0x2f, 0x0c, 0x97, 0xb2, 0xa0, 0x0b,
	0x61, 0x5c, 0xd5, 0x85, 0xc2, 0x2f, 0x64, 0xef, 0xcf, 0x0c, 0xb3, 0xb3, 0x5d, 0xc9, 0xac, 0xa2,
	0x0b, 0xc5, 0x6e, 0x62, 0x89, 0xf9, 0x7a, 0x6a, 0x38, 0x26, 0xa3, 0x52, 0x74, 0x21, 0xb7, 0x53,
	0x72, 0xed, 0x67, 0xa9, 0xf1, 0x00, 0xba, 0x10, 0x3c, 0x46, 0x74, 0xa1, 0x06, 0x23, 0x64, 0xcd,
	0xcf, 0x51, 0xa4, 0xc1, 0x74, 0xa1, 0x02, 0xa6, 0xe9, 0x42, 0x8e, 0xa7, 0xea, 0xc2, 0x9f, 0x07,
	0xb4, 0xf4, 0xba, 0x10, 0x7c, 0x46, 0x75, 0x61, 0xb8, 0xd9, 0x95, 0x5a, 0xf1, 0x0b, 0x40, 0x61,
	0x0a, 0x5d, 0x28, 0x52, 0x24, 0xa2, 0x0b, 0x23, 0x20, 0x82, 0xc0, 0x5f, 0x84, 0x90, 0xd2, 0xe9,
	0x42, 0x0d, 0x4a, 0xd3, 0x85, 0xf0, 0x09, 0xb0, 0xf6, 0x4b, 0x14, 0xc2, 0x56, 0x17, 0x82, 0x83,
	0x50, 0x17, 0x2a, 0xfe, 0xdc, 0x9f, 0x44, 0x85, 0xe6, 0xda, 0x36, 0xab, 0x72, 0x50, 0xea, 0xbe,
	0x0d, 0x31, 0xfc, 0xb0, 0x05, 0xc0, 0x8d, 0x6b, 0xdb, 0xb4, 0x20, 0xd2, 0xff, 0x4e, 0xb5, 0x3a,
	0x3b, 0xe7, 0x83, 0x83, 0x4d, 0x39, 0xe2, 0x9e, 0x45, 0xe3, 0x21, 0x02, 0xd4, 0xb4, 0xdf, 0x02,
	0x88, 0x2b, 0xed, 0x21, 0x58, 0x41, 0x05, 0x8c, 0xb1, 0xa6, 0x32, 0xe4, 0x6e, 0xa0, 0x89, 0x10,
	0x84, 0xd7, 0xd8, 0xdf, 0x06, 0x94, 0xab, 0xec, 0x51, 0xa0, 0x1a, 0x03, 0x4c, 0xa1, 0xa9, 0x8e,
	0x69, 0x38, 0xbc, 0x42, 0xff, 0x4e, 0x6a, 0x9c, 0xd3, 0x06, 0x1c, 0x5e, 0xdf, 0x23, 0xa4, 0x11,
	0xec, 0xfd, 0xee, 0x20, 0xa4, 0x11, 0x1c, 0x23, 0x8d, 0xe0, 0x18, 0x69, 0x04, 0x7b, 0xbf, 0x37,
	0x10, 0x69, 0x02, 0x46, 0x25, 0x2d, 0x82, 0xc3, 0x5b, 0xcb, 0x77, 0x06, 0x22, 0x2d, 0x8a, 0xc3,
	0x1b, 0xd3, 0x16, 0x2a, 0x86, 0x38, 0xa2, 0xd7, 0xfc, 0x3e, 0x00, 0x5d, 0x6d, 0x0f, 0xc4, 0x5b,
	0x18, 0x20, 0x8d, 0x37, 0xb5, 0x41, 0xb7, 0x81, 0x26, 0x25, 0x75, 0x02, 0xeb, 0x0f, 0x00, 0xeb,
	0x9a, 0x14, 0xe4, 0x6d, 0xa8, 0x60, 0x13, 0x4d, 0x7d, 0x54, 0xdb, 0x0d, 0xd0, 0x17, 0xff, 0x30,
	0xf5, 0x6e, 0x60, 0x1d, 0x54, 0xdf, 0x0d, 0xd0, 0x54, 0x63, 0xec, 0x11, 0xec, 0xfd, 0xd1, 0x60,
	0xec, 0x89, 0xef, 0x49, 0x63, 0x8f, 0x60, 0x03, 0x7b, 0x04, 0x7b, 0x7f, 0x3c, 0x20, 0x7b, 0x02,
	0x4c, 0x67, 0x2f, 0xb2, 0xfd, 0x78, 0x4f, 0xff, 0x93, 0xd4, 0xdb, 0x0f, 0xba, 0xbf, 0xbe, 0xfd,
	0xb8, 0x22, 0xd0, 0xd2, 0x09, 0x14, 0xc1, 0x9f, 0xa6, 0x4f, 0x27, 0xe6, 0x20, 0x92, 0x4e, 0xa0,
	0x27, 0xd4, 0xdd, 0x00, 0x7a, 0xe2, 0xcf, 0x52, 0xef, 0x06, 0xa6, 0x3c, 0xf4, 0xdd, 0x00, 0x62,
	0x64, 0x1b, 0x1d, 0x0a, 0x41, 0x14, 0x31, 0xf2, 0xe7, 0x80, 0xf4, 0x0a, 0x7b, 0xa4, 0x50, 0x80,
	0x00, 0x5a, 0xb1, 0x19, 0x19, 0x76, 0xcf, 0xa3, 0xa9, 0x08, 0xa2, 0x68, 0xab, 0x7f, 0x01, 0xa0,
	0xd7, 0xa6, 0x04, 0xe5, 0x63, 0x80, 0x7b, 0xa8, 0x19, 0xff, 0xc4, 0xdd, 0x45, 0x87, 0x43, 0x68,
	0x55, 0xa2, 0xfc, 0x25, 0x00, 0x2f, 0xdb, 0x03, 0x4b, 0x55, 0x02, 0xb0, 0x93, 0xcd, 0xe8, 0xb8,
	0x7b, 0x07, 0x3a, 0xa2, 0x54, 0x5f, 0x45, 0xad, 0x7c, 0x17, 0x50, 0x57, 0xd2, 0xd4, 0xe0, 0x50,
	0xa7, 0x00, 0xac, 0xdb, 0x8c, 0x7d, 0xe0, 0xde, 0x8d, 0xbc, 0x18, 0xae, 0x60, 0xfa, 0xaf, 0x00,
	0xfa, 0x54, 0x6a, 0x68, 0x8d, 0xeb, 0x23, 0x4d, 0xd3, 0x67, 0x62, 0xff, 0xb2, 0x46, 0x07, 0x9a,
	0xe3, 0xaf, 0x53, 0xed, 0x5f, 0xd6, 0xf9, 0xa5, 0xe8, 0xa0, 0xfb, 0x37, 0x1c, 0x12, 0xc9, 0xb8,
	0xab, 0xa0, 0xfc, 0x4d, 0xaa, 0x64, 0x84, 0xc6, 0x2f, 0x61, 0x68, 0x32, 0xca, 0x31, 0x81, 0xd3,
	0x55, 0x70, 0xfe, 0x36, 0x15, 0xce, 0x69, 0x03, 0x8e, 0x1c, 0x53, 0x48, 0x23, 0x18, 0x60, 0xfe,
	0x2e, 0x2d, 0x69, 0x04, 0xc7, 0x48, 0x83, 0x21, 0x95, 0x34, 0x81, 0xf2, 0xf7, 0xa9, 0x49, 0x53,
	0x61, 0x04, 0x69, 0x3a, 0x4e, 0x57, 0xc1, 0xf9, 0x87, 0xd4, 0xa4, 0x45, 0x71, 0xe4, 0x98, 0x68,
	0x69, 0xbc, 0x8d, 0x02, 0xd0, 0x3f, 0xa6, 0x6a, 0x69, 0xbc, 0xef, 0x4b, 0x24, 0xfa, 0x6d, 0x28,
	0x83, 0x21, 0x75, 0xac, 0x44, 0x03, 0xd2, 0x3f, 0xa5, 0xa3, 0x8e, 0x79, 0x88, 0x50, 0x17, 0x8e,
	0xb9, 0x25, 0x84, 0xda, 0xad, 0x7a, 0x7b, 0x03, 0x20, 0x9e, 0xce, 0x95, 0x9c, 0xf2, 0x81, 0xea,
	0x50, 0x30, 0xca, 0x06, 0x99, 0xc5, 0x2c, 0x3a, 0x08, 0x16, 0x20, 0x4f, 0x9f, 0xa1, 0x26, 0xb9,
	0xea, 0x50, 0x00, 0xf3, 0x40, 0x2e, 0xbf, 0x0c, 0x8d, 0x81, 0x0d, 0xd7, 0xca, 0xcf, 0x52, 0xa3,
	0xc9, 0xea, 0x50, 0x00, 0x53, 0xb9, 0xd8, 0x0d, 0xad, 0xb8, 0xd2, 0x7d, 0x8e, 0x5a, 0x15, 0x42,
	0x2b, 0x2e, 0x55, 0x55, 0x3c, 0x82, 0xbd, 0xe7, 0xa9, 0x51, 0x56, 0xc5, 0x23, 0x58, 0xc7, 0x23,
	0xd8, 0xfb, 0x0c, 0x35, 0x72, 0x35, 0x3c, 0xd5, 0x8a, 0x8b, 0xc4, 0xcf, 0x52, 0xab, 0x61, 0x0d,
	0x8f, 0x60, 0xf7, 0xe5, 0xa8, 0x00, 0x56, 0x42, 0x76, 0x7d, 0x8e, 0x9a, 0x8d, 0x54, 0x87, 0x02,
	0x98, 0x2d, 0x24, 0x5a, 0x19, 0x8d, 0x73, 0x4c, 0x61, 0xf8, 0x79, 0x6a, 0x38, 0x51, 0x1d, 0x0a,
	0xc0, 0x41, 0x28, 0xaf, 0xc2, 0x08, 0x40, 0x5b, 0xfd, 0x32, 0x35, 0xcb, 0x84, 0x11, 0x80, 0x3a,
	0xd2, 0x51, 0x09, 0xf6, 0x7e, 0x85, 0x5a, 0xe5, 0x75, 0x54, 0x76, 0x80, 0xa0, 0xa1, 0x12, 0xec,
	0xfd, 0x2a, 0x35, 0x2c, 0x46, 0x50, 0xd5, 0x68, 0xb9, 0x26, 0x79, 0x81, 0xda, 0x39, 0x61, 0xb4,
	0x5c, 0x54, 0x48, 0xe6, 0x40, 0x51, 0x7c, 0x81, 0x5a, 0x8d, 0x4a, 0xe6, 0x40, 0x12, 0x84, 0x11,
	0x80, 0x1e, 0xf8, 0x22, 0x35, 0x1a, 0x0b, 0x23, 0x80, 0x8e, 0xbe, 0x86, 0x8a, 0x60, 0xa3, 0xb4,
	0xf3, 0x2f, 0xe5, 0xd2, 0x3f, 0xc6, 0xad, 0x0e, 0x05, 0x10, 0xaa, 0x6c, 0xe1, 0xb7, 0xa2, 0x43,
	0x2a, 0x84, 0xe8, 0x2a, 0x5f, 0xce, 0x0d, 0xf4, 0x8a, 0x4d, 0x75, 0x28, 0x98, 0x94, 0x40, 0xa2,
	0x8b, 0xac, 0x23, 0x18, 0xd4, 0x1a, 0xf6, 0x57, 0x72, 0x03, 0xbc, 0x5f, 0x53, 0x1d, 0x0a, 0x26,
	0x98, 0x4b, 0xa5, 0x49, 0xaf, 0x22, 0x57, 0x6c, 0x5c, 0xa5, 0x43, 0x7f, 0x35, 0x97, 0xe6, 0x59,
	0x74, 0x75, 0x28, 0x28, 0xf2, 0xed, 0x2e, 0xbb, 0xf1, 0x39, 0x74, 0x44, 0x07, 0x10, 0xa4, 0x7d,
	0x2d, 0x97, 0xf2, 0xcd, 0x9a, 0xea, 0x50, 0x70, 0x48, 0x85, 0x11, 0x84, 0xfd, 0x18, 0xaf, 0x1c,
	0xc0, 0xd4, 0xd7, 0x73, 0xd6, 0xaf, 0x09, 0xde, 0x44, 0x67, 0x0b, 0xa6, 0x14, 0x5f, 0x32, 0x37,
	0x60, 0x8f, 0x2e, 0x78, 0xdf, 0x10, 0x9b, 0x74, 0x4c, 0xd9, 0xa4, 0x0b, 0x51, 0x3b, 0xdf, 0xfb,
	0xa6, 0xc9, 0xce, 0x8f, 0xda, 0x2d, 0x7a, 0xdf, 0x32, 0xd9, 0x2d, 0xba, 0x27, 0xd1, 0x61, 0x9e,
	0x41, 0xfa, 0x03, 0xad, 0x7b, 0xf3, 0xf2, 0x85, 0x9e, 0xaa, 0x13, 0xc0, 0x37, 0xa8, 0x3f, 0xcf,
	0xba, 0x4a, 0xd0, 0x1e, 0x7d, 0x98, 0xf5, 0xbe, 0xbc, 0xfa, 0x76, 0x4f, 0xd5, 0xe1, 0x5c, 0x46,
	0x9e, 0x65, 0x5d, 0x8d, 0xa6, 0xa2, 0xd3, 0x79, 0x25, 0xbd, 0x2f, 0xaf, 0xbc, 0xea, 0x53, 0x75,
	0x82, 0xc3, 0xfa, 0x74, 0x5e, 0x59, 0xaf, 0x8a, 0xcf, 0xe7, 0x35, 0xf6, 0xfe, 0xbc, 0x7c, 0xef,
	0x27, 0x3e, 0xfd, 0xb4, 0x78, 0x0c, 0x66, 0x5a, 0x3d, 0xc1, 0xde, 0x03, 0xf9, 0xe8, 0x4b, 0x40,
	0xc6, 0x08, 0x08, 0x4e, 0x8a, 0x80, 0x60, 0xef, 0xc1, 0xbc, 0xf2, 0x46, 0x90, 0x39, 0x02, 0x82,
	0x93, 0x22, 0x20, 0xd8, 0x7b, 0x28, 0x2f, 0x5f, 0x0f, 0x32, 0x47, 0xc0, 0x1e, 0x7d, 0x4d, 0x47,
	0xa7, 0x8b, 0x2a, 0xfd, 0x70, 0x5e, 0x7d, 0x57, 0xa8, 0xea, 0x04, 0x47, 0x74, 0x0f, 0xa2, 0xbe,
	0x5f, 0x87, 0xbc, 0x58, 0x04, 0xc2, 0xc7, 0x23, 0x79, 0xed, 0xc5, 0xa1, 0xaa, 0x13, 0x4c, 0x45,
	0xa2, 0x10, 0xb5, 0xff, 0xea, 0x38, 0x95, 0xd0, 0x05, 0xde, 0x9f, 0xd7, 0xde, 0x22, 0x8a, 0xf3,
	0x08, 0x7d, 0x21, 0x29, 0x10, 0x82, 0xbd, 0x0f, 0xe4, 0xd5, 0x57, 0x8a, 0x12, 0x02, 0x21, 0x38,
	0x39, 0x10, 0x82, 0xbd, 0x47, 0xf3, 0xda, 0xfb, 0x45, 0x49, 0x81, 0x10, 0xec, 0x5e, 0x1f, 0xff,
	0x42, 0x78, 0x63, 0x79, 0x2c, 0x6f, 0x78, 0xd9, 0x28, 0xfe, 0xcd, 0xf0, 0x86, 0x73, 0x83, 0x61,
	0x63, 0x40, 0xeb, 0x79, 0x3c, 0x6f, 0x7e, 0xf3, 0xc8, 0xb0, 0x47, 0xa0, 0x2b, 0xdd, 0x14, 0xe7,
	0x16, 0xfa, 0xd3, 0x13, 0xf9, 0xfe, 0xaf, 0x21, 0xc5, 0xc9, 0x86, 0x16, 0xf6, 0x5a, 0x34, 0x13,
	0x75, 0xa8, 0x34, 0xb3, 0x27, 0xf3, 0x03, 0xbf, 0x93, 0x54, 0x75, 0x82, 0x69, 0x1d, 0x58, 0xfd,
	0xfb, 0xf4, 0xe2, 0x78, 0xc6, 0x28, 0x4d, 0xe1, 0xa9, 0xfc, 0x00, 0x2f, 0x28, 0x55, 0x9d, 0xe0,
	0x68, 0x34, 0xcf, 0x42, 0x9b, 0x99, 0x9f, 0x42, 0x63, 0x5a, 0xef, 0x7b, 0x11, 0xdf, 0x34, 0x9f,
	0xb9, 0x0b, 0x21, 0xa5, 0x1f, 0xbe, 0x98, 0xc8, 0xd7, 0xa0, 0x82, 0xf6, 0x26, 0xa7, 0x2d, 0x38,
	0x75, 0xa0, 0xbd, 0x13, 0x91, 0xce, 0x81, 0x72, 0x78, 0x6e, 0xed, 0xe0, 0x6a, 0x54, 0x8c, 0x1e,
	0x8e, 0xbb, 0x45, 0x94, 0xbd, 0xad, 0x7e, 0x9e, 0x39, 0x39, 0x10, 0xd0, 0xff, 0x75, 0x0f, 0xa3,
	0xdc, 0x1d, 0x6b, 0x8d, 0x6e, 0xdd, 0xcb, 0xb0, 0x31, 0xf8, 0x47, 0x25, 0x73, 0x85, 0x33, 0x73,
	0x0d, 0x9a, 0x8c, 0x9d, 0x7c, 0xef, 0xe5, 0x20, 0xa7, 0x3a, 0x78, 0x05, 0x72, 0xe3, 0x87, 0xda,
	0x7b, 0x79, 0x98, 0x34, 0x7b, 0x38, 0xbd, 0x7f, 0x0f, 0x85, 0xc4, 0x20, 0xf8, 0x29, 0xdd, 0x5e,
	0x0e, 0xb2, 0xc9, 0x41, 0xec, 0xd3, 0x83, 0x9b, 0x1c, 0xc4, 0x3e, 0x3d, 0x0c, 0xab, 0x1e, 0x96,
	0xd1, 0x21, 0xc3, 0xb9, 0xf0, 0x5e, 0x2e, 0x46, 0x54, 0x17, 0x2b, 0xe8, 0xb0, 0xe9, 0xb8, 0x77,
	0x2f, 0x1f, 0x13, 0x66, 0x2e, 0xe5, 0x39, 0xee, 0x5e, 0x0e, 0x32, 0x7d, 0xe2, 0xd8, 0x27, 0x15,
	0xf9, 0x7e, 0x71, 0xec, 0xd3, 0x47, 0xd1, 0xfc, 0x85, 0x28, 0x07, 0xaa, 0x7b, 0x79, 0x70, 0x12,
	0x36, 0x85, 0x3c, 0x2a, 0xdd, 0xcb, 0xc3, 0xa8, 0x99, 0x4b, 0x79, 0x0a, 0xba, 0x97, 0x83, 0x31,
	0xd5, 0xc1, 0x79, 0x74, 0xc4, 0x78, 0xb8, 0x69, 0x70, 0xf2, 0x2a, 0xd5, 0x49, 0xda, 0x87, 0xb9,
	0x0a, 0xf4, 0xdd, 0xc8, 0x4b, 0x3a, 0xe2, 0x34, 0xa0, 0xdf, 0xa8, 0xa2, 0x0f, 0xf0, 0x80, 0x57,
	0x59, 0xc0, 0x6b, 0xd1, 0x94, 0xf9, 0xa8, 0xd3, 0x00, 0xff, 0x23, 0x3a, 0x7c, 0xca, 0x27, 0xbe,
	0x0a, 0x78, 0x17, 0x4d, 0x27, 0x9c, 0x78, 0x1a, 0xd0, 0xaf, 0xd3, 0xa9, 0xb7, 0x7d, 0x08, 0xac,
	0xc5, 0x3c, 0x93, 0x7c, 0xda, 0x69, 0x40, 0x7e, 0xa5, 0x1e, 0x77, 0x8a, 0xc7, 0xc2, 0xb1, 0xdd,
	0xaa, 0x9f, 0x79, 0xaa, 0x98, 0xb9, 0xbd, 0x7a, 0x09, 0x24, 0x4c, 0xe4, 0x38, 0x53, 0xf5, 0x30,
	0xb9, 0x3f, 0x0f, 0xa7, 0x93, 0x3d, 0x14, 0xf6, 0xd7, 0xcf, 0xf4, 0x33, 0x48, 0xd5, 0x41, 0x76,
	0xff, 0x41, 0x24, 0x78, 0x70, 0xf7, 0x1f, 0x44, 0x82, 0x87, 0xe1, 0xbd, 0x3c, 0x40, 0x09, 0x8d,
	0x9e, 0x08, 0xaa, 0x2e, 0x46, 0xf6, 0x19, 0x86, 0x7e, 0xd4, 0xa7, 0x7a, 0x18, 0xdd, 0xcb, 0xc3,
	0x95, 0x08, 0xc9, 0xbf, 0xc7, 0xad, 0x75, 0x49, 0x15, 0x4d, 0x9d, 0xba, 0xab, 0x53, 0x6f, 0xed,
	0x6e, 0xb5, 0x5b, 0x83, 0x69, 0x2c, 0xd5, 0xd3, 0x40, 0x5a, 0x69, 0x76, 0x0e, 0x8d, 0x4a, 0xb1,
	0x3d, 0x8a, 0x40, 0x17, 0x17, 0x87, 0xe8, 0xff, 0xae, 0x04, 0xcb, 0x3f, 0x7a, 0x53, 0xd1, 0x71,
	0x0f, 0xa2, 0x91, 0x6b, 0xab, 0xcb, 0xc1, 0xab, 0x6f, 0x38, 0x55, 0xcc, 0x5c, 0x36, 0x7a, 0xe0,
	0x9e, 0x5a, 0xf1, 0xc2, 0x85, 0x0b, 0x17, 0x32, 0xfe, 0x59, 0x34, 0x5d, 0x17, 0x8b, 0x58, 0xd5,
	0xee, 0x2c, 0xba, 0x16, 0xa2, 0xd3, 0xbb, 0xa7, 0xc6, 0x58, 0x3e, 0x52, 0x8f, 0x52, 0x43, 0xbf,
	0x22, 0xbf, 0x8e, 0x3c, 0x03, 0x08, 0xfc, 0x41, 0x6e, 0x83, 0xf2, 0x86, 0x1a, 0xcb, 0xd6, 0xa9,
	0x18, 0x0a, 0xcb, 0x6d, 0x7f, 0x13, 0x1d, 0x35, 0xc0, 0xec, 0xda, 0xe3, 0xbc, 0xb1, 0xc6, 0x72,
	0x7a, 0x3a, 0x86, 0x03, 0x25, 0x20, 0x01, 0xa8, 0x6b, 0x0f, 0xf4, 0xa6, 0x1a, 0x4b, 0xfd, 0x38,
	0x10, 0x54, 0x8a, 0x64, 0xe2, 0x08, 0xb6, 0xc2, 0x79, 0x73, 0x8d, 0x55, 0x08, 0x23, 0x71, 0x04,
	0xf7, 0x21, 0xce, 0x12, 0xe7, 0x2d, 0x35, 0x56, 0x47, 0xcc, 0xc4, 0x25, 0x02, 0x75, 0xed, 0x81,
	0xde, 0x5a, 0x63, 0xe5, 0xc6, 0x4c, 0x1c, 0xc1, 0xfe, 0x16, 0x9a, 0x31, 0x00, 0x89, 0x93, 0x0b,
	0x1b, 0xa4, 0xb7, 0xd5, 0x58, 0x55, 0xf2, 0x62, 0x48, 0xbc, 0x8a, 0xf9, 0xb7, 0xa1, 0x8b, 0x4c,
	0xe4, 0xa5, 0xc1, 0x7a, 0x7b, 0x8d, 0x89, 0xd6, 0xa3, 0x71, 0xfa, 0xb8, 0xb7, 0x84, 0x0d, 0xb1,
	0x01, 0xaf, 0xf6, 0x59, 0x20, 0xbd, 0xa3, 0xc6, 0xd4, 0x6d, 0x7c, 0x43, 0x30, 0x6d, 0xdc, 0x8f,
	0x3e, 0xcb, 0x2f, 0xea, 0x9d, 0x35, 0xa6, 0x81, 0x13, 0xe8, 0x23, 0xb8, 0x2f, 0x7d, 0x96, 0x58,
	0xef, 0xaa, 0x31, 0xad, 0x9c, 0x44, 0x5f, 0xe2, 0xfe, 0x83, 0xc3, 0x1e, 0x2b, 0xa8, 0x5e, 0x8d,
	0x89, 0xea, 0xf8, 0xfe, 0x03, 0x4d, 0x9e, 0x94, 0x51, 0x70, 0xb8, 0x63, 0x03, 0xf4, 0xee, 0x1a,
	0xeb, 0x02, 0x86, 0x8c, 0x82, 0x13, 0x5f, 0xf3, 0x86, 0x60, 0x67, 0x45, 0x56, 0x38, 0xef, 0xa9,
	0x31, 0x89, 0x1e, 0xdf, 0x10, 0x4c, 0xe0, 0xfb, 0x0f, 0x38, 0xe8, 0x12, 0x03, 0x8e, 0x3c, 0x42,
	0xb2, 0x02, 0x7b, 0x6f, 0x6d, 0x00, 0x29, 0x3f, 0x13, 0x5b, 0x62, 0xf8, 0x99, 0xff, 0xb8, 0x83,
	0x4a, 0x89, 0xcb, 0xe4, 0x8f, 0x07, 0xac, 0x56, 0x7a, 0x6f, 0x6d, 0x30, 0xd9, 0x7f, 0x89, 0x79,
	0xb1, 0xfc, 0x63, 0xff, 0x61, 0x07, 0x7d, 0x9f, 0x61, 0xbd, 0xca, 0x73, 0x19, 0xab, 0xd5, 0xbe,
	0xaf, 0x36, 0xc8, 0x5f, 0x09, 0x17, 0xc5, 0xd6, 0x2a, 0x3f, 0xf4, 0xef, 0x73, 0xd0, 0xa5, 0xc6,
	0x1e, 0x21, 0x8f, 0xf1, 0xac, 0x96, 0x7a, 0x5f, 0x2d, 0xd5, 0x9f, 0x14, 0x17, 0x1b, 0x3a, 0x4b,
	0xf8, 0xa9, 0xff, 0xa8, 0x83, 0x66, 0xfb, 0x2c, 0x32, 0xcd, 0x06, 0xb8, 0xbf, 0x96, 0xf6, 0x0f,
	0x90, 0x4b, 0x93, 0x96, 0x2a, 0xbe, 0xfc, 0x87, 0x1c, 0x24, 0xd3, 0x4d, 0xbf, 0x69, 0x6d, 0xb3,
	0xc2, 0x07, 0x6a, 0xec, 0x71, 0x94, 0xcd, 0x9b, 0x36, 0x66, 0x01, 0x1b, 0x24, 0xac, 0xc6, 0x6f,
	0xa8, 0x35, 0x46, 0x7f, 0x60, 0x64, 0x97, 0x4c, 0x3d, 0xf5, 0xba, 0xb8, 0x44, 0xd3, 0x9e, 0x2f,
	0xf9, 0xdb, 0x6a, 0xe9, 0x8c, 0x3c, 0x63, 0xb2, 0xcb, 0x86, 0x9e, 0x7e, 0xdd, 0x7c, 0x3a, 0x0e,
	0x08, 0xba, 0xf1, 0x76, 0xb5, 0xdb, 0x45, 0x1f, 0x4b, 0xd9, 0xed, 0xea, 0x9e, 0x76, 0x5d, 0xdd,
	0x8b, 0x23, 0x72, 0x05, 0xb9, 0x6d, 0x86, 0x4c, 0x21, 0x21, 0xef, 0xef, 0xa9, 0xd7, 0xdd, 0x0d,
	0x88, 0x5c, 0x4a, 0x76, 0x12, 0x69, 0xb5, 0xec, 0xb2, 0x0f, 0xf4, 0xe2, 0xd7, 0xe5, 0xcd, 0xd4,
	0x12, 0xdc, 0x8f, 0x5a, 0x4b, 0xd8, 0x07, 0x7b, 0xda, 0x75, 0xfb, 0x04, 0x6a, 0x09, 0xee, 0x47,
	0xad, 0x25, 0xe4, 0x43, 0x3d, 0xf5, 0xba, 0x7e, 0x02, 0xb5, 0x04, 0xfb, 0x1d, 0x55, 0xc2, 0xc4,
	0x9e, 0xca, 0x59, 0x41, 0x3e, 0xdc, 0xd3, 0xaf, 0xfb, 0x1f, 0x8d, 0x83, 0x0a, 0xdd, 0x79, 0x27,
	0xba, 0xd8, 0x48, 0x6d, 0x1a, 0xd8, 0x47, 0x7a, 0x91, 0x9f, 0x0b, 0x98, 0x31, 0xd0, 0x2b, 0x34,
	0xe8, 0xed, 0xe6, 0x9d, 0x64, 0x2f, 0x42, 0xdf, 0xdf, 0x8b, 0xfc, 0xdc, 0x80, 0x61, 0x1b, 0x81,
	0x1e, 0xed, 0xc7, 0xb0, 0xe5, 0x97, 0xfa, 0x81, 0x9e, 0xfe, 0x73, 0x05, 0x49, 0x0c, 0x13, 0xdc,
	0x9f, 0x61, 0x4b, 0xd8, 0x47, 0x7b, 0x91, 0x9f, 0x3b, 0x48, 0x64, 0x98, 0x60, 0xff, 0xbc, 0x79,
	0x0b, 0xa7, 0xd0, 0xa9, 0x8f, 0xf5, 0x8c, 0x3f, 0x97, 0x60, 0xd8, 0xcb, 0x5c, 0xb8, 0xbe, 0x2e,
	0x21, 0x61, 0xed, 0x95, 0xeb, 0xe3, 0xbd, 0xa4, 0x9f, 0x5b, 0x30, 0xe5, 0x2e, 0xa8, 0xd9, 0xd7,
	0x3b, 0xe6, 0xbd, 0x65, 0xaf, 0x67, 0x9f, 0xe8, 0xed, 0xf5, 0x7b, 0x0d, 0x86, 0xcd, 0x06, 0x5a,
	0xf7, 0x09, 0x4d, 0x94, 0x99, 0x9e, 0x97, 0x5a, 0xad, 0xe4, 0xc9, 0xde, 0xf7, 0xe0, 0x07, 0x1f,
	0x2e, 0x8a, 0x2f, 0x56, 0xaa, 0xde, 0xc7, 0x34, 0xd5, 0x6b, 0x7e, 0xc6, 0x6a, 0xb5, 0xe4, 0xa7,
	0x7a, 0x03, 0xfd, 0x62, 0xc4, 0x25, 0xa6, 0xda, 0x2c, 0x55, 0xda, 0xba, 0x7a, 0xe4, 0xa4, 0x5d,
	0x16, 0xb4, 0x5b, 0xe4, 0x77, 0x1d, 0x76, 0xb3, 0x50, 0x9e, 0x39, 0x05, 0xca, 0x15, 0x43, 0x7f,
	0x43, 0x15, 0x2d, 0xfa, 0x65, 0x43, 0x2b, 0x98, 0x0f, 0x32, 0x18, 0xf5, 0xd0, 0x29, 0x50, 0xaf,
	0x28, 0xfa, 0xe7, 0xd4, 0x1d, 0x1b, 0xb9, 0xac, 0x68, 0x05, 0xf4, 0x21, 0x06, 0xa4, 0x9e, 0x3a,
	0x05, 0xda, 0x15, 0xc7, 0x04, 0xa4, 0x14, 0x92, 0xe1, 0xc3, 0x0c, 0xa9, 0x60, 0x40, 0xe2, 0x5a,
	0x21, 0x91, 0x3b, 0xcb, 0xa2, 0xf7, 0x11, 0x06, 0x94, 0x35, 0x73, 0x47, 0x70, 0x1f, 0xee, 0x2c,
	0x81, 0x3e, 0xca, 0x80, 0xdc, 0x04, 0xee, 0x12, 0x91, 0x52, 0x68, 0x82, 0x8f, 0x31, 0xa4, 0xe1,
	0x04, 0xee, 0x08, 0xf6, 0x6f, 0x55, 0x0b, 0x68, 0xf4, 0xb2, 0xa7, 0x15, 0xd4, 0xc7, 0x19, 0x94,
	0x7a, 0xf4, 0x14, 0xe8, 0x57, 0x44, 0xfd, 0x86, 0xda, 0x16, 0x63, 0x97, 0x45, 0xad, 0xc0, 0x3e,
	0xc1, 0xc0, 0xd4, 0xb3, 0xa7, 0x20, 0x72, 0xc5, 0x34, 0x61, 0x57, 0xd8, 0xb7, 0xfd, 0x4f, 0x32,
	0xa8, 0x8c, 0x61, 0x57, 0x40, 0xb3, 0xef, 0xc3, 0xa0, 0xe5, 0x97, 0xf5, 0x29, 0x86, 0x94, 0x4f,
	0x62, 0x90, 0xe0, 0xbe, 0x0c, 0x5a, 0x82, 0x7d, 0x9a, 0x81, 0x15, 0x13, 0x19, 0x4c, 0xdc, 0x85,
	0x29, 0xda, 0xfa, 0xd3, 0x0c, 0xcb, 0x31, 0xec, 0x42, 0xde, 0xc6, 0x13, 0x32, 0xcb, 0xbe, 0x8b,
	0x3f, 0xc3, 0x90, 0x46, 0x4d, 0x99, 0x05, 0x2d, 0xdb, 0xbc, 0x2b, 0xec, 0x1b, 0xf6, 0xb3, 0x0c,
	0x68, 0xcc, 0xb0, 0x2b, 0xa0, 0x2b, 0x3f, 0xa8, 0x9d, 0x40, 0x19, 0x6e, 0xfb, 0x5a, 0xa1, 0x3d,
	0xc7, 0xd0, 0x06, 0x3f, 0x82, 0x0a, 0xa2, 0x77, 0x84, 0xa9, 0x7a, 0x28, 0x25, 0xae, 0x33, 0xcd,
	0x09, 0xc4, 0xf3, 0x6c, 0xa9, 0xdf, 0x93, 0x33, 0xa8, 0xc0, 0x70, 0xc9, 0xd8, 0x7f, 0x44, 0x93,
	0x3b, 0xa6, 0xfb, 0xc6, 0x56, 0xcb, 0xfd, 0x0c, 0x5f, 0xee, 0xc0, 0x87, 0x50, 0x41, 0xec, 0x96,
	0xb2, 0x7f, 0xbf, 0x76, 0x08, 0x65, 0xbc, 0xb0, 0x6c, 0xb5, 0xd6, 0xcf, 0xf2, 0x5d, 0x90, 0xfe,
	0x14, 0x2a, 0x88, 0x5f, 0x73, 0xa6, 0x72, 0x6c, 0xb6, 0xcf, 0x2a, 0xd3, 0xec, 0x81, 0xcf, 0x71,
	0x52, 0x07, 0x3a, 0x86, 0x0a, 0x8c, 0xf7, 0xa4, 0xfd, 0x87, 0xd5, 0x63, 0x28, 0xfd, 0x86, 0xb3,
	0xcd, 0x12, 0x3f, 0xcf, 0x96, 0x98, 0xf2, 0x1c, 0x4a, 0xbf, 0x67, 0x9d, 0xb0, 0x9c, 0x95, 0x82,
	0x78, 0x55, 0xbf, 0xdb, 0xda, 0x6a, 0xb7, 0x56, 0xa6, 0xe3, 0xef, 0x48, 0xb2, 0x0f, 0x2e, 0x5b,
	0x40, 0x07, 0xd5, 0xf7, 0xc4, 0x4d, 0x0f, 0x44, 0x91, 0x3b, 0x26, 0x1f, 0x88, 0xbe, 0xe0, 0xac,
	0xbc, 0xe6, 0xc7, 0x6b, 0xb1, 0x65, 0x1f, 0x67, 0xcb, 0x3e, 0xd3, 0xdd, 0x38, 0xbe, 0xd5, 0xea,
	0xd4, 0x77, 0x5a, 0x6b, 0x0d, 0xf6, 0x3b, 0xb7, 0x6c, 0x74, 0xf7, 0x78, 0xa3, 0xbe, 0xb9, 0x76,
	0xf6, 0xfc, 0xf1, 0xa4, 0x9f, 0xc4, 0xfd, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x51, 0x2c, 0x45,
	0xc2, 0x2d, 0x57, 0x00, 0x00,
}
