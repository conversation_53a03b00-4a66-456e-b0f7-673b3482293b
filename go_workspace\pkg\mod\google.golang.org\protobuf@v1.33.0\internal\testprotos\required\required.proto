// Copyright 2020 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

syntax = "proto2";

package goproto.proto.testrequired;

option go_package = "google.golang.org/protobuf/internal/testprotos/required";

message Int32 {
  required int32 v = 1;
}

message Int64 {
  required int64 v = 1;
}

message Uint32 {
  required uint32 v = 1;
}

message Uint64 {
  required uint64 v = 1;
}

message Sint32 {
  required sint32 v = 1;
}

message Sint64 {
  required sint64 v = 1;
}

message Fixed32 {
  required fixed32 v = 1;
}

message Fixed64 {
  required fixed64 v = 1;
}

message Float {
  required float v = 1;
}

message Double {
  required double v = 1;
}

message Bool {
  required bool v = 1;
}

message String {
  required string v = 1;
}

message Bytes {
  required bytes v = 1;
}

message Message {
  message M {}
  required M v = 1;
}

message Group {
  required group Group = 1 {
    optional int32 v = 1;
  }
}
