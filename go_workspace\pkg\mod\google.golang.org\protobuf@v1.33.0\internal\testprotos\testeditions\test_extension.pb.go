// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/testeditions/test_extension.proto

package testeditions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type TestAllExtensions struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestAllExtensions) Reset() {
	*x = TestAllExtensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllExtensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllExtensions) ProtoMessage() {}

func (x *TestAllExtensions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllExtensions.ProtoReflect.Descriptor instead.
func (*TestAllExtensions) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_extension_proto_rawDescGZIP(), []int{0}
}

type OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                           `protobuf:"varint,17,opt,name=a" json:"a,omitempty"`
	SameFieldNumber       *int32                           `protobuf:"varint,16,opt,name=same_field_number,json=sameFieldNumber" json:"same_field_number,omitempty"`
	OptionalNestedMessage *TestAllExtensions_NestedMessage `protobuf:"bytes,1000,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
}

func (x *OptionalGroup) Reset() {
	*x = OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionalGroup) ProtoMessage() {}

func (x *OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionalGroup.ProtoReflect.Descriptor instead.
func (*OptionalGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_extension_proto_rawDescGZIP(), []int{1}
}

func (x *OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *OptionalGroup) GetSameFieldNumber() int32 {
	if x != nil && x.SameFieldNumber != nil {
		return *x.SameFieldNumber
	}
	return 0
}

func (x *OptionalGroup) GetOptionalNestedMessage() *TestAllExtensions_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

type RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                     *int32                           `protobuf:"varint,47,opt,name=a" json:"a,omitempty"`
	OptionalNestedMessage *TestAllExtensions_NestedMessage `protobuf:"bytes,1001,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
}

func (x *RepeatedGroup) Reset() {
	*x = RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedGroup) ProtoMessage() {}

func (x *RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedGroup.ProtoReflect.Descriptor instead.
func (*RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_extension_proto_rawDescGZIP(), []int{2}
}

func (x *RepeatedGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *RepeatedGroup) GetOptionalNestedMessage() *TestAllExtensions_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

type TestAllExtensions_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           *int32             `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllExtensions `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllExtensions_NestedMessage) Reset() {
	*x = TestAllExtensions_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllExtensions_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllExtensions_NestedMessage) ProtoMessage() {}

func (x *TestAllExtensions_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_testeditions_test_extension_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllExtensions_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllExtensions_NestedMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_testeditions_test_extension_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllExtensions_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllExtensions_NestedMessage) GetCorecursive() *TestAllExtensions {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

var file_internal_testprotos_testeditions_test_extension_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1,
		Name:          "goproto.proto.testeditions.optional_int32",
		Tag:           "varint,1,opt,name=optional_int32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2,
		Name:          "goproto.proto.testeditions.optional_int64",
		Tag:           "varint,2,opt,name=optional_int64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         3,
		Name:          "goproto.proto.testeditions.optional_uint32",
		Tag:           "varint,3,opt,name=optional_uint32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         4,
		Name:          "goproto.proto.testeditions.optional_uint64",
		Tag:           "varint,4,opt,name=optional_uint64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         5,
		Name:          "goproto.proto.testeditions.optional_sint32",
		Tag:           "zigzag32,5,opt,name=optional_sint32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         6,
		Name:          "goproto.proto.testeditions.optional_sint64",
		Tag:           "zigzag64,6,opt,name=optional_sint64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         7,
		Name:          "goproto.proto.testeditions.optional_fixed32",
		Tag:           "fixed32,7,opt,name=optional_fixed32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         8,
		Name:          "goproto.proto.testeditions.optional_fixed64",
		Tag:           "fixed64,8,opt,name=optional_fixed64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         9,
		Name:          "goproto.proto.testeditions.optional_sfixed32",
		Tag:           "fixed32,9,opt,name=optional_sfixed32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         10,
		Name:          "goproto.proto.testeditions.optional_sfixed64",
		Tag:           "fixed64,10,opt,name=optional_sfixed64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         11,
		Name:          "goproto.proto.testeditions.optional_float",
		Tag:           "fixed32,11,opt,name=optional_float",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         12,
		Name:          "goproto.proto.testeditions.optional_double",
		Tag:           "fixed64,12,opt,name=optional_double",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         13,
		Name:          "goproto.proto.testeditions.optional_bool",
		Tag:           "varint,13,opt,name=optional_bool",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         14,
		Name:          "goproto.proto.testeditions.optional_string",
		Tag:           "bytes,14,opt,name=optional_string",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         15,
		Name:          "goproto.proto.testeditions.optional_bytes",
		Tag:           "bytes,15,opt,name=optional_bytes",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*OptionalGroup)(nil),
		Field:         16,
		Name:          "goproto.proto.testeditions.optionalgroup",
		Tag:           "bytes,16,opt,name=optionalgroup",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*TestAllExtensions_NestedMessage)(nil),
		Field:         18,
		Name:          "goproto.proto.testeditions.optional_nested_message",
		Tag:           "bytes,18,opt,name=optional_nested_message",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*TestAllTypes_NestedEnum)(nil),
		Field:         21,
		Name:          "goproto.proto.testeditions.optional_nested_enum",
		Tag:           "varint,21,opt,name=optional_nested_enum,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         31,
		Name:          "goproto.proto.testeditions.repeated_int32",
		Tag:           "varint,31,rep,name=repeated_int32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         32,
		Name:          "goproto.proto.testeditions.repeated_int64",
		Tag:           "varint,32,rep,name=repeated_int64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         33,
		Name:          "goproto.proto.testeditions.repeated_uint32",
		Tag:           "varint,33,rep,name=repeated_uint32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         34,
		Name:          "goproto.proto.testeditions.repeated_uint64",
		Tag:           "varint,34,rep,name=repeated_uint64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         35,
		Name:          "goproto.proto.testeditions.repeated_sint32",
		Tag:           "zigzag32,35,rep,name=repeated_sint32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         36,
		Name:          "goproto.proto.testeditions.repeated_sint64",
		Tag:           "zigzag64,36,rep,name=repeated_sint64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         37,
		Name:          "goproto.proto.testeditions.repeated_fixed32",
		Tag:           "fixed32,37,rep,name=repeated_fixed32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         38,
		Name:          "goproto.proto.testeditions.repeated_fixed64",
		Tag:           "fixed64,38,rep,name=repeated_fixed64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         39,
		Name:          "goproto.proto.testeditions.repeated_sfixed32",
		Tag:           "fixed32,39,rep,name=repeated_sfixed32",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         40,
		Name:          "goproto.proto.testeditions.repeated_sfixed64",
		Tag:           "fixed64,40,rep,name=repeated_sfixed64",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         41,
		Name:          "goproto.proto.testeditions.repeated_float",
		Tag:           "fixed32,41,rep,name=repeated_float",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         42,
		Name:          "goproto.proto.testeditions.repeated_double",
		Tag:           "fixed64,42,rep,name=repeated_double",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         43,
		Name:          "goproto.proto.testeditions.repeated_bool",
		Tag:           "varint,43,rep,name=repeated_bool",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         44,
		Name:          "goproto.proto.testeditions.repeated_string",
		Tag:           "bytes,44,rep,name=repeated_string",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([][]byte)(nil),
		Field:         45,
		Name:          "goproto.proto.testeditions.repeated_bytes",
		Tag:           "bytes,45,rep,name=repeated_bytes",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]*RepeatedGroup)(nil),
		Field:         46,
		Name:          "goproto.proto.testeditions.repeatedgroup",
		Tag:           "bytes,46,rep,name=repeatedgroup",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]*TestAllExtensions_NestedMessage)(nil),
		Field:         48,
		Name:          "goproto.proto.testeditions.repeated_nested_message",
		Tag:           "bytes,48,rep,name=repeated_nested_message",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]TestAllTypes_NestedEnum)(nil),
		Field:         51,
		Name:          "goproto.proto.testeditions.repeated_nested_enum",
		Tag:           "varint,51,rep,name=repeated_nested_enum,enum=goproto.proto.testeditions.TestAllTypes_NestedEnum",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         81,
		Name:          "goproto.proto.testeditions.default_int32",
		Tag:           "varint,81,opt,name=default_int32,def=81",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         82,
		Name:          "goproto.proto.testeditions.default_int64",
		Tag:           "varint,82,opt,name=default_int64,def=82",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         83,
		Name:          "goproto.proto.testeditions.default_uint32",
		Tag:           "varint,83,opt,name=default_uint32,def=83",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         84,
		Name:          "goproto.proto.testeditions.default_uint64",
		Tag:           "varint,84,opt,name=default_uint64,def=84",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         85,
		Name:          "goproto.proto.testeditions.default_sint32",
		Tag:           "zigzag32,85,opt,name=default_sint32,def=-85",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         86,
		Name:          "goproto.proto.testeditions.default_sint64",
		Tag:           "zigzag64,86,opt,name=default_sint64,def=86",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         87,
		Name:          "goproto.proto.testeditions.default_fixed32",
		Tag:           "fixed32,87,opt,name=default_fixed32,def=87",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         88,
		Name:          "goproto.proto.testeditions.default_fixed64",
		Tag:           "fixed64,88,opt,name=default_fixed64,def=88",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         89,
		Name:          "goproto.proto.testeditions.default_sfixed32",
		Tag:           "fixed32,89,opt,name=default_sfixed32,def=89",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         80,
		Name:          "goproto.proto.testeditions.default_sfixed64",
		Tag:           "fixed64,80,opt,name=default_sfixed64,def=-90",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         91,
		Name:          "goproto.proto.testeditions.default_float",
		Tag:           "fixed32,91,opt,name=default_float,def=91.5",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         92,
		Name:          "goproto.proto.testeditions.default_double",
		Tag:           "fixed64,92,opt,name=default_double,def=92000",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         93,
		Name:          "goproto.proto.testeditions.default_bool",
		Tag:           "varint,93,opt,name=default_bool,def=1",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*string)(nil),
		Field:         94,
		Name:          "goproto.proto.testeditions.default_string",
		Tag:           "bytes,94,opt,name=default_string,def=hello",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         95,
		Name:          "goproto.proto.testeditions.default_bytes",
		Tag:           "bytes,95,opt,name=default_bytes,def=world",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: (*TestRequired)(nil),
		Field:         1000,
		Name:          "goproto.proto.testeditions.single",
		Tag:           "bytes,1000,opt,name=single",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
	{
		ExtendedType:  (*TestAllExtensions)(nil),
		ExtensionType: ([]*TestRequired)(nil),
		Field:         1001,
		Name:          "goproto.proto.testeditions.multi",
		Tag:           "bytes,1001,rep,name=multi",
		Filename:      "internal/testprotos/testeditions/test_extension.proto",
	},
}

// Extension fields to TestAllExtensions.
var (
	// optional int32 optional_int32 = 1;
	E_OptionalInt32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[0]
	// optional int64 optional_int64 = 2;
	E_OptionalInt64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[1]
	// optional uint32 optional_uint32 = 3;
	E_OptionalUint32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[2]
	// optional uint64 optional_uint64 = 4;
	E_OptionalUint64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[3]
	// optional sint32 optional_sint32 = 5;
	E_OptionalSint32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[4]
	// optional sint64 optional_sint64 = 6;
	E_OptionalSint64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[5]
	// optional fixed32 optional_fixed32 = 7;
	E_OptionalFixed32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[6]
	// optional fixed64 optional_fixed64 = 8;
	E_OptionalFixed64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[7]
	// optional sfixed32 optional_sfixed32 = 9;
	E_OptionalSfixed32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[8]
	// optional sfixed64 optional_sfixed64 = 10;
	E_OptionalSfixed64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[9]
	// optional float optional_float = 11;
	E_OptionalFloat = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[10]
	// optional double optional_double = 12;
	E_OptionalDouble = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[11]
	// optional bool optional_bool = 13;
	E_OptionalBool = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[12]
	// optional string optional_string = 14;
	E_OptionalString = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[13]
	// optional bytes optional_bytes = 15;
	E_OptionalBytes = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[14]
	// optional goproto.proto.testeditions.OptionalGroup optionalgroup = 16;
	E_Optionalgroup = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[15]
	// optional goproto.proto.testeditions.TestAllExtensions.NestedMessage optional_nested_message = 18;
	E_OptionalNestedMessage = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[16]
	// optional goproto.proto.testeditions.TestAllTypes.NestedEnum optional_nested_enum = 21;
	E_OptionalNestedEnum = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[17]
	// repeated int32 repeated_int32 = 31;
	E_RepeatedInt32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[18]
	// repeated int64 repeated_int64 = 32;
	E_RepeatedInt64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[19]
	// repeated uint32 repeated_uint32 = 33;
	E_RepeatedUint32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[20]
	// repeated uint64 repeated_uint64 = 34;
	E_RepeatedUint64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[21]
	// repeated sint32 repeated_sint32 = 35;
	E_RepeatedSint32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[22]
	// repeated sint64 repeated_sint64 = 36;
	E_RepeatedSint64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[23]
	// repeated fixed32 repeated_fixed32 = 37;
	E_RepeatedFixed32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[24]
	// repeated fixed64 repeated_fixed64 = 38;
	E_RepeatedFixed64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[25]
	// repeated sfixed32 repeated_sfixed32 = 39;
	E_RepeatedSfixed32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[26]
	// repeated sfixed64 repeated_sfixed64 = 40;
	E_RepeatedSfixed64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[27]
	// repeated float repeated_float = 41;
	E_RepeatedFloat = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[28]
	// repeated double repeated_double = 42;
	E_RepeatedDouble = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[29]
	// repeated bool repeated_bool = 43;
	E_RepeatedBool = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[30]
	// repeated string repeated_string = 44;
	E_RepeatedString = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[31]
	// repeated bytes repeated_bytes = 45;
	E_RepeatedBytes = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[32]
	// repeated goproto.proto.testeditions.RepeatedGroup repeatedgroup = 46;
	E_Repeatedgroup = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[33]
	// repeated goproto.proto.testeditions.TestAllExtensions.NestedMessage repeated_nested_message = 48;
	E_RepeatedNestedMessage = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[34]
	// repeated goproto.proto.testeditions.TestAllTypes.NestedEnum repeated_nested_enum = 51;
	E_RepeatedNestedEnum = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[35]
	// optional int32 default_int32 = 81;
	E_DefaultInt32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[36]
	// optional int64 default_int64 = 82;
	E_DefaultInt64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[37]
	// optional uint32 default_uint32 = 83;
	E_DefaultUint32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[38]
	// optional uint64 default_uint64 = 84;
	E_DefaultUint64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[39]
	// optional sint32 default_sint32 = 85;
	E_DefaultSint32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[40]
	// optional sint64 default_sint64 = 86;
	E_DefaultSint64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[41]
	// optional fixed32 default_fixed32 = 87;
	E_DefaultFixed32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[42]
	// optional fixed64 default_fixed64 = 88;
	E_DefaultFixed64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[43]
	// optional sfixed32 default_sfixed32 = 89;
	E_DefaultSfixed32 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[44]
	// optional sfixed64 default_sfixed64 = 80;
	E_DefaultSfixed64 = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[45]
	// optional float default_float = 91;
	E_DefaultFloat = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[46]
	// optional double default_double = 92;
	E_DefaultDouble = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[47]
	// optional bool default_bool = 93;
	E_DefaultBool = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[48]
	// optional string default_string = 94;
	E_DefaultString = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[49]
	// optional bytes default_bytes = 95;
	E_DefaultBytes = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[50]
	// optional goproto.proto.testeditions.TestRequired single = 1000;
	E_Single = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[51]
	// repeated goproto.proto.testeditions.TestRequired multi = 1001;
	E_Multi = &file_internal_testprotos_testeditions_test_extension_proto_extTypes[52]
)

var File_internal_testprotos_testeditions_test_extension_proto protoreflect.FileDescriptor

var file_internal_testprotos_testeditions_test_extension_proto_rawDesc = []byte{
	0x0a, 0x35, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x1a, 0x2b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65,
	0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x8d, 0x01, 0x0a, 0x11, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x6e, 0x0a, 0x0d, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x4f, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x2a, 0x08, 0x08, 0x01, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02,
	0x22, 0xbf, 0x01, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6d,
	0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x74, 0x0a, 0x17,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe8, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x61, 0x12, 0x74, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe9, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x54, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x54,
	0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x56, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x56, 0x0a, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x56, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0e, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x56, 0x0a, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x58, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x58,
	0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x5a, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2d, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x3a, 0x5a, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x3a, 0x54, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x56, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x52,
	0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12,
	0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f,
	0x6f, 0x6c, 0x3a, 0x56, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x54, 0x0a, 0x0e, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x3a, 0x85, 0x01, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x3a, 0xa2, 0x01, 0x0a, 0x17, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x94, 0x01,
	0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x54, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x54, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x56, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x56, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x56, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x56, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x12,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x58, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x25, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x58, 0x0a, 0x10, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x26, 0x20,
	0x03, 0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x3a, 0x5a, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x3a, 0x5a, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x54, 0x0a, 0x0e,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2d,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x29, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x3a, 0x56, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x52, 0x0a, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x2d, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x08,
	0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x56,
	0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x54, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2d, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x3a, 0x85, 0x01, 0x0a,
	0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2d,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x3a, 0xa2, 0x01, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x94, 0x01, 0x0a, 0x14, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x3a, 0x56, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x51, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x02, 0x38, 0x31, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x56, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x52, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x02,
	0x38, 0x32, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x58, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x02, 0x38, 0x33, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x58, 0x0a, 0x0e, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x54, 0x20, 0x01, 0x28,
	0x04, 0x3a, 0x02, 0x38, 0x34, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x59, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x55, 0x20, 0x01, 0x28, 0x11, 0x3a, 0x03, 0x2d, 0x38, 0x35,
	0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a,
	0x58, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x56, 0x20, 0x01, 0x28, 0x12, 0x3a, 0x02, 0x38, 0x36, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x5a, 0x0a, 0x0f, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x57, 0x20, 0x01, 0x28,
	0x07, 0x3a, 0x02, 0x38, 0x37, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x5a, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x58, 0x20, 0x01, 0x28, 0x06, 0x3a, 0x02, 0x38,
	0x38, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x3a, 0x5c, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x59, 0x20, 0x01, 0x28, 0x0f, 0x3a, 0x02, 0x38, 0x39, 0x52, 0x0f,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a,
	0x5d, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x50, 0x20, 0x01, 0x28, 0x10, 0x3a, 0x03, 0x2d, 0x39, 0x30, 0x52, 0x0f, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x58,
	0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12,
	0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5b,
	0x20, 0x01, 0x28, 0x02, 0x3a, 0x04, 0x39, 0x31, 0x2e, 0x35, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x5b, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x01, 0x3a,
	0x05, 0x39, 0x32, 0x30, 0x30, 0x30, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x56, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65,
	0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x5b, 0x0a,
	0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5e,
	0x20, 0x01, 0x28, 0x09, 0x3a, 0x05, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x59, 0x0a, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x0c,
	0x3a, 0x05, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x3a, 0x70, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x12,
	0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe8,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x3a, 0x6e, 0x0a, 0x05, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xe9, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x05, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x42, 0x44, 0x5a, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74,
	0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x92, 0x03, 0x04, 0x18, 0x02, 0x20, 0x03, 0x62, 0x08, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_internal_testprotos_testeditions_test_extension_proto_rawDescOnce sync.Once
	file_internal_testprotos_testeditions_test_extension_proto_rawDescData = file_internal_testprotos_testeditions_test_extension_proto_rawDesc
)

func file_internal_testprotos_testeditions_test_extension_proto_rawDescGZIP() []byte {
	file_internal_testprotos_testeditions_test_extension_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_testeditions_test_extension_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_testeditions_test_extension_proto_rawDescData)
	})
	return file_internal_testprotos_testeditions_test_extension_proto_rawDescData
}

var file_internal_testprotos_testeditions_test_extension_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_internal_testprotos_testeditions_test_extension_proto_goTypes = []interface{}{
	(*TestAllExtensions)(nil),               // 0: goproto.proto.testeditions.TestAllExtensions
	(*OptionalGroup)(nil),                   // 1: goproto.proto.testeditions.OptionalGroup
	(*RepeatedGroup)(nil),                   // 2: goproto.proto.testeditions.RepeatedGroup
	(*TestAllExtensions_NestedMessage)(nil), // 3: goproto.proto.testeditions.TestAllExtensions.NestedMessage
	(TestAllTypes_NestedEnum)(0),            // 4: goproto.proto.testeditions.TestAllTypes.NestedEnum
	(*TestRequired)(nil),                    // 5: goproto.proto.testeditions.TestRequired
}
var file_internal_testprotos_testeditions_test_extension_proto_depIdxs = []int32{
	3,  // 0: goproto.proto.testeditions.OptionalGroup.optional_nested_message:type_name -> goproto.proto.testeditions.TestAllExtensions.NestedMessage
	3,  // 1: goproto.proto.testeditions.RepeatedGroup.optional_nested_message:type_name -> goproto.proto.testeditions.TestAllExtensions.NestedMessage
	0,  // 2: goproto.proto.testeditions.TestAllExtensions.NestedMessage.corecursive:type_name -> goproto.proto.testeditions.TestAllExtensions
	0,  // 3: goproto.proto.testeditions.optional_int32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 4: goproto.proto.testeditions.optional_int64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 5: goproto.proto.testeditions.optional_uint32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 6: goproto.proto.testeditions.optional_uint64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 7: goproto.proto.testeditions.optional_sint32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 8: goproto.proto.testeditions.optional_sint64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 9: goproto.proto.testeditions.optional_fixed32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 10: goproto.proto.testeditions.optional_fixed64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 11: goproto.proto.testeditions.optional_sfixed32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 12: goproto.proto.testeditions.optional_sfixed64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 13: goproto.proto.testeditions.optional_float:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 14: goproto.proto.testeditions.optional_double:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 15: goproto.proto.testeditions.optional_bool:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 16: goproto.proto.testeditions.optional_string:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 17: goproto.proto.testeditions.optional_bytes:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 18: goproto.proto.testeditions.optionalgroup:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 19: goproto.proto.testeditions.optional_nested_message:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 20: goproto.proto.testeditions.optional_nested_enum:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 21: goproto.proto.testeditions.repeated_int32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 22: goproto.proto.testeditions.repeated_int64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 23: goproto.proto.testeditions.repeated_uint32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 24: goproto.proto.testeditions.repeated_uint64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 25: goproto.proto.testeditions.repeated_sint32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 26: goproto.proto.testeditions.repeated_sint64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 27: goproto.proto.testeditions.repeated_fixed32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 28: goproto.proto.testeditions.repeated_fixed64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 29: goproto.proto.testeditions.repeated_sfixed32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 30: goproto.proto.testeditions.repeated_sfixed64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 31: goproto.proto.testeditions.repeated_float:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 32: goproto.proto.testeditions.repeated_double:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 33: goproto.proto.testeditions.repeated_bool:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 34: goproto.proto.testeditions.repeated_string:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 35: goproto.proto.testeditions.repeated_bytes:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 36: goproto.proto.testeditions.repeatedgroup:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 37: goproto.proto.testeditions.repeated_nested_message:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 38: goproto.proto.testeditions.repeated_nested_enum:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 39: goproto.proto.testeditions.default_int32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 40: goproto.proto.testeditions.default_int64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 41: goproto.proto.testeditions.default_uint32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 42: goproto.proto.testeditions.default_uint64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 43: goproto.proto.testeditions.default_sint32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 44: goproto.proto.testeditions.default_sint64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 45: goproto.proto.testeditions.default_fixed32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 46: goproto.proto.testeditions.default_fixed64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 47: goproto.proto.testeditions.default_sfixed32:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 48: goproto.proto.testeditions.default_sfixed64:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 49: goproto.proto.testeditions.default_float:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 50: goproto.proto.testeditions.default_double:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 51: goproto.proto.testeditions.default_bool:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 52: goproto.proto.testeditions.default_string:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 53: goproto.proto.testeditions.default_bytes:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 54: goproto.proto.testeditions.single:extendee -> goproto.proto.testeditions.TestAllExtensions
	0,  // 55: goproto.proto.testeditions.multi:extendee -> goproto.proto.testeditions.TestAllExtensions
	1,  // 56: goproto.proto.testeditions.optionalgroup:type_name -> goproto.proto.testeditions.OptionalGroup
	3,  // 57: goproto.proto.testeditions.optional_nested_message:type_name -> goproto.proto.testeditions.TestAllExtensions.NestedMessage
	4,  // 58: goproto.proto.testeditions.optional_nested_enum:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	2,  // 59: goproto.proto.testeditions.repeatedgroup:type_name -> goproto.proto.testeditions.RepeatedGroup
	3,  // 60: goproto.proto.testeditions.repeated_nested_message:type_name -> goproto.proto.testeditions.TestAllExtensions.NestedMessage
	4,  // 61: goproto.proto.testeditions.repeated_nested_enum:type_name -> goproto.proto.testeditions.TestAllTypes.NestedEnum
	5,  // 62: goproto.proto.testeditions.single:type_name -> goproto.proto.testeditions.TestRequired
	5,  // 63: goproto.proto.testeditions.multi:type_name -> goproto.proto.testeditions.TestRequired
	64, // [64:64] is the sub-list for method output_type
	64, // [64:64] is the sub-list for method input_type
	56, // [56:64] is the sub-list for extension type_name
	3,  // [3:56] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_internal_testprotos_testeditions_test_extension_proto_init() }
func file_internal_testprotos_testeditions_test_extension_proto_init() {
	if File_internal_testprotos_testeditions_test_extension_proto != nil {
		return
	}
	file_internal_testprotos_testeditions_test_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_testeditions_test_extension_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllExtensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_extension_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_extension_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_testeditions_test_extension_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllExtensions_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_testeditions_test_extension_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 53,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_testeditions_test_extension_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_testeditions_test_extension_proto_depIdxs,
		MessageInfos:      file_internal_testprotos_testeditions_test_extension_proto_msgTypes,
		ExtensionInfos:    file_internal_testprotos_testeditions_test_extension_proto_extTypes,
	}.Build()
	File_internal_testprotos_testeditions_test_extension_proto = out.File
	file_internal_testprotos_testeditions_test_extension_proto_rawDesc = nil
	file_internal_testprotos_testeditions_test_extension_proto_goTypes = nil
	file_internal_testprotos_testeditions_test_extension_proto_depIdxs = nil
}
