// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto3_20190205_c823c79e/test.proto

package proto3_20190205_c823c79e

import (
	fmt "fmt"
	math "math"

	proto "google.golang.org/protobuf/internal/protolegacy"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SiblingEnum int32

const (
	SiblingEnum_ALPHA   SiblingEnum = 0
	SiblingEnum_BRAVO   SiblingEnum = 10
	SiblingEnum_CHARLIE SiblingEnum = 200
)

var SiblingEnum_name = map[int32]string{
	0:   "ALPHA",
	10:  "BRAVO",
	200: "CHARLIE",
}

var SiblingEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   10,
	"CHARLIE": 200,
}

func (x SiblingEnum) String() string {
	return proto.EnumName(SiblingEnum_name, int32(x))
}

func (SiblingEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_95cd555ff3d1bc43, []int{0}
}

type Message_ChildEnum int32

const (
	Message_ALPHA   Message_ChildEnum = 0
	Message_BRAVO   Message_ChildEnum = 1
	Message_CHARLIE Message_ChildEnum = 2
)

var Message_ChildEnum_name = map[int32]string{
	0: "ALPHA",
	1: "BRAVO",
	2: "CHARLIE",
}

var Message_ChildEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   1,
	"CHARLIE": 2,
}

func (x Message_ChildEnum) String() string {
	return proto.EnumName(Message_ChildEnum_name, int32(x))
}

func (Message_ChildEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_95cd555ff3d1bc43, []int{1, 0}
}

type SiblingMessage struct {
	F1                   string   `protobuf:"bytes,1,opt,name=f1,proto3" json:"f1,omitempty"`
	F2                   []string `protobuf:"bytes,2,rep,name=f2,proto3" json:"f2,omitempty"`
	F3                   *Message `protobuf:"bytes,3,opt,name=f3,proto3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SiblingMessage) Reset()         { *m = SiblingMessage{} }
func (m *SiblingMessage) String() string { return proto.CompactTextString(m) }
func (*SiblingMessage) ProtoMessage()    {}
func (*SiblingMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_95cd555ff3d1bc43, []int{0}
}

func (m *SiblingMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SiblingMessage.Unmarshal(m, b)
}
func (m *SiblingMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SiblingMessage.Marshal(b, m, deterministic)
}
func (m *SiblingMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SiblingMessage.Merge(m, src)
}
func (m *SiblingMessage) XXX_Size() int {
	return xxx_messageInfo_SiblingMessage.Size(m)
}
func (m *SiblingMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_SiblingMessage.DiscardUnknown(m)
}

var xxx_messageInfo_SiblingMessage proto.InternalMessageInfo

func (m *SiblingMessage) GetF1() string {
	if m != nil {
		return m.F1
	}
	return ""
}

func (m *SiblingMessage) GetF2() []string {
	if m != nil {
		return m.F2
	}
	return nil
}

func (m *SiblingMessage) GetF3() *Message {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message struct {
	// Optional fields.
	OptionalBool           bool                  `protobuf:"varint,100,opt,name=optional_bool,json=optionalBool,proto3" json:"optional_bool,omitempty"`
	OptionalInt32          int32                 `protobuf:"varint,101,opt,name=optional_int32,json=optionalInt32,proto3" json:"optional_int32,omitempty"`
	OptionalSint32         int32                 `protobuf:"zigzag32,102,opt,name=optional_sint32,json=optionalSint32,proto3" json:"optional_sint32,omitempty"`
	OptionalUint32         uint32                `protobuf:"varint,103,opt,name=optional_uint32,json=optionalUint32,proto3" json:"optional_uint32,omitempty"`
	OptionalInt64          int64                 `protobuf:"varint,104,opt,name=optional_int64,json=optionalInt64,proto3" json:"optional_int64,omitempty"`
	OptionalSint64         int64                 `protobuf:"zigzag64,105,opt,name=optional_sint64,json=optionalSint64,proto3" json:"optional_sint64,omitempty"`
	OptionalUint64         uint64                `protobuf:"varint,106,opt,name=optional_uint64,json=optionalUint64,proto3" json:"optional_uint64,omitempty"`
	OptionalFixed32        uint32                `protobuf:"fixed32,107,opt,name=optional_fixed32,json=optionalFixed32,proto3" json:"optional_fixed32,omitempty"`
	OptionalSfixed32       int32                 `protobuf:"fixed32,108,opt,name=optional_sfixed32,json=optionalSfixed32,proto3" json:"optional_sfixed32,omitempty"`
	OptionalFloat          float32               `protobuf:"fixed32,109,opt,name=optional_float,json=optionalFloat,proto3" json:"optional_float,omitempty"`
	OptionalFixed64        uint64                `protobuf:"fixed64,110,opt,name=optional_fixed64,json=optionalFixed64,proto3" json:"optional_fixed64,omitempty"`
	OptionalSfixed64       int64                 `protobuf:"fixed64,111,opt,name=optional_sfixed64,json=optionalSfixed64,proto3" json:"optional_sfixed64,omitempty"`
	OptionalDouble         float64               `protobuf:"fixed64,112,opt,name=optional_double,json=optionalDouble,proto3" json:"optional_double,omitempty"`
	OptionalString         string                `protobuf:"bytes,113,opt,name=optional_string,json=optionalString,proto3" json:"optional_string,omitempty"`
	OptionalBytes          []byte                `protobuf:"bytes,114,opt,name=optional_bytes,json=optionalBytes,proto3" json:"optional_bytes,omitempty"`
	OptionalChildEnum      Message_ChildEnum     `protobuf:"varint,115,opt,name=optional_child_enum,json=optionalChildEnum,proto3,enum=google.golang.org.proto3_20190205.Message_ChildEnum" json:"optional_child_enum,omitempty"`
	OptionalChildMessage   *Message_ChildMessage `protobuf:"bytes,116,opt,name=optional_child_message,json=optionalChildMessage,proto3" json:"optional_child_message,omitempty"`
	OptionalSiblingEnum    SiblingEnum           `protobuf:"varint,117,opt,name=optional_sibling_enum,json=optionalSiblingEnum,proto3,enum=google.golang.org.proto3_20190205.SiblingEnum" json:"optional_sibling_enum,omitempty"`
	OptionalSiblingMessage *SiblingMessage       `protobuf:"bytes,118,opt,name=optional_sibling_message,json=optionalSiblingMessage,proto3" json:"optional_sibling_message,omitempty"`
	// Repeated fields.
	RepeatedBool           []bool                  `protobuf:"varint,200,rep,packed,name=repeated_bool,json=repeatedBool,proto3" json:"repeated_bool,omitempty"`
	RepeatedInt32          []int32                 `protobuf:"varint,201,rep,packed,name=repeated_int32,json=repeatedInt32,proto3" json:"repeated_int32,omitempty"`
	RepeatedSint32         []int32                 `protobuf:"zigzag32,202,rep,packed,name=repeated_sint32,json=repeatedSint32,proto3" json:"repeated_sint32,omitempty"`
	RepeatedUint32         []uint32                `protobuf:"varint,203,rep,packed,name=repeated_uint32,json=repeatedUint32,proto3" json:"repeated_uint32,omitempty"`
	RepeatedInt64          []int64                 `protobuf:"varint,204,rep,packed,name=repeated_int64,json=repeatedInt64,proto3" json:"repeated_int64,omitempty"`
	RepeatedSint64         []int64                 `protobuf:"zigzag64,205,rep,packed,name=repeated_sint64,json=repeatedSint64,proto3" json:"repeated_sint64,omitempty"`
	RepeatedUint64         []uint64                `protobuf:"varint,206,rep,packed,name=repeated_uint64,json=repeatedUint64,proto3" json:"repeated_uint64,omitempty"`
	RepeatedFixed32        []uint32                `protobuf:"fixed32,207,rep,packed,name=repeated_fixed32,json=repeatedFixed32,proto3" json:"repeated_fixed32,omitempty"`
	RepeatedSfixed32       []int32                 `protobuf:"fixed32,208,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32,proto3" json:"repeated_sfixed32,omitempty"`
	RepeatedFloat          []float32               `protobuf:"fixed32,209,rep,packed,name=repeated_float,json=repeatedFloat,proto3" json:"repeated_float,omitempty"`
	RepeatedFixed64        []uint64                `protobuf:"fixed64,210,rep,packed,name=repeated_fixed64,json=repeatedFixed64,proto3" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed64       []int64                 `protobuf:"fixed64,211,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64,proto3" json:"repeated_sfixed64,omitempty"`
	RepeatedDouble         []float64               `protobuf:"fixed64,212,rep,packed,name=repeated_double,json=repeatedDouble,proto3" json:"repeated_double,omitempty"`
	RepeatedString         []string                `protobuf:"bytes,213,rep,name=repeated_string,json=repeatedString,proto3" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                `protobuf:"bytes,214,rep,name=repeated_bytes,json=repeatedBytes,proto3" json:"repeated_bytes,omitempty"`
	RepeatedChildEnum      []Message_ChildEnum     `protobuf:"varint,215,rep,packed,name=repeated_child_enum,json=repeatedChildEnum,proto3,enum=google.golang.org.proto3_20190205.Message_ChildEnum" json:"repeated_child_enum,omitempty"`
	RepeatedChildMessage   []*Message_ChildMessage `protobuf:"bytes,216,rep,name=repeated_child_message,json=repeatedChildMessage,proto3" json:"repeated_child_message,omitempty"`
	RepeatedSiblingEnum    []SiblingEnum           `protobuf:"varint,217,rep,packed,name=repeated_sibling_enum,json=repeatedSiblingEnum,proto3,enum=google.golang.org.proto3_20190205.SiblingEnum" json:"repeated_sibling_enum,omitempty"`
	RepeatedSiblingMessage []*SiblingMessage       `protobuf:"bytes,218,rep,name=repeated_sibling_message,json=repeatedSiblingMessage,proto3" json:"repeated_sibling_message,omitempty"`
	// Map fields.
	MapBoolBool           map[bool]bool                  `protobuf:"bytes,300,rep,name=map_bool_bool,json=mapBoolBool,proto3" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolInt32          map[bool]int32                 `protobuf:"bytes,301,rep,name=map_bool_int32,json=mapBoolInt32,proto3" json:"map_bool_int32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolSint32         map[bool]int32                 `protobuf:"bytes,302,rep,name=map_bool_sint32,json=mapBoolSint32,proto3" json:"map_bool_sint32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"zigzag32,2,opt,name=value,proto3"`
	MapBoolUint32         map[bool]uint32                `protobuf:"bytes,303,rep,name=map_bool_uint32,json=mapBoolUint32,proto3" json:"map_bool_uint32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolInt64          map[bool]int64                 `protobuf:"bytes,304,rep,name=map_bool_int64,json=mapBoolInt64,proto3" json:"map_bool_int64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolSint64         map[bool]int64                 `protobuf:"bytes,305,rep,name=map_bool_sint64,json=mapBoolSint64,proto3" json:"map_bool_sint64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"zigzag64,2,opt,name=value,proto3"`
	MapBoolUint64         map[bool]uint64                `protobuf:"bytes,306,rep,name=map_bool_uint64,json=mapBoolUint64,proto3" json:"map_bool_uint64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolFixed32        map[bool]uint32                `protobuf:"bytes,307,rep,name=map_bool_fixed32,json=mapBoolFixed32,proto3" json:"map_bool_fixed32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapBoolSfixed32       map[bool]int32                 `protobuf:"bytes,308,rep,name=map_bool_sfixed32,json=mapBoolSfixed32,proto3" json:"map_bool_sfixed32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapBoolFloat          map[bool]float32               `protobuf:"bytes,309,rep,name=map_bool_float,json=mapBoolFloat,proto3" json:"map_bool_float,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapBoolFixed64        map[bool]uint64                `protobuf:"bytes,310,rep,name=map_bool_fixed64,json=mapBoolFixed64,proto3" json:"map_bool_fixed64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolSfixed64       map[bool]int64                 `protobuf:"bytes,311,rep,name=map_bool_sfixed64,json=mapBoolSfixed64,proto3" json:"map_bool_sfixed64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolDouble         map[bool]float64               `protobuf:"bytes,312,rep,name=map_bool_double,json=mapBoolDouble,proto3" json:"map_bool_double,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolString         map[bool]string                `protobuf:"bytes,313,rep,name=map_bool_string,json=mapBoolString,proto3" json:"map_bool_string,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolBytes          map[bool][]byte                `protobuf:"bytes,314,rep,name=map_bool_bytes,json=mapBoolBytes,proto3" json:"map_bool_bytes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolChildEnum      map[bool]Message_ChildEnum     `protobuf:"bytes,315,rep,name=map_bool_child_enum,json=mapBoolChildEnum,proto3" json:"map_bool_child_enum,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=google.golang.org.proto3_20190205.Message_ChildEnum"`
	MapBoolChildMessage   map[bool]*Message_ChildMessage `protobuf:"bytes,316,rep,name=map_bool_child_message,json=mapBoolChildMessage,proto3" json:"map_bool_child_message,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolSiblingEnum    map[bool]SiblingEnum           `protobuf:"bytes,317,rep,name=map_bool_sibling_enum,json=mapBoolSiblingEnum,proto3" json:"map_bool_sibling_enum,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=google.golang.org.proto3_20190205.SiblingEnum"`
	MapBoolSiblingMessage map[bool]*SiblingMessage       `protobuf:"bytes,318,rep,name=map_bool_sibling_message,json=mapBoolSiblingMessage,proto3" json:"map_bool_sibling_message,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapInt32Bool          map[int32]bool                 `protobuf:"bytes,319,rep,name=map_int32_bool,json=mapInt32Bool,proto3" json:"map_int32_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapSint32Bool         map[int32]bool                 `protobuf:"bytes,320,rep,name=map_sint32_bool,json=mapSint32Bool,proto3" json:"map_sint32_bool,omitempty" protobuf_key:"zigzag32,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapUint32Bool         map[uint32]bool                `protobuf:"bytes,321,rep,name=map_uint32_bool,json=mapUint32Bool,proto3" json:"map_uint32_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapInt64Bool          map[int64]bool                 `protobuf:"bytes,322,rep,name=map_int64_bool,json=mapInt64Bool,proto3" json:"map_int64_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapSint64Bool         map[int64]bool                 `protobuf:"bytes,323,rep,name=map_sint64_bool,json=mapSint64Bool,proto3" json:"map_sint64_bool,omitempty" protobuf_key:"zigzag64,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapUint64Bool         map[uint64]bool                `protobuf:"bytes,324,rep,name=map_uint64_bool,json=mapUint64Bool,proto3" json:"map_uint64_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapFixed32Bool        map[uint32]bool                `protobuf:"bytes,325,rep,name=map_fixed32_bool,json=mapFixed32Bool,proto3" json:"map_fixed32_bool,omitempty" protobuf_key:"fixed32,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapStringBool         map[string]bool                `protobuf:"bytes,326,rep,name=map_string_bool,json=mapStringBool,proto3" json:"map_string_bool,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// Oneof fields.
	//
	// Types that are valid to be assigned to OneofUnion:
	//	*Message_OneofBool
	//	*Message_OneofInt32
	//	*Message_OneofSint32
	//	*Message_OneofUint32
	//	*Message_OneofInt64
	//	*Message_OneofSint64
	//	*Message_OneofUint64
	//	*Message_OneofFixed32
	//	*Message_OneofSfixed32
	//	*Message_OneofFloat
	//	*Message_OneofFixed64
	//	*Message_OneofSfixed64
	//	*Message_OneofDouble
	//	*Message_OneofString
	//	*Message_OneofBytes
	//	*Message_OneofChildEnum
	//	*Message_OneofChildMessage
	//	*Message_OneofSiblingEnum
	//	*Message_OneofSiblingMessage
	//	*Message_OneofString1
	//	*Message_OneofString2
	//	*Message_OneofString3
	OneofUnion           isMessage_OneofUnion `protobuf_oneof:"oneof_union"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *Message) Reset()         { *m = Message{} }
func (m *Message) String() string { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()    {}
func (*Message) Descriptor() ([]byte, []int) {
	return fileDescriptor_95cd555ff3d1bc43, []int{1}
}

func (m *Message) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message.Unmarshal(m, b)
}
func (m *Message) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message.Marshal(b, m, deterministic)
}
func (m *Message) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message.Merge(m, src)
}
func (m *Message) XXX_Size() int {
	return xxx_messageInfo_Message.Size(m)
}
func (m *Message) XXX_DiscardUnknown() {
	xxx_messageInfo_Message.DiscardUnknown(m)
}

var xxx_messageInfo_Message proto.InternalMessageInfo

func (m *Message) GetOptionalBool() bool {
	if m != nil {
		return m.OptionalBool
	}
	return false
}

func (m *Message) GetOptionalInt32() int32 {
	if m != nil {
		return m.OptionalInt32
	}
	return 0
}

func (m *Message) GetOptionalSint32() int32 {
	if m != nil {
		return m.OptionalSint32
	}
	return 0
}

func (m *Message) GetOptionalUint32() uint32 {
	if m != nil {
		return m.OptionalUint32
	}
	return 0
}

func (m *Message) GetOptionalInt64() int64 {
	if m != nil {
		return m.OptionalInt64
	}
	return 0
}

func (m *Message) GetOptionalSint64() int64 {
	if m != nil {
		return m.OptionalSint64
	}
	return 0
}

func (m *Message) GetOptionalUint64() uint64 {
	if m != nil {
		return m.OptionalUint64
	}
	return 0
}

func (m *Message) GetOptionalFixed32() uint32 {
	if m != nil {
		return m.OptionalFixed32
	}
	return 0
}

func (m *Message) GetOptionalSfixed32() int32 {
	if m != nil {
		return m.OptionalSfixed32
	}
	return 0
}

func (m *Message) GetOptionalFloat() float32 {
	if m != nil {
		return m.OptionalFloat
	}
	return 0
}

func (m *Message) GetOptionalFixed64() uint64 {
	if m != nil {
		return m.OptionalFixed64
	}
	return 0
}

func (m *Message) GetOptionalSfixed64() int64 {
	if m != nil {
		return m.OptionalSfixed64
	}
	return 0
}

func (m *Message) GetOptionalDouble() float64 {
	if m != nil {
		return m.OptionalDouble
	}
	return 0
}

func (m *Message) GetOptionalString() string {
	if m != nil {
		return m.OptionalString
	}
	return ""
}

func (m *Message) GetOptionalBytes() []byte {
	if m != nil {
		return m.OptionalBytes
	}
	return nil
}

func (m *Message) GetOptionalChildEnum() Message_ChildEnum {
	if m != nil {
		return m.OptionalChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOptionalChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.OptionalChildMessage
	}
	return nil
}

func (m *Message) GetOptionalSiblingEnum() SiblingEnum {
	if m != nil {
		return m.OptionalSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOptionalSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.OptionalSiblingMessage
	}
	return nil
}

func (m *Message) GetRepeatedBool() []bool {
	if m != nil {
		return m.RepeatedBool
	}
	return nil
}

func (m *Message) GetRepeatedInt32() []int32 {
	if m != nil {
		return m.RepeatedInt32
	}
	return nil
}

func (m *Message) GetRepeatedSint32() []int32 {
	if m != nil {
		return m.RepeatedSint32
	}
	return nil
}

func (m *Message) GetRepeatedUint32() []uint32 {
	if m != nil {
		return m.RepeatedUint32
	}
	return nil
}

func (m *Message) GetRepeatedInt64() []int64 {
	if m != nil {
		return m.RepeatedInt64
	}
	return nil
}

func (m *Message) GetRepeatedSint64() []int64 {
	if m != nil {
		return m.RepeatedSint64
	}
	return nil
}

func (m *Message) GetRepeatedUint64() []uint64 {
	if m != nil {
		return m.RepeatedUint64
	}
	return nil
}

func (m *Message) GetRepeatedFixed32() []uint32 {
	if m != nil {
		return m.RepeatedFixed32
	}
	return nil
}

func (m *Message) GetRepeatedSfixed32() []int32 {
	if m != nil {
		return m.RepeatedSfixed32
	}
	return nil
}

func (m *Message) GetRepeatedFloat() []float32 {
	if m != nil {
		return m.RepeatedFloat
	}
	return nil
}

func (m *Message) GetRepeatedFixed64() []uint64 {
	if m != nil {
		return m.RepeatedFixed64
	}
	return nil
}

func (m *Message) GetRepeatedSfixed64() []int64 {
	if m != nil {
		return m.RepeatedSfixed64
	}
	return nil
}

func (m *Message) GetRepeatedDouble() []float64 {
	if m != nil {
		return m.RepeatedDouble
	}
	return nil
}

func (m *Message) GetRepeatedString() []string {
	if m != nil {
		return m.RepeatedString
	}
	return nil
}

func (m *Message) GetRepeatedBytes() [][]byte {
	if m != nil {
		return m.RepeatedBytes
	}
	return nil
}

func (m *Message) GetRepeatedChildEnum() []Message_ChildEnum {
	if m != nil {
		return m.RepeatedChildEnum
	}
	return nil
}

func (m *Message) GetRepeatedChildMessage() []*Message_ChildMessage {
	if m != nil {
		return m.RepeatedChildMessage
	}
	return nil
}

func (m *Message) GetRepeatedSiblingEnum() []SiblingEnum {
	if m != nil {
		return m.RepeatedSiblingEnum
	}
	return nil
}

func (m *Message) GetRepeatedSiblingMessage() []*SiblingMessage {
	if m != nil {
		return m.RepeatedSiblingMessage
	}
	return nil
}

func (m *Message) GetMapBoolBool() map[bool]bool {
	if m != nil {
		return m.MapBoolBool
	}
	return nil
}

func (m *Message) GetMapBoolInt32() map[bool]int32 {
	if m != nil {
		return m.MapBoolInt32
	}
	return nil
}

func (m *Message) GetMapBoolSint32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSint32
	}
	return nil
}

func (m *Message) GetMapBoolUint32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolUint32
	}
	return nil
}

func (m *Message) GetMapBoolInt64() map[bool]int64 {
	if m != nil {
		return m.MapBoolInt64
	}
	return nil
}

func (m *Message) GetMapBoolSint64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSint64
	}
	return nil
}

func (m *Message) GetMapBoolUint64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolUint64
	}
	return nil
}

func (m *Message) GetMapBoolFixed32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolFixed32
	}
	return nil
}

func (m *Message) GetMapBoolSfixed32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSfixed32
	}
	return nil
}

func (m *Message) GetMapBoolFloat() map[bool]float32 {
	if m != nil {
		return m.MapBoolFloat
	}
	return nil
}

func (m *Message) GetMapBoolFixed64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolFixed64
	}
	return nil
}

func (m *Message) GetMapBoolSfixed64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSfixed64
	}
	return nil
}

func (m *Message) GetMapBoolDouble() map[bool]float64 {
	if m != nil {
		return m.MapBoolDouble
	}
	return nil
}

func (m *Message) GetMapBoolString() map[bool]string {
	if m != nil {
		return m.MapBoolString
	}
	return nil
}

func (m *Message) GetMapBoolBytes() map[bool][]byte {
	if m != nil {
		return m.MapBoolBytes
	}
	return nil
}

func (m *Message) GetMapBoolChildEnum() map[bool]Message_ChildEnum {
	if m != nil {
		return m.MapBoolChildEnum
	}
	return nil
}

func (m *Message) GetMapBoolChildMessage() map[bool]*Message_ChildMessage {
	if m != nil {
		return m.MapBoolChildMessage
	}
	return nil
}

func (m *Message) GetMapBoolSiblingEnum() map[bool]SiblingEnum {
	if m != nil {
		return m.MapBoolSiblingEnum
	}
	return nil
}

func (m *Message) GetMapBoolSiblingMessage() map[bool]*SiblingMessage {
	if m != nil {
		return m.MapBoolSiblingMessage
	}
	return nil
}

func (m *Message) GetMapInt32Bool() map[int32]bool {
	if m != nil {
		return m.MapInt32Bool
	}
	return nil
}

func (m *Message) GetMapSint32Bool() map[int32]bool {
	if m != nil {
		return m.MapSint32Bool
	}
	return nil
}

func (m *Message) GetMapUint32Bool() map[uint32]bool {
	if m != nil {
		return m.MapUint32Bool
	}
	return nil
}

func (m *Message) GetMapInt64Bool() map[int64]bool {
	if m != nil {
		return m.MapInt64Bool
	}
	return nil
}

func (m *Message) GetMapSint64Bool() map[int64]bool {
	if m != nil {
		return m.MapSint64Bool
	}
	return nil
}

func (m *Message) GetMapUint64Bool() map[uint64]bool {
	if m != nil {
		return m.MapUint64Bool
	}
	return nil
}

func (m *Message) GetMapFixed32Bool() map[uint32]bool {
	if m != nil {
		return m.MapFixed32Bool
	}
	return nil
}

func (m *Message) GetMapStringBool() map[string]bool {
	if m != nil {
		return m.MapStringBool
	}
	return nil
}

type isMessage_OneofUnion interface {
	isMessage_OneofUnion()
}

type Message_OneofBool struct {
	OneofBool bool `protobuf:"varint,400,opt,name=oneof_bool,json=oneofBool,proto3,oneof"`
}

type Message_OneofInt32 struct {
	OneofInt32 int32 `protobuf:"varint,401,opt,name=oneof_int32,json=oneofInt32,proto3,oneof"`
}

type Message_OneofSint32 struct {
	OneofSint32 int32 `protobuf:"zigzag32,402,opt,name=oneof_sint32,json=oneofSint32,proto3,oneof"`
}

type Message_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,403,opt,name=oneof_uint32,json=oneofUint32,proto3,oneof"`
}

type Message_OneofInt64 struct {
	OneofInt64 int64 `protobuf:"varint,404,opt,name=oneof_int64,json=oneofInt64,proto3,oneof"`
}

type Message_OneofSint64 struct {
	OneofSint64 int64 `protobuf:"zigzag64,405,opt,name=oneof_sint64,json=oneofSint64,proto3,oneof"`
}

type Message_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,406,opt,name=oneof_uint64,json=oneofUint64,proto3,oneof"`
}

type Message_OneofFixed32 struct {
	OneofFixed32 uint32 `protobuf:"fixed32,407,opt,name=oneof_fixed32,json=oneofFixed32,proto3,oneof"`
}

type Message_OneofSfixed32 struct {
	OneofSfixed32 int32 `protobuf:"fixed32,408,opt,name=oneof_sfixed32,json=oneofSfixed32,proto3,oneof"`
}

type Message_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,409,opt,name=oneof_float,json=oneofFloat,proto3,oneof"`
}

type Message_OneofFixed64 struct {
	OneofFixed64 uint64 `protobuf:"fixed64,410,opt,name=oneof_fixed64,json=oneofFixed64,proto3,oneof"`
}

type Message_OneofSfixed64 struct {
	OneofSfixed64 int64 `protobuf:"fixed64,411,opt,name=oneof_sfixed64,json=oneofSfixed64,proto3,oneof"`
}

type Message_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,412,opt,name=oneof_double,json=oneofDouble,proto3,oneof"`
}

type Message_OneofString struct {
	OneofString string `protobuf:"bytes,413,opt,name=oneof_string,json=oneofString,proto3,oneof"`
}

type Message_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,414,opt,name=oneof_bytes,json=oneofBytes,proto3,oneof"`
}

type Message_OneofChildEnum struct {
	OneofChildEnum Message_ChildEnum `protobuf:"varint,415,opt,name=oneof_child_enum,json=oneofChildEnum,proto3,enum=google.golang.org.proto3_20190205.Message_ChildEnum,oneof"`
}

type Message_OneofChildMessage struct {
	OneofChildMessage *Message_ChildMessage `protobuf:"bytes,416,opt,name=oneof_child_message,json=oneofChildMessage,proto3,oneof"`
}

type Message_OneofSiblingEnum struct {
	OneofSiblingEnum SiblingEnum `protobuf:"varint,417,opt,name=oneof_sibling_enum,json=oneofSiblingEnum,proto3,enum=google.golang.org.proto3_20190205.SiblingEnum,oneof"`
}

type Message_OneofSiblingMessage struct {
	OneofSiblingMessage *SiblingMessage `protobuf:"bytes,418,opt,name=oneof_sibling_message,json=oneofSiblingMessage,proto3,oneof"`
}

type Message_OneofString1 struct {
	OneofString1 string `protobuf:"bytes,419,opt,name=oneof_string1,json=oneofString1,proto3,oneof"`
}

type Message_OneofString2 struct {
	OneofString2 string `protobuf:"bytes,420,opt,name=oneof_string2,json=oneofString2,proto3,oneof"`
}

type Message_OneofString3 struct {
	OneofString3 string `protobuf:"bytes,421,opt,name=oneof_string3,json=oneofString3,proto3,oneof"`
}

func (*Message_OneofBool) isMessage_OneofUnion() {}

func (*Message_OneofInt32) isMessage_OneofUnion() {}

func (*Message_OneofSint32) isMessage_OneofUnion() {}

func (*Message_OneofUint32) isMessage_OneofUnion() {}

func (*Message_OneofInt64) isMessage_OneofUnion() {}

func (*Message_OneofSint64) isMessage_OneofUnion() {}

func (*Message_OneofUint64) isMessage_OneofUnion() {}

func (*Message_OneofFixed32) isMessage_OneofUnion() {}

func (*Message_OneofSfixed32) isMessage_OneofUnion() {}

func (*Message_OneofFloat) isMessage_OneofUnion() {}

func (*Message_OneofFixed64) isMessage_OneofUnion() {}

func (*Message_OneofSfixed64) isMessage_OneofUnion() {}

func (*Message_OneofDouble) isMessage_OneofUnion() {}

func (*Message_OneofString) isMessage_OneofUnion() {}

func (*Message_OneofBytes) isMessage_OneofUnion() {}

func (*Message_OneofChildEnum) isMessage_OneofUnion() {}

func (*Message_OneofChildMessage) isMessage_OneofUnion() {}

func (*Message_OneofSiblingEnum) isMessage_OneofUnion() {}

func (*Message_OneofSiblingMessage) isMessage_OneofUnion() {}

func (*Message_OneofString1) isMessage_OneofUnion() {}

func (*Message_OneofString2) isMessage_OneofUnion() {}

func (*Message_OneofString3) isMessage_OneofUnion() {}

func (m *Message) GetOneofUnion() isMessage_OneofUnion {
	if m != nil {
		return m.OneofUnion
	}
	return nil
}

func (m *Message) GetOneofBool() bool {
	if x, ok := m.GetOneofUnion().(*Message_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (m *Message) GetOneofInt32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt32); ok {
		return x.OneofInt32
	}
	return 0
}

func (m *Message) GetOneofSint32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint32); ok {
		return x.OneofSint32
	}
	return 0
}

func (m *Message) GetOneofUint32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (m *Message) GetOneofInt64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt64); ok {
		return x.OneofInt64
	}
	return 0
}

func (m *Message) GetOneofSint64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint64); ok {
		return x.OneofSint64
	}
	return 0
}

func (m *Message) GetOneofUint64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (m *Message) GetOneofFixed32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed32); ok {
		return x.OneofFixed32
	}
	return 0
}

func (m *Message) GetOneofSfixed32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed32); ok {
		return x.OneofSfixed32
	}
	return 0
}

func (m *Message) GetOneofFloat() float32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (m *Message) GetOneofFixed64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed64); ok {
		return x.OneofFixed64
	}
	return 0
}

func (m *Message) GetOneofSfixed64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed64); ok {
		return x.OneofSfixed64
	}
	return 0
}

func (m *Message) GetOneofDouble() float64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (m *Message) GetOneofString() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (m *Message) GetOneofBytes() []byte {
	if x, ok := m.GetOneofUnion().(*Message_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (m *Message) GetOneofChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildEnum); ok {
		return x.OneofChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOneofChildMessage() *Message_ChildMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildMessage); ok {
		return x.OneofChildMessage
	}
	return nil
}

func (m *Message) GetOneofSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingEnum); ok {
		return x.OneofSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOneofSiblingMessage() *SiblingMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingMessage); ok {
		return x.OneofSiblingMessage
	}
	return nil
}

func (m *Message) GetOneofString1() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString1); ok {
		return x.OneofString1
	}
	return ""
}

func (m *Message) GetOneofString2() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString2); ok {
		return x.OneofString2
	}
	return ""
}

func (m *Message) GetOneofString3() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString3); ok {
		return x.OneofString3
	}
	return ""
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*Message) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*Message_OneofBool)(nil),
		(*Message_OneofInt32)(nil),
		(*Message_OneofSint32)(nil),
		(*Message_OneofUint32)(nil),
		(*Message_OneofInt64)(nil),
		(*Message_OneofSint64)(nil),
		(*Message_OneofUint64)(nil),
		(*Message_OneofFixed32)(nil),
		(*Message_OneofSfixed32)(nil),
		(*Message_OneofFloat)(nil),
		(*Message_OneofFixed64)(nil),
		(*Message_OneofSfixed64)(nil),
		(*Message_OneofDouble)(nil),
		(*Message_OneofString)(nil),
		(*Message_OneofBytes)(nil),
		(*Message_OneofChildEnum)(nil),
		(*Message_OneofChildMessage)(nil),
		(*Message_OneofSiblingEnum)(nil),
		(*Message_OneofSiblingMessage)(nil),
		(*Message_OneofString1)(nil),
		(*Message_OneofString2)(nil),
		(*Message_OneofString3)(nil),
	}
}

type Message_ChildMessage struct {
	F1                   string   `protobuf:"bytes,1,opt,name=f1,proto3" json:"f1,omitempty"`
	F2                   []string `protobuf:"bytes,2,rep,name=f2,proto3" json:"f2,omitempty"`
	F3                   *Message `protobuf:"bytes,3,opt,name=f3,proto3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_ChildMessage) Reset()         { *m = Message_ChildMessage{} }
func (m *Message_ChildMessage) String() string { return proto.CompactTextString(m) }
func (*Message_ChildMessage) ProtoMessage()    {}
func (*Message_ChildMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_95cd555ff3d1bc43, []int{1, 0}
}

func (m *Message_ChildMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_ChildMessage.Unmarshal(m, b)
}
func (m *Message_ChildMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_ChildMessage.Marshal(b, m, deterministic)
}
func (m *Message_ChildMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_ChildMessage.Merge(m, src)
}
func (m *Message_ChildMessage) XXX_Size() int {
	return xxx_messageInfo_Message_ChildMessage.Size(m)
}
func (m *Message_ChildMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_ChildMessage.DiscardUnknown(m)
}

var xxx_messageInfo_Message_ChildMessage proto.InternalMessageInfo

func (m *Message_ChildMessage) GetF1() string {
	if m != nil {
		return m.F1
	}
	return ""
}

func (m *Message_ChildMessage) GetF2() []string {
	if m != nil {
		return m.F2
	}
	return nil
}

func (m *Message_ChildMessage) GetF3() *Message {
	if m != nil {
		return m.F3
	}
	return nil
}

func init() {
	proto.RegisterEnum("google.golang.org.proto3_20190205.SiblingEnum", SiblingEnum_name, SiblingEnum_value)
	proto.RegisterEnum("google.golang.org.proto3_20190205.Message_ChildEnum", Message_ChildEnum_name, Message_ChildEnum_value)
	proto.RegisterType((*SiblingMessage)(nil), "google.golang.org.proto3_20190205.SiblingMessage")
	proto.RegisterType((*Message)(nil), "google.golang.org.proto3_20190205.Message")
	proto.RegisterMapType((map[bool]bool)(nil), "google.golang.org.proto3_20190205.Message.MapBoolBoolEntry")
	proto.RegisterMapType((map[bool][]byte)(nil), "google.golang.org.proto3_20190205.Message.MapBoolBytesEntry")
	proto.RegisterMapType((map[bool]Message_ChildEnum)(nil), "google.golang.org.proto3_20190205.Message.MapBoolChildEnumEntry")
	proto.RegisterMapType((map[bool]*Message_ChildMessage)(nil), "google.golang.org.proto3_20190205.Message.MapBoolChildMessageEntry")
	proto.RegisterMapType((map[bool]float64)(nil), "google.golang.org.proto3_20190205.Message.MapBoolDoubleEntry")
	proto.RegisterMapType((map[bool]uint32)(nil), "google.golang.org.proto3_20190205.Message.MapBoolFixed32Entry")
	proto.RegisterMapType((map[bool]uint64)(nil), "google.golang.org.proto3_20190205.Message.MapBoolFixed64Entry")
	proto.RegisterMapType((map[bool]float32)(nil), "google.golang.org.proto3_20190205.Message.MapBoolFloatEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto3_20190205.Message.MapBoolInt32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto3_20190205.Message.MapBoolInt64Entry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto3_20190205.Message.MapBoolSfixed32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto3_20190205.Message.MapBoolSfixed64Entry")
	proto.RegisterMapType((map[bool]SiblingEnum)(nil), "google.golang.org.proto3_20190205.Message.MapBoolSiblingEnumEntry")
	proto.RegisterMapType((map[bool]*SiblingMessage)(nil), "google.golang.org.proto3_20190205.Message.MapBoolSiblingMessageEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto3_20190205.Message.MapBoolSint32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto3_20190205.Message.MapBoolSint64Entry")
	proto.RegisterMapType((map[bool]string)(nil), "google.golang.org.proto3_20190205.Message.MapBoolStringEntry")
	proto.RegisterMapType((map[bool]uint32)(nil), "google.golang.org.proto3_20190205.Message.MapBoolUint32Entry")
	proto.RegisterMapType((map[bool]uint64)(nil), "google.golang.org.proto3_20190205.Message.MapBoolUint64Entry")
	proto.RegisterMapType((map[uint32]bool)(nil), "google.golang.org.proto3_20190205.Message.MapFixed32BoolEntry")
	proto.RegisterMapType((map[int32]bool)(nil), "google.golang.org.proto3_20190205.Message.MapInt32BoolEntry")
	proto.RegisterMapType((map[int64]bool)(nil), "google.golang.org.proto3_20190205.Message.MapInt64BoolEntry")
	proto.RegisterMapType((map[int32]bool)(nil), "google.golang.org.proto3_20190205.Message.MapSint32BoolEntry")
	proto.RegisterMapType((map[int64]bool)(nil), "google.golang.org.proto3_20190205.Message.MapSint64BoolEntry")
	proto.RegisterMapType((map[string]bool)(nil), "google.golang.org.proto3_20190205.Message.MapStringBoolEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "google.golang.org.proto3_20190205.Message.MapUint32BoolEntry")
	proto.RegisterMapType((map[uint64]bool)(nil), "google.golang.org.proto3_20190205.Message.MapUint64BoolEntry")
	proto.RegisterType((*Message_ChildMessage)(nil), "google.golang.org.proto3_20190205.Message.ChildMessage")
}

func init() {
	proto.RegisterFile("proto3_20190205_c823c79e/test.proto", fileDescriptor_95cd555ff3d1bc43)
}

var fileDescriptor_95cd555ff3d1bc43 = []byte{
	// 1947 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x9a, 0x59, 0x73, 0xdb, 0xc8,
	0x11, 0xc7, 0x09, 0x52, 0x87, 0x35, 0xe2, 0x09, 0x5a, 0xca, 0x94, 0x9e, 0x10, 0xd9, 0x71, 0x10,
	0x27, 0x45, 0x59, 0x24, 0x02, 0x1f, 0x49, 0x6c, 0x4b, 0xb6, 0x1c, 0x3a, 0x65, 0x27, 0x2e, 0xb8,
	0x94, 0x87, 0xbc, 0x28, 0x94, 0x04, 0xd2, 0xb4, 0x41, 0x42, 0x11, 0x49, 0x57, 0x54, 0x79, 0xf0,
	0x57, 0xc8, 0x7d, 0xef, 0xbd, 0xfb, 0xb6, 0xf7, 0x7d, 0xdf, 0xde, 0xf2, 0xde, 0xf7, 0xf9, 0x69,
	0xb6, 0x06, 0x8d, 0xb9, 0x00, 0xd0, 0x24, 0xc1, 0xda, 0x07, 0x57, 0x49, 0xcd, 0xff, 0xf4, 0x8f,
	0xdd, 0xe8, 0xe9, 0x9e, 0x81, 0x85, 0x0e, 0xec, 0xec, 0xba, 0x5d, 0xb7, 0xb2, 0x51, 0x3e, 0xb2,
	0x7c, 0xfc, 0x48, 0xf9, 0xc8, 0x4f, 0x37, 0xb6, 0x8e, 0x95, 0x2b, 0x5b, 0x47, 0x8f, 0xdb, 0x4b,
	0x5d, 0xbb, 0xd3, 0x2d, 0x79, 0x9f, 0xaa, 0xdf, 0x6f, 0xb8, 0x6e, 0xc3, 0xb1, 0x4b, 0x0d, 0xd7,
	0xa9, 0xb5, 0x1b, 0x25, 0x77, 0xb7, 0x51, 0x0a, 0x2c, 0x5b, 0x74, 0x50, 0xf6, 0x72, 0x73, 0xd3,
	0x69, 0xb6, 0x1b, 0x17, 0xed, 0x4e, 0xa7, 0xd6, 0xb0, 0xd5, 0x2c, 0x4a, 0xd6, 0x97, 0xb1, 0xa2,
	0x29, 0xfa, 0x8c, 0x95, 0xac, 0x2f, 0x7b, 0xbf, 0x97, 0x71, 0x52, 0x4b, 0x79, 0xbf, 0x97, 0xd5,
	0x13, 0x28, 0x59, 0xaf, 0xe0, 0x94, 0xa6, 0xe8, 0xb3, 0xe5, 0xc3, 0xa5, 0x81, 0x84, 0x92, 0xef,
	0xd7, 0x4a, 0xd6, 0x2b, 0x8b, 0x37, 0x4f, 0xa3, 0x69, 0xca, 0x39, 0x80, 0x32, 0xee, 0x4e, 0xb7,
	0xe9, 0xb6, 0x6b, 0xce, 0xc6, 0xa6, 0xeb, 0x3a, 0x78, 0x5b, 0x53, 0xf4, 0x7d, 0x56, 0x9a, 0x1a,
	0x57, 0x5d, 0xd7, 0x51, 0x7f, 0x80, 0xb2, 0x4c, 0xd4, 0x6c, 0x77, 0x2b, 0x65, 0x6c, 0x6b, 0x8a,
	0x3e, 0x69, 0xb1, 0xa5, 0xe7, 0x89, 0x51, 0xfd, 0x21, 0xca, 0x31, 0x59, 0x07, 0x74, 0x75, 0x4d,
	0xd1, 0x0b, 0x16, 0x5b, 0x7d, 0xb9, 0x19, 0x12, 0xf6, 0x40, 0xd8, 0xd0, 0x14, 0x3d, 0xc3, 0x85,
	0xeb, 0x20, 0x0c, 0x80, 0x4d, 0x03, 0x5f, 0xd1, 0x14, 0x3d, 0x25, 0x81, 0x4d, 0x23, 0x04, 0x36,
	0x0d, 0xdc, 0xd4, 0x14, 0x5d, 0x95, 0xc1, 0x01, 0x61, 0x0f, 0x84, 0x57, 0x35, 0x45, 0x9f, 0x90,
	0xc1, 0xa6, 0xa1, 0xfe, 0x08, 0xe5, 0x99, 0xb0, 0xde, 0xfc, 0xa3, 0xbd, 0x5d, 0x29, 0xe3, 0x6b,
	0x9a, 0xa2, 0x4f, 0x5b, 0xcc, 0xc1, 0x39, 0x30, 0xab, 0x3f, 0x46, 0x05, 0x0e, 0xa7, 0x5a, 0x47,
	0x53, 0xf4, 0x9c, 0xc5, 0x7c, 0x5c, 0xf6, 0xed, 0x52, 0x40, 0x75, 0xc7, 0xad, 0x75, 0x71, 0x4b,
	0x53, 0xf4, 0x24, 0x0f, 0xe8, 0x1c, 0x31, 0x86, 0xf1, 0xa6, 0x81, 0xdb, 0x9a, 0xa2, 0x4f, 0x05,
	0xf0, 0xa6, 0x11, 0x81, 0x37, 0x0d, 0xec, 0x6a, 0x8a, 0x9e, 0x0f, 0xe2, 0x03, 0xf1, 0x6f, 0xbb,
	0xbd, 0x4d, 0xc7, 0xc6, 0x3b, 0x9a, 0xa2, 0x2b, 0x3c, 0xfe, 0xb3, 0x9e, 0x55, 0xce, 0x68, 0x77,
	0xb7, 0xd9, 0x6e, 0xe0, 0x3f, 0x78, 0xb5, 0xc8, 0x33, 0xea, 0x59, 0xa5, 0x80, 0x36, 0xf7, 0xba,
	0x76, 0x07, 0xef, 0x6a, 0x8a, 0x9e, 0xe6, 0x01, 0xad, 0x12, 0xa3, 0xba, 0x8d, 0x8a, 0x4c, 0xb6,
	0x75, 0xa5, 0xe9, 0x6c, 0x6f, 0xd8, 0xed, 0x5e, 0x0b, 0x77, 0x34, 0x45, 0xcf, 0x96, 0x8d, 0xe1,
	0xeb, 0xb7, 0x74, 0x86, 0x2c, 0x5e, 0x6b, 0xf7, 0x5a, 0x16, 0x0b, 0x9b, 0x99, 0xd4, 0x16, 0x9a,
	0x0f, 0x50, 0x5a, 0xb0, 0x0c, 0x77, 0xbd, 0x8d, 0x72, 0x74, 0x54, 0x10, 0xdd, 0x35, 0xfb, 0x25,
	0x16, 0xdd, 0x3b, 0x9b, 0x68, 0x4e, 0x28, 0x3b, 0x6f, 0xfb, 0x42, 0x58, 0x3d, 0x2f, 0xac, 0xd2,
	0x10, 0x34, 0x7f, 0xd7, 0x7b, 0x01, 0x15, 0x79, 0xb1, 0x32, 0xa3, 0x7a, 0x0d, 0xe1, 0x10, 0x83,
	0x06, 0x75, 0xdd, 0x0b, 0x6a, 0x79, 0x78, 0x0c, 0x0d, 0x67, 0x3e, 0x40, 0xa2, 0x01, 0x1d, 0x44,
	0x99, 0x5d, 0x7b, 0xc7, 0xae, 0x75, 0xed, 0x6d, 0x68, 0x06, 0xb7, 0x14, 0x2d, 0x45, 0xba, 0x01,
	0xb5, 0x7a, 0xdd, 0xe0, 0x10, 0xca, 0x32, 0x15, 0x6c, 0xde, 0xb7, 0x89, 0x6c, 0xd2, 0x62, 0x8b,
	0xa1, 0x1d, 0xe8, 0x28, 0xc7, 0x74, 0x7e, 0x3b, 0x78, 0x87, 0x08, 0x0b, 0x16, 0x5b, 0xef, 0xf7,
	0x03, 0x51, 0xe9, 0xf7, 0x83, 0x77, 0x89, 0x32, 0xc3, 0x95, 0x7e, 0x43, 0x08, 0xb0, 0x4d, 0x03,
	0xbf, 0x47, 0x84, 0x29, 0x89, 0x6d, 0x1a, 0x21, 0xb6, 0x69, 0xe0, 0xf7, 0x89, 0x50, 0x95, 0xd9,
	0x01, 0xa5, 0xdf, 0x12, 0x3e, 0x20, 0xca, 0x09, 0x99, 0x6d, 0x1a, 0xea, 0x61, 0x94, 0x67, 0x4a,
	0xba, 0xcf, 0x3f, 0x24, 0xd2, 0x69, 0x8b, 0xb9, 0xa0, 0x4d, 0xe1, 0x27, 0xa8, 0xc0, 0xf9, 0x54,
	0xfc, 0x11, 0x11, 0xe7, 0x2c, 0xe6, 0x85, 0x75, 0x05, 0x31, 0x2a, 0xe8, 0x0a, 0x1f, 0x13, 0x69,
	0x92, 0x47, 0x05, 0x6d, 0x21, 0xf4, 0x0d, 0x4c, 0x03, 0x7f, 0x42, 0x94, 0x53, 0x81, 0x6f, 0x60,
	0x1a, 0x11, 0xdf, 0xc0, 0x34, 0xf0, 0xa7, 0x44, 0x9c, 0x0f, 0x7e, 0x83, 0x40, 0x16, 0xfc, 0xc6,
	0xf0, 0x19, 0xd1, 0x2a, 0x3c, 0x0b, 0x7e, 0x67, 0x90, 0x32, 0x0b, 0x9d, 0xe1, 0x73, 0xc5, 0x1b,
	0x4b, 0x3c, 0xb3, 0xd0, 0x1a, 0xc4, 0xa8, 0xa0, 0x35, 0x7c, 0x41, 0x84, 0x69, 0x1e, 0x15, 0xf4,
	0x06, 0x1b, 0x15, 0x99, 0x4e, 0xe8, 0x0d, 0x5f, 0x12, 0x71, 0xec, 0xe6, 0x40, 0x3d, 0xf2, 0xe6,
	0xd0, 0x46, 0xf3, 0x01, 0x0c, 0xdd, 0x47, 0x5f, 0x11, 0xd2, 0x38, 0xdd, 0x41, 0x82, 0xd1, 0xcd,
	0xb4, 0x85, 0xe6, 0x84, 0x12, 0x14, 0xba, 0xc3, 0xd7, 0x10, 0xd8, 0xc8, 0xed, 0x81, 0x17, 0x2e,
	0x6f, 0x0f, 0x0e, 0xc2, 0x21, 0x08, 0x0d, 0xeb, 0x1b, 0x08, 0x2b, 0x4e, 0x7f, 0x08, 0xa0, 0x68,
	0x48, 0xbf, 0x47, 0x99, 0x56, 0x6d, 0xc7, 0x6b, 0x0d, 0xd0, 0x1f, 0x1e, 0x4c, 0x7a, 0x88, 0x9f,
	0x8d, 0x90, 0xb9, 0x8b, 0xb5, 0x1d, 0xd2, 0x45, 0xc8, 0xbf, 0xb5, 0x76, 0x77, 0x77, 0xcf, 0x9a,
	0x6d, 0x71, 0x8b, 0xba, 0x85, 0xb2, 0x8c, 0x00, 0x8d, 0xe0, 0x21, 0x40, 0xfc, 0x7c, 0x74, 0x84,
	0xd7, 0x85, 0x80, 0x91, 0x6e, 0x09, 0x26, 0xb5, 0x8e, 0x72, 0x0c, 0xe2, 0x37, 0xa6, 0x87, 0x81,
	0xf2, 0x8b, 0xd1, 0x29, 0xd0, 0xc2, 0x00, 0x93, 0x69, 0x89, 0x36, 0x89, 0xe3, 0xb7, 0xb5, 0x47,
	0x62, 0x73, 0xd6, 0x23, 0x38, 0x7e, 0x53, 0x0c, 0x24, 0xcd, 0x34, 0xf0, 0xa3, 0xe3, 0x24, 0xcd,
	0x34, 0x42, 0x49, 0x33, 0x8d, 0x50, 0xd2, 0x4c, 0x03, 0x3f, 0x36, 0x56, 0xd2, 0x28, 0x46, 0x4c,
	0x5a, 0x80, 0xe3, 0xf7, 0xe3, 0xc7, 0xc7, 0x4a, 0x5a, 0x90, 0xe3, 0x77, 0xf3, 0x26, 0xca, 0x33,
	0x0e, 0x6d, 0xd0, 0x4f, 0x00, 0xe8, 0xe4, 0xe8, 0x20, 0xbf, 0xef, 0x03, 0x29, 0xdb, 0x92, 0x8c,
	0xaa, 0x83, 0x0a, 0x3c, 0x75, 0x94, 0xf5, 0x24, 0xb0, 0x4e, 0xc5, 0x48, 0x5e, 0x5d, 0x84, 0xe5,
	0x5a, 0xb2, 0x55, 0xaa, 0x06, 0x18, 0x26, 0x4f, 0xc5, 0xae, 0x06, 0x6f, 0xec, 0xc8, 0xd5, 0x00,
	0x93, 0x28, 0x94, 0x3d, 0xd3, 0xc0, 0x4f, 0x8f, 0x97, 0x3d, 0xfa, 0x9c, 0xa4, 0xec, 0x99, 0x46,
	0x44, 0xf6, 0x4c, 0x03, 0x3f, 0x33, 0x66, 0xf6, 0x28, 0x4c, 0xce, 0x5e, 0xa0, 0xfc, 0xfc, 0x41,
	0xf8, 0x6c, 0xec, 0xf2, 0x83, 0x91, 0x29, 0x97, 0x9f, 0x3f, 0x46, 0xa5, 0xed, 0x04, 0x63, 0xf4,
	0xb9, 0xf8, 0xdb, 0xc9, 0x73, 0x10, 0xd8, 0x4e, 0x30, 0x84, 0xc5, 0x6a, 0x80, 0x21, 0xfc, 0x7c,
	0xec, 0x6a, 0xf0, 0xc6, 0xb5, 0x5c, 0x0d, 0x30, 0xc1, 0x77, 0x50, 0x91, 0x41, 0x84, 0x09, 0xfe,
	0x02, 0x90, 0x4e, 0x8f, 0x4e, 0x62, 0x53, 0x1b, 0x68, 0xf9, 0x56, 0xc0, 0xac, 0xee, 0xa1, 0xf9,
	0x00, 0x91, 0x4e, 0xbd, 0x17, 0x01, 0x7a, 0x26, 0x26, 0xd4, 0xb7, 0x01, 0xb7, 0xd8, 0x0a, 0x7f,
	0xa2, 0x5e, 0x47, 0x73, 0x42, 0x23, 0x14, 0xe6, 0xfa, 0x4b, 0x40, 0x5e, 0x8d, 0xd3, 0x0e, 0xd9,
	0x44, 0x07, 0xb0, 0xda, 0x0a, 0x7d, 0xa0, 0xde, 0x40, 0x38, 0xc4, 0xa5, 0x41, 0xbf, 0x0c, 0xe8,
	0xb5, 0xd8, 0x68, 0x29, 0xec, 0xb9, 0x56, 0xd4, 0x67, 0xb4, 0x94, 0xbc, 0x99, 0x03, 0xe3, 0xff,
	0x95, 0x58, 0xa5, 0xe4, 0x0d, 0x61, 0x3e, 0xff, 0x49, 0x29, 0x31, 0x13, 0xdd, 0x17, 0x1d, 0x81,
	0xf2, 0x6a, 0xac, 0x7d, 0x01, 0x33, 0x98, 0x63, 0xc8, 0xbe, 0xe0, 0x36, 0xca, 0xe9, 0x09, 0x9c,
	0xd7, 0x62, 0x71, 0xd6, 0x23, 0x38, 0xdc, 0x26, 0x24, 0xcd, 0x34, 0x00, 0xf3, 0x7a, 0xdc, 0xa4,
	0x99, 0x46, 0x28, 0x69, 0x60, 0x12, 0x93, 0x46, 0x29, 0x6f, 0xc4, 0x4e, 0x9a, 0x88, 0xa1, 0x49,
	0x93, 0x39, 0x3d, 0x81, 0xf3, 0x66, 0xec, 0xa4, 0x05, 0x39, 0xdc, 0x46, 0xa7, 0x8b, 0x3f, 0xd1,
	0x00, 0x74, 0x33, 0xd6, 0x74, 0xf1, 0x47, 0x30, 0x27, 0x91, 0xa7, 0x21, 0x18, 0x59, 0xea, 0xbc,
	0x6e, 0x09, 0xa4, 0xb7, 0xe2, 0xa5, 0xce, 0xf3, 0x10, 0x48, 0x1d, 0xb3, 0xa9, 0x1a, 0x42, 0x6e,
	0xdb, 0x76, 0xeb, 0x80, 0xf8, 0x73, 0x4a, 0x53, 0xf4, 0x7d, 0xd5, 0x84, 0x35, 0xe3, 0x19, 0x3d,
	0xc5, 0x22, 0x9a, 0x05, 0x05, 0x9c, 0x14, 0xff, 0x42, 0x24, 0x93, 0xd5, 0x84, 0x05, 0xeb, 0xe0,
	0xe4, 0x7a, 0x10, 0xa5, 0x41, 0xe3, 0x1f, 0x5b, 0xff, 0x4a, 0x44, 0x85, 0x6a, 0xc2, 0x82, 0xa5,
	0xfe, 0xb9, 0x93, 0xa9, 0xfc, 0x43, 0xe7, 0xdf, 0x88, 0x2a, 0xc3, 0x54, 0xfe, 0xa9, 0x51, 0xe4,
	0x99, 0x06, 0xfe, 0x3b, 0x11, 0xa5, 0x44, 0x9e, 0x69, 0xc8, 0x3c, 0xd3, 0xc0, 0xff, 0x20, 0x22,
	0x55, 0xe2, 0x89, 0x2a, 0xff, 0xbc, 0xf6, 0x4f, 0xa2, 0x9a, 0x90, 0x78, 0xa6, 0xa1, 0x1e, 0x42,
	0x19, 0x50, 0xd1, 0x13, 0xd0, 0xbf, 0x88, 0x6c, 0xba, 0x9a, 0xb0, 0x60, 0x35, 0x3d, 0x2d, 0xe9,
	0x28, 0xeb, 0x33, 0xa9, 0xf0, 0xdf, 0x44, 0x98, 0xab, 0x26, 0x2c, 0x70, 0xc0, 0x4e, 0x3a, 0x2c,
	0x02, 0x38, 0xe6, 0xfc, 0x87, 0xc8, 0x92, 0x2c, 0x02, 0x38, 0xa8, 0xc8, 0x54, 0xd3, 0xc0, 0xff,
	0x25, 0xaa, 0x29, 0x99, 0xea, 0x5d, 0x80, 0x25, 0xaa, 0x69, 0xe0, 0xff, 0x11, 0x61, 0x3e, 0x40,
	0x15, 0xa3, 0xf5, 0x8f, 0x07, 0xff, 0x27, 0x3a, 0x85, 0x45, 0xeb, 0xcf, 0x77, 0x9e, 0x39, 0x18,
	0xee, 0x77, 0x10, 0xd5, 0x0c, 0xcf, 0x1c, 0x4c, 0x67, 0x16, 0x01, 0x8c, 0xe6, 0x3b, 0x89, 0x28,
	0xcd, 0x22, 0x80, 0xe1, 0x5a, 0x43, 0x79, 0xd0, 0x08, 0x93, 0xf5, 0xae, 0x54, 0xfc, 0x17, 0x67,
	0xd5, 0x84, 0x05, 0xa1, 0xf2, 0x69, 0x7a, 0x15, 0x15, 0x45, 0x04, 0x9d, 0x2a, 0x77, 0xa7, 0xc6,
	0x7a, 0x6b, 0x56, 0x4d, 0x58, 0x05, 0x0e, 0xa2, 0x53, 0x64, 0x03, 0xa9, 0xb4, 0xa4, 0x84, 0xd9,
	0x79, 0x4f, 0x2a, 0xce, 0x2b, 0xb3, 0x6a, 0xc2, 0xca, 0xfb, 0x85, 0xc8, 0xe7, 0xe4, 0x15, 0x34,
	0x27, 0x03, 0x68, 0x38, 0xf7, 0xa6, 0x62, 0xbe, 0x2f, 0xab, 0x26, 0xac, 0xa2, 0x88, 0xa1, 0xa1,
	0xb0, 0xda, 0x82, 0x67, 0xbc, 0x8c, 0xef, 0xa3, 0x0f, 0x39, 0x2d, 0x3c, 0xe4, 0xe5, 0xa0, 0xae,
	0x8c, 0xef, 0x8f, 0xd2, 0x95, 0x83, 0xba, 0x0a, 0x7e, 0x20, 0x4a, 0x57, 0x59, 0xb8, 0x8a, 0xd2,
	0x52, 0x4a, 0xbf, 0xc3, 0xff, 0x2b, 0x58, 0x38, 0x89, 0xf2, 0xc1, 0x1b, 0xbb, 0x9a, 0x47, 0xa9,
	0x6b, 0xf6, 0x9e, 0x07, 0xdc, 0x67, 0x91, 0x1f, 0xd5, 0xfd, 0x68, 0xf2, 0x7a, 0xcd, 0xe9, 0xd9,
	0x38, 0xe9, 0xd9, 0xe0, 0x97, 0x13, 0xc9, 0x63, 0xca, 0xc2, 0x29, 0x54, 0x08, 0x5d, 0xc7, 0x07,
	0x39, 0x98, 0x14, 0x1d, 0x9c, 0x46, 0x6a, 0xf8, 0xa6, 0x3d, 0xc8, 0x43, 0x21, 0xda, 0xc3, 0xfa,
	0xf0, 0x1e, 0x32, 0x7d, 0x83, 0xf0, 0xaf, 0x0e, 0x83, 0x1c, 0xa4, 0xfa, 0x07, 0x31, 0xa4, 0x07,
	0xb5, 0x7f, 0x10, 0x43, 0x7a, 0x98, 0x10, 0x3d, 0xac, 0xa0, 0x62, 0xc4, 0x65, 0x75, 0x90, 0x8b,
	0x69, 0xd1, 0xc5, 0x2a, 0xda, 0x1f, 0x75, 0x07, 0x1d, 0xe4, 0x23, 0x17, 0x9d, 0x4b, 0x7e, 0xb9,
	0x1c, 0xe4, 0x20, 0x79, 0x9b, 0x38, 0x86, 0x4c, 0xc5, 0xd4, 0xed, 0xe2, 0x18, 0xd2, 0x47, 0x3e,
	0xfa, 0x81, 0x08, 0xb7, 0xbc, 0x41, 0x1e, 0x94, 0x3e, 0x45, 0xc1, 0xef, 0x6f, 0x83, 0x3c, 0xcc,
	0x44, 0xe7, 0x92, 0x5f, 0xcd, 0x06, 0x39, 0x48, 0x8b, 0x0e, 0xf6, 0xd0, 0x5c, 0xe4, 0x8d, 0x2b,
	0xc2, 0xc9, 0xaf, 0x44, 0x27, 0x71, 0x5f, 0xcb, 0x0a, 0xe8, 0x1b, 0x08, 0xf7, 0xbb, 0x77, 0x45,
	0xd0, 0x2f, 0x8a, 0xf4, 0x31, 0x5e, 0xd5, 0x0a, 0x5f, 0xa0, 0x87, 0xbe, 0xd7, 0xe7, 0xfa, 0x15,
	0xc1, 0x3f, 0x2b, 0x47, 0x3f, 0xea, 0xbb, 0x5b, 0x01, 0xfb, 0x27, 0xb4, 0xd0, 0xff, 0xea, 0x15,
	0x41, 0xfe, 0xa5, 0x1c, 0x79, 0x8c, 0xb7, 0xb9, 0xa1, 0x82, 0x91, 0x2f, 0x60, 0x22, 0x73, 0x72,
	0x50, 0x3b, 0x87, 0x9a, 0x0d, 0xdc, 0xad, 0x44, 0x0f, 0x85, 0xe1, 0x3c, 0xac, 0xf7, 0xf7, 0x90,
	0x19, 0x6e, 0xa4, 0xc8, 0x17, 0x22, 0xd1, 0x41, 0x6a, 0xf8, 0x20, 0xfa, 0x78, 0x50, 0x87, 0x0f,
	0xa2, 0x8f, 0x87, 0x89, 0x41, 0x1e, 0xa0, 0x8b, 0x05, 0xaf, 0x27, 0xa2, 0x8b, 0xe9, 0x21, 0xc3,
	0x90, 0xef, 0x1d, 0xa2, 0x87, 0x99, 0x01, 0x1e, 0x16, 0x4b, 0x68, 0x86, 0x1f, 0x02, 0x67, 0xd0,
	0xe4, 0xca, 0x85, 0x4b, 0xd5, 0x95, 0x7c, 0x82, 0xfc, 0xb8, 0x6a, 0xad, 0xfc, 0xf6, 0x37, 0x79,
	0x45, 0x9d, 0x45, 0xd3, 0x67, 0xaa, 0x2b, 0xd6, 0x85, 0xf3, 0x6b, 0xf9, 0xe4, 0x6a, 0x86, 0x1e,
	0x57, 0x7b, 0xed, 0xa6, 0xdb, 0x3e, 0xbc, 0x8c, 0x66, 0xc5, 0x83, 0x57, 0x94, 0x03, 0xa4, 0xa6,
	0xb9, 0x83, 0x5b, 0xca, 0xea, 0xa5, 0xdf, 0xfd, 0x3a, 0x54, 0xbf, 0x4b, 0x5e, 0xfd, 0x6e, 0xf6,
	0xea, 0x4b, 0xcd, 0x76, 0xd7, 0xde, 0x6d, 0xd7, 0x1c, 0xef, 0xaf, 0x27, 0x3c, 0x6b, 0x67, 0xc9,
	0xb1, 0x1b, 0xb5, 0xad, 0xbd, 0xa5, 0x7e, 0x7f, 0x68, 0xb1, 0x39, 0x05, 0x9f, 0x7c, 0x1b, 0x00,
	0x00, 0xff, 0xff, 0x57, 0xd9, 0x0f, 0xc0, 0x8b, 0x21, 0x00, 0x00,
}
