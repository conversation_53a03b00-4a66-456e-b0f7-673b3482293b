package com.fuck.fuckinggooo.model

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface ProxyNodeDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(nodes: List<ProxyNode>)

    @Query("SELECT * FROM proxy_nodes")
    fun getAll(): kotlinx.coroutines.flow.Flow<List<ProxyNode>>

    @Query("SELECT * FROM proxy_nodes WHERE id = :id")
    fun getById(id: String): kotlinx.coroutines.flow.Flow<ProxyNode?>

    @Query("DELETE FROM proxy_nodes")
    suspend fun deleteAll()
}